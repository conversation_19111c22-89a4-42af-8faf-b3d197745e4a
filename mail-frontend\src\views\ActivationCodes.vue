<template>
  <div class="activation-codes">
    <div class="page-header">
      <div class="header-left">
        <h1>激活码管理</h1>
        <p>管理系统中的所有激活码</p>
      </div>
      <div class="header-right">
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <n-icon>
              <AddIcon />
            </n-icon>
          </template>
          创建激活码
        </n-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <n-card class="filter-card">
      <div class="filter-row">
        <n-input
          v-model:value="searchQuery"
          placeholder="搜索激活码..."
          clearable
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon>
              <SearchIcon />
            </n-icon>
          </template>
        </n-input>

        <n-select
          v-model:value="statusFilter"
          placeholder="状态筛选"
          :options="statusOptions"
          clearable
          style="width: 150px"
          @update:value="handleFilter"
        />

        <n-button @click="refreshData">
          <template #icon>
            <n-icon>
              <RefreshIcon />
            </n-icon>
          </template>
          刷新
        </n-button>

        <n-button type="error" :disabled="selectedRowKeys.length === 0" @click="handleBatchDelete">
          <template #icon>
            <n-icon>
              <TrashIcon />
            </n-icon>
          </template>
          批量删除
        </n-button>
      </div>
    </n-card>

    <!-- 数据表格 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.id"
        v-model:checked-row-keys="selectedRowKeys"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 创建激活码模态框 -->
    <n-modal v-model:show="showCreateModal" preset="dialog" title="创建激活码">
      <template #default>
        <n-form
          ref="createFormRef"
          :model="createForm"
          :rules="createFormRules"
          label-placement="left"
          label-width="auto"
        >
          <n-form-item label="数量" path="count">
            <n-input-number
              v-model:value="createForm.count"
              :min="1"
              :max="1000"
              placeholder="请输入创建数量"
              style="width: 100%"
            />
          </n-form-item>

          <n-form-item label="有效期(天)" path="expiry_days">
            <n-input-number
              v-model:value="createForm.expiry_days"
              :min="1"
              :max="365"
              placeholder="请输入有效期天数"
              style="width: 100%"
            />
          </n-form-item>

          <n-form-item label="备注" path="description">
            <n-input
              v-model:value="createForm.description"
              type="textarea"
              placeholder="可选的备注信息"
              :rows="3"
            />
          </n-form-item>
        </n-form>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showCreateModal = false">取消</n-button>
          <n-button type="primary" :loading="createLoading" @click="handleCreate"> 创建 </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NInput,
  NSelect,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NInputNumber,
  NSpace,
  NTag,
  NPopconfirm,
  useMessage,
  type DataTableColumns,
  type FormInst,
  type FormRules,
} from 'naive-ui'
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Trash as TrashIcon,
  Copy as CopyIcon,
  Eye as EyeIcon,
} from '@vicons/ionicons5'
import { activationApi } from '@/api/activation'
import type { ActivationCode } from '@/types/api'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateModal = ref(false)
const searchQuery = ref('')
const statusFilter = ref<string | null>(null)
const selectedRowKeys = ref<string[]>([])
const tableData = ref<ActivationCode[]>([])

// 表单引用
const createFormRef = ref<FormInst | null>(null)

// 创建表单数据
const createForm = reactive({
  count: 10,
  expiry_days: 30,
  description: '',
})

// 创建表单验证规则
const createFormRules: FormRules = {
  count: [
    {
      required: true,
      type: 'number',
      message: '请输入创建数量',
      trigger: ['input', 'blur'],
    },
    {
      type: 'number',
      min: 1,
      max: 1000,
      message: '数量应在1-1000之间',
      trigger: ['input', 'blur'],
    },
  ],
  expiry_days: [
    {
      required: true,
      type: 'number',
      message: '请输入有效期天数',
      trigger: ['input', 'blur'],
    },
    {
      type: 'number',
      min: 1,
      max: 365,
      message: '有效期应在1-365天之间',
      trigger: ['input', 'blur'],
    },
  ],
}

// 状态筛选选项
const statusOptions = [
  { label: '未使用', value: 'unused' },
  { label: '已使用', value: 'used' },
  { label: '已过期', value: 'expired' },
]

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
})

// 表格列配置
const columns = computed<DataTableColumns<ActivationCode>>(() => [
  {
    type: 'selection',
  },
  {
    title: '激活码',
    key: 'code',
    width: 200,
    render: row => {
      return h('div', { class: 'code-cell' }, [
        h('code', { class: 'activation-code' }, row.code),
        h(
          NButton,
          {
            size: 'tiny',
            quaternary: true,
            onClick: () => copyToClipboard(row.code),
          },
          {
            icon: () => h(NIcon, null, { default: () => h(CopyIcon) }),
          }
        ),
      ])
    },
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: row => {
      const statusMap = {
        active: { label: '活跃', type: 'success' as const },
        unused: { label: '未使用', type: 'success' as const },
        used: { label: '已使用', type: 'info' as const },
        expired: { label: '已过期', type: 'error' as const },
      }
      const status = statusMap[row.status as keyof typeof statusMap] || {
        label: row.status,
        type: 'default' as const,
      }
      return h(NTag, { type: status.type }, { default: () => status.label })
    },
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 180,
    render: row => {
      try {
        return row.createdAt ? new Date(row.createdAt).toLocaleString() : '-'
      } catch (error) {
        console.warn('Invalid createdAt date:', row.createdAt, error)
        return '-'
      }
    },
  },
  {
    title: '使用时间',
    key: 'usedAt',
    width: 180,
    render: row => {
      try {
        return row.usedAt ? new Date(row.usedAt).toLocaleString() : '-'
      } catch (error) {
        console.warn('Invalid usedAt date:', row.usedAt, error)
        return '-'
      }
    },
  },
  {
    title: '过期时间',
    key: 'expiresAt',
    width: 180,
    render: row => {
      try {
        return row.expiresAt ? new Date(row.expiresAt).toLocaleString() : '-'
      } catch (error) {
        console.warn('Invalid expiresAt date:', row.expiresAt, error)
        return '-'
      }
    },
  },
  {
    title: '设备指纹',
    key: 'deviceFingerprint',
    width: 150,
    render: row => {
      const fingerprint = row.deviceFingerprint || row.device_fingerprint || ''
      return fingerprint ? fingerprint.substring(0, 12) + '...' : '-'
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: row => {
      return h('div', { class: 'action-buttons' }, [
        h(
          NButton,
          {
            size: 'small',
            quaternary: true,
            onClick: () => viewDetails(row),
          },
          {
            icon: () => h(NIcon, null, { default: () => h(EyeIcon) }),
          }
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => deleteActivationCode(String(row.id)),
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  size: 'small',
                  quaternary: true,
                  type: 'error',
                },
                {
                  icon: () => h(NIcon, null, { default: () => h(TrashIcon) }),
                }
              ),
            default: () => '确定删除这个激活码吗？',
          }
        ),
      ])
    },
  },
])

// 获取激活码列表
const fetchActivationCodes = async () => {
  try {
    loading.value = true
    const response = await activationApi.getActivationCodes({
      page: pagination.page,
      size: pagination.pageSize, // 后端使用的是 size 参数
      status: statusFilter.value || undefined,
      search: searchQuery.value || undefined,
    })

    if (response.success && response.data) {
      // 确保 items 存在且为数组，兼容大小写字段名
      const items = response.data.Items || response.data.items || []

      // 数据转换：处理后端蛇形命名到前端驼峰命名的转换
      tableData.value = items.map((item: any) => ({
        ...item,
        // 确保 ID 为字符串类型
        id: String(item.id),
        // 处理设备指纹字段
        deviceFingerprint: item.deviceFingerprint || item.device_fingerprint || '',
        // 处理时间字段
        createdAt: item.createdAt || item.created_at || '',
        usedAt: item.usedAt || item.used_at || null,
        expiresAt: item.expiresAt || item.expires_at || '',
        // 处理其他字段
        macAddress: item.macAddress || item.mac_address || '',
        batchId: item.batchId || item.batch_id || '',
        // 状态映射：将 unused 映射为 active
        status: item.status === 'unused' ? 'active' : item.status,
      }))

      // 更新分页信息，兼容大小写字段名
      pagination.itemCount = response.data.Total || response.data.total || 0

      console.log('Fetched activation codes:', tableData.value.length, 'items')
    } else {
      // 如果响应不成功，清空数据
      tableData.value = []
      pagination.itemCount = 0
      console.warn('API response not successful:', response)
    }
  } catch (error) {
    console.error('Failed to fetch activation codes:', error)
    message.error('获取激活码列表失败')
    // 出错时清空数据，避免显示旧数据
    tableData.value = []
    pagination.itemCount = 0
  } finally {
    loading.value = false
  }
}

// 创建激活码
const handleCreate = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    createLoading.value = true

    const response = await activationApi.createActivationCodes(createForm)
    if (response.success) {
      message.success(`成功创建 ${createForm.count} 个激活码`)
      showCreateModal.value = false
      await fetchActivationCodes()

      // 重置表单
      Object.assign(createForm, {
        count: 10,
        expiry_days: 30,
        description: '',
      })
    }
  } catch (error) {
    console.error('Failed to create activation codes:', error)
    message.error('创建激活码失败')
  } finally {
    createLoading.value = false
  }
}

// 删除激活码
const deleteActivationCode = async (id: string) => {
  try {
    const response = await activationApi.deleteActivationCode(id)
    if (response.success) {
      message.success('删除成功')
      await fetchActivationCodes()
    }
  } catch (error) {
    console.error('Failed to delete activation code:', error)
    message.error('删除失败')
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) return

  try {
    const response = await activationApi.batchDeleteActivationCodes(selectedRowKeys.value)
    if (response.success) {
      message.success(`成功删除 ${selectedRowKeys.value.length} 个激活码`)
      selectedRowKeys.value = []
      await fetchActivationCodes()
    }
  } catch (error) {
    console.error('Failed to batch delete activation codes:', error)
    message.error('批量删除失败')
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('已复制到剪贴板')
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    message.error('复制失败')
  }
}

// 查看详情
const viewDetails = (row: ActivationCode) => {
  console.log('View details:', row)
  // 这里可以打开详情模态框
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  fetchActivationCodes()
}

// 筛选处理
const handleFilter = () => {
  pagination.page = 1
  fetchActivationCodes()
}

// 刷新数据
const refreshData = () => {
  fetchActivationCodes()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  fetchActivationCodes()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchActivationCodes()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchActivationCodes()
})
</script>

<style scoped>
.activation-codes {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: var(--n-text-color-2);
}

.filter-card {
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.code-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.activation-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: var(--n-code-color);
  padding: 2px 6px;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-row > * {
    width: 100%;
  }
}
</style>
