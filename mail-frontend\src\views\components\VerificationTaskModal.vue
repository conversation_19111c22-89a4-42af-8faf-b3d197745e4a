<template>
  <n-modal v-model:show="visible" preset="dialog" title="启动验证任务" style="width: 600px">
    <div class="verification-task-modal">
      <n-alert type="info" class="mb-4">
        <template #header>验证任务说明</template>
        <div>
          <p>将对选中的邮箱账户进行登录验证，检查账户的有效性。</p>
          <p>验证过程将在后台异步执行，您可以在任务控制面板中查看进度。</p>
        </div>
      </n-alert>

      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="120px"
      >
        <n-form-item label="选中账户数">
          <n-tag type="info">{{ selectedAccountsCount }} 个账户</n-tag>
        </n-form-item>

        <n-form-item label="验证类型" path="verificationType">
          <n-select
            v-model:value="formData.verificationType"
            :options="verificationTypeOptions"
            placeholder="请选择验证类型"
          />
        </n-form-item>

        <n-form-item label="并发限制" path="concurrentLimit">
          <n-input-number
            v-model:value="formData.concurrentLimit"
            :min="1"
            :max="50"
            placeholder="同时验证的账户数量"
          />
          <template #feedback>
            <span class="text-gray-500">建议设置为 5-20，过高可能导致IP被封</span>
          </template>
        </n-form-item>

        <n-form-item label="重试次数" path="retryLimit">
          <n-input-number
            v-model:value="formData.retryLimit"
            :min="0"
            :max="10"
            placeholder="验证失败时的重试次数"
          />
        </n-form-item>

        <n-form-item label="超时设置">
          <n-input-number
            v-model:value="formData.timeoutSeconds"
            :min="10"
            :max="300"
            placeholder="单个账户验证超时时间（秒）"
          />
        </n-form-item>

        <n-form-item>
          <n-checkbox v-model:checked="formData.skipRecentVerified">
            跳过最近24小时内已验证的账户
          </n-checkbox>
        </n-form-item>

        <n-form-item>
          <n-checkbox v-model:checked="formData.updateAccountStatus">
            根据验证结果自动更新账户状态
          </n-checkbox>
        </n-form-item>
      </n-form>

      <!-- 预估信息 -->
      <n-card title="预估信息" size="small" class="mb-4">
        <n-descriptions :column="2" size="small">
          <n-descriptions-item label="预计耗时">
            {{ estimatedTime }}
          </n-descriptions-item>
          <n-descriptions-item label="并发数">
            {{ formData.concurrentLimit }}
          </n-descriptions-item>
          <n-descriptions-item label="总账户数">
            {{ selectedAccountsCount }}
          </n-descriptions-item>
          <n-descriptions-item label="验证类型">
            {{ getVerificationTypeLabel(formData.verificationType) }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <n-space justify="end">
          <n-button @click="visible = false">取消</n-button>
          <n-button
            type="primary"
            @click="startVerification"
            :loading="starting"
            :disabled="selectedAccountsCount === 0"
          >
            启动验证任务
          </n-button>
        </n-space>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NModal,
  NAlert,
  NForm,
  NFormItem,
  NSelect,
  NInputNumber,
  NCheckbox,
  NTag,
  NCard,
  NDescriptions,
  NDescriptionsItem,
  NSpace,
  NButton,
  useMessage,
} from 'naive-ui'
import { mailboxApi, type VerificationTaskRequest, type ExtendedAccount } from '@/api/mailbox'

interface Props {
  visible: boolean
  selectedAccounts: ExtendedAccount[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', result: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 响应式数据
const starting = ref(false)

const formData = reactive({
  verificationType: 'login',
  concurrentLimit: 10,
  retryLimit: 3,
  timeoutSeconds: 30,
  skipRecentVerified: true,
  updateAccountStatus: true,
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

const selectedAccountsCount = computed(() => props.selectedAccounts.length)

const estimatedTime = computed(() => {
  const totalAccounts = selectedAccountsCount.value
  const concurrent = formData.concurrentLimit
  const timePerAccount = formData.timeoutSeconds + 5 // 加上处理时间

  if (totalAccounts === 0) return '0分钟'

  const totalSeconds = Math.ceil(totalAccounts / concurrent) * timePerAccount
  const minutes = Math.ceil(totalSeconds / 60)

  if (minutes < 60) {
    return `约 ${minutes} 分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `约 ${hours} 小时 ${remainingMinutes} 分钟`
  }
})

// 选项数据
const verificationTypeOptions = [
  { label: '登录验证', value: 'login' },
  { label: '健康检查', value: 'health_check' },
  { label: '会话测试', value: 'session_test' },
]

// 表单验证规则
const rules = {
  verificationType: {
    required: true,
    message: '请选择验证类型',
    trigger: 'change',
  },
  concurrentLimit: {
    required: true,
    type: 'number' as const,
    min: 1,
    max: 50,
    message: '并发限制必须在1-50之间',
    trigger: 'blur',
  },
  retryLimit: {
    required: true,
    type: 'number' as const,
    min: 0,
    max: 10,
    message: '重试次数必须在0-10之间',
    trigger: 'blur',
  },
}

// 方法
const getVerificationTypeLabel = (type: string) => {
  const option = verificationTypeOptions.find(opt => opt.value === type)
  return option?.label || type
}

const startVerification = async () => {
  if (selectedAccountsCount.value === 0) {
    message.error('请先选择要验证的账户')
    return
  }

  starting.value = true
  try {
    const accountEmails = props.selectedAccounts.map(account => account.email)

    const request: VerificationTaskRequest = {
      account_emails: accountEmails,
      verification_type: formData.verificationType,
      concurrent_limit: formData.concurrentLimit,
      retry_limit: formData.retryLimit,
    }

    const response = await mailboxApi.startVerificationTask(request)
    console.log('验证任务响应:', JSON.stringify(response))
    const apiResponse = (response as any).data || response
    if (apiResponse.success) {
      message.success('验证任务已创建，正在后台执行')
      emit('success', apiResponse.data)
      visible.value = false
    } else {
      message.error(apiResponse.message || '创建验证任务失败')
    }
  } catch (error: any) {
    message.error(error.message || '创建验证任务失败')
    console.error(error)
  } finally {
    starting.value = false
  }
}

const resetForm = () => {
  formData.verificationType = 'login'
  formData.concurrentLimit = 10
  formData.retryLimit = 3
  formData.timeoutSeconds = 30
  formData.skipRecentVerified = true
  formData.updateAccountStatus = true
}

// 监听弹窗关闭，重置表单
watch(visible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.verification-task-modal {
  padding: 20px 0;
}

.modal-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>
