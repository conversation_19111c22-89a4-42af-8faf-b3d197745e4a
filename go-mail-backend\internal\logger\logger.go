package logger

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	LevelDebug LogLevel = iota
	LevelInfo
	LevelWarn
	LevelError
)

// String 返回日志级别字符串
func (l LogLevel) String() string {
	switch l {
	case LevelDebug:
		return "DEBUG"
	case LevelInfo:
		return "INFO"
	case LevelWarn:
		return "WARN"
	case LevelError:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level         LogLevel `json:"level"`
	Format        string   `json:"format"`         // "text", "json", or "logback"
	Output        string   `json:"output"`         // "stdout", "stderr", or file path
	MaxSize       int      `json:"max_size"`       // 最大文件大小(MB)
	MaxBackups    int      `json:"max_backups"`    // 最大备份文件数
	MaxAge        int      `json:"max_age"`        // 最大保留天数
	Compress      bool     `json:"compress"`       // 是否压缩备份文件
	AsyncWrite    bool     `json:"async_write"`    // 是否启用异步写入
	BufferSize    int      `json:"buffer_size"`    // 异步写入缓冲区大小
	FlushInterval int      `json:"flush_interval"` // 刷新间隔(秒)
	ShowCaller    bool     `json:"show_caller"`    // 是否显示调用者信息
	ShowThread    bool     `json:"show_thread"`    // 是否显示线程信息
}

// DefaultLoggerConfig 默认日志配置
func DefaultLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:         LevelInfo,
		Format:        "logback",
		Output:        "stdout",
		MaxSize:       100,
		MaxBackups:    5,
		MaxAge:        30,
		Compress:      true,
		AsyncWrite:    false,
		BufferSize:    1024,
		FlushInterval: 5,
		ShowCaller:    true,
		ShowThread:    true,
	}
}

// AsyncWriter 异步写入器
type AsyncWriter struct {
	output     *os.File
	buffer     chan []byte
	done       chan struct{}
	wg         sync.WaitGroup
	bufferSize int
	flushTimer *time.Timer
}

// NewAsyncWriter 创建异步写入器
func NewAsyncWriter(output *os.File, bufferSize int, flushInterval time.Duration) *AsyncWriter {
	aw := &AsyncWriter{
		output:     output,
		buffer:     make(chan []byte, bufferSize),
		done:       make(chan struct{}),
		bufferSize: bufferSize,
	}

	aw.wg.Add(1)
	go aw.writeLoop(flushInterval)

	return aw
}

// Write 实现 io.Writer 接口
func (aw *AsyncWriter) Write(p []byte) (n int, err error) {
	// 复制数据避免竞态条件
	data := make([]byte, len(p))
	copy(data, p)

	select {
	case aw.buffer <- data:
		return len(p), nil
	case <-aw.done:
		return 0, fmt.Errorf("async writer is closed")
	default:
		// 缓冲区满时，同步写入
		return aw.output.Write(p)
	}
}

// writeLoop 异步写入循环
func (aw *AsyncWriter) writeLoop(flushInterval time.Duration) {
	defer aw.wg.Done()

	ticker := time.NewTicker(flushInterval)
	defer ticker.Stop()

	for {
		select {
		case data := <-aw.buffer:
			aw.output.Write(data)
		case <-ticker.C:
			aw.output.Sync() // 定期刷新
		case <-aw.done:
			// 处理剩余的缓冲数据
			for {
				select {
				case data := <-aw.buffer:
					aw.output.Write(data)
				default:
					aw.output.Sync()
					return
				}
			}
		}
	}
}

// Close 关闭异步写入器
func (aw *AsyncWriter) Close() error {
	close(aw.done)
	aw.wg.Wait()
	return aw.output.Close()
}

// Logger 日志记录器
type Logger struct {
	*slog.Logger
	config      *LoggerConfig
	asyncWriter *AsyncWriter
}

// NewLogger 创建新的日志记录器
func NewLogger(config *LoggerConfig) (*Logger, error) {
	if config == nil {
		config = DefaultLoggerConfig()
	}

	// 创建处理器选项
	opts := &slog.HandlerOptions{
		Level: convertLogLevel(config.Level),
		ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
			// 根据格式类型自定义属性
			switch config.Format {
			case "logback":
				return formatLogbackStyle(a, config)
			default:
				// 自定义时间格式
				if a.Key == slog.TimeKey {
					a.Value = slog.StringValue(time.Now().Format("2006-01-02 15:04:05.000"))
				}
				// 添加调用者信息
				if a.Key == slog.SourceKey && config.ShowCaller {
					if source, ok := a.Value.Any().(*slog.Source); ok {
						// 简化文件路径
						source.File = filepath.Base(source.File)
						a.Value = slog.AnyValue(source)
					}
				}
			}
			return a
		},
		AddSource: config.ShowCaller,
	}

	// 创建输出目标
	var output *os.File
	var err error

	switch config.Output {
	case "stdout":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// 文件输出
		if err := os.MkdirAll(filepath.Dir(config.Output), 0755); err != nil {
			return nil, fmt.Errorf("创建日志目录失败: %w", err)
		}
		output, err = os.OpenFile(config.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			return nil, fmt.Errorf("打开日志文件失败: %w", err)
		}
	}

	// 创建写入器（支持异步写入）
	var writer interface {
		Write([]byte) (int, error)
	}
	var asyncWriter *AsyncWriter

	if config.AsyncWrite && output != os.Stdout && output != os.Stderr {
		// 只对文件输出启用异步写入
		flushInterval := time.Duration(config.FlushInterval) * time.Second
		asyncWriter = NewAsyncWriter(output, config.BufferSize, flushInterval)
		writer = asyncWriter
	} else {
		writer = output
	}

	// 创建处理器
	var handler slog.Handler
	switch config.Format {
	case "json":
		handler = slog.NewJSONHandler(writer, opts)
	case "logback":
		handler = NewLogbackHandler(writer, opts, config)
	default:
		handler = slog.NewTextHandler(writer, opts)
	}

	logger := slog.New(handler)

	return &Logger{
		Logger:      logger,
		config:      config,
		asyncWriter: asyncWriter,
	}, nil
}

// convertLogLevel 转换日志级别
func convertLogLevel(level LogLevel) slog.Level {
	switch level {
	case LevelDebug:
		return slog.LevelDebug
	case LevelInfo:
		return slog.LevelInfo
	case LevelWarn:
		return slog.LevelWarn
	case LevelError:
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// WithContext 添加上下文信息
func (l *Logger) WithContext(ctx context.Context) *Logger {
	// 从上下文中提取请求ID等信息
	if requestID := ctx.Value("request_id"); requestID != nil {
		return &Logger{
			Logger: l.Logger.With("request_id", requestID),
			config: l.config,
		}
	}
	return l
}

// WithFields 添加字段
func (l *Logger) WithFields(fields map[string]any) *Logger {
	args := make([]any, 0, len(fields)*2)
	for k, v := range fields {
		args = append(args, k, v)
	}
	return &Logger{
		Logger: l.Logger.With(args...),
		config: l.config,
	}
}

// WithError 添加错误信息
func (l *Logger) WithError(err error) *Logger {
	if err == nil {
		return l
	}
	return &Logger{
		Logger: l.Logger.With("error", err.Error()),
		config: l.config,
	}
}

// WithCaller 添加调用者信息
func (l *Logger) WithCaller() *Logger {
	_, file, line, ok := runtime.Caller(1)
	if !ok {
		return l
	}
	return &Logger{
		Logger: l.Logger.With(
			"caller", fmt.Sprintf("%s:%d", filepath.Base(file), line),
		),
		config: l.config,
	}
}

// LogRequest 记录HTTP请求
func (l *Logger) LogRequest(method, path, userAgent, clientIP string, statusCode int, duration time.Duration) {
	l.Info("HTTP请求",
		"method", method,
		"path", path,
		"status", statusCode,
		"duration", duration.String(),
		"user_agent", userAgent,
		"client_ip", clientIP,
	)
}

// LogError 记录错误
func (l *Logger) LogError(operation string, err error, fields ...any) {
	args := []any{"operation", operation, "error", err.Error()}
	args = append(args, fields...)
	l.Error("操作失败", args...)
}

// LogSuccess 记录成功操作
func (l *Logger) LogSuccess(operation string, fields ...any) {
	args := []any{"operation", operation}
	args = append(args, fields...)
	l.Info("操作成功", args...)
}

// LogAuth 记录认证事件
func (l *Logger) LogAuth(event, username, clientIP string, success bool, fields ...any) {
	args := []any{
		"event", event,
		"username", username,
		"client_ip", clientIP,
		"success", success,
	}
	args = append(args, fields...)

	if success {
		l.Info("认证事件", args...)
	} else {
		l.Warn("认证失败", args...)
	}
}

// LogMailbox 记录邮箱操作
func (l *Logger) LogMailbox(operation string, mailboxID int, address string, fields ...any) {
	args := []any{
		"operation", operation,
		"mailbox_id", mailboxID,
		"address", address,
	}
	args = append(args, fields...)
	l.Info("邮箱操作", args...)
}

// LogActivation 记录激活码操作
func (l *Logger) LogActivation(operation, code, deviceFingerprint string, fields ...any) {
	args := []any{
		"operation", operation,
		"code", code,
		"device_fingerprint", deviceFingerprint,
	}
	args = append(args, fields...)
	l.Info("激活码操作", args...)
}

// LogTask 记录定时任务
func (l *Logger) LogTask(taskName string, duration time.Duration, success bool, err error) {
	args := []any{
		"task", taskName,
		"duration", duration.String(),
		"success", success,
	}

	if err != nil {
		args = append(args, "error", err.Error())
		l.Error("定时任务执行失败", args...)
	} else {
		l.Info("定时任务执行完成", args...)
	}
}

// LogPerformance 记录性能指标
func (l *Logger) LogPerformance(operation string, duration time.Duration, fields ...any) {
	args := []any{
		"operation", operation,
		"duration", duration.String(),
	}
	args = append(args, fields...)

	// 根据耗时判断日志级别
	if duration > 5*time.Second {
		l.Warn("操作耗时过长", args...)
	} else if duration > 1*time.Second {
		l.Info("操作耗时", args...)
	} else {
		l.Debug("操作耗时", args...)
	}
}

// Close 关闭日志记录器
func (l *Logger) Close() error {
	if l.asyncWriter != nil {
		return l.asyncWriter.Close()
	}
	return nil
}

// GetConfig 获取配置
func (l *Logger) GetConfig() *LoggerConfig {
	return l.config
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) {
	l.config.Level = level
	// 注意：slog.Logger的级别在创建时设定，运行时无法修改
	// 如果需要动态修改级别，需要重新创建Logger
}

// 全局日志记录器
var defaultLogger *Logger

// InitDefaultLogger 初始化默认日志记录器
func InitDefaultLogger(config *LoggerConfig) error {
	logger, err := NewLogger(config)
	if err != nil {
		return err
	}
	defaultLogger = logger
	return nil
}

// GetDefaultLogger 获取默认日志记录器
func GetDefaultLogger() *Logger {
	if defaultLogger == nil {
		// 如果没有初始化，使用默认配置
		logger, _ := NewLogger(DefaultLoggerConfig())
		defaultLogger = logger
	}
	return defaultLogger
}

// 便捷函数
func Debug(msg string, args ...any) {
	GetDefaultLogger().Debug(msg, args...)
}

func Info(msg string, args ...any) {
	GetDefaultLogger().Info(msg, args...)
}

func Warn(msg string, args ...any) {
	GetDefaultLogger().Warn(msg, args...)
}

func Error(msg string, args ...any) {
	GetDefaultLogger().Error(msg, args...)
}

// formatLogbackStyle 格式化为 Logback 风格
func formatLogbackStyle(a slog.Attr, config *LoggerConfig) slog.Attr {
	switch a.Key {
	case slog.TimeKey:
		// Logback 时间格式: 2024-01-15 14:30:45.123
		a.Value = slog.StringValue(time.Now().Format("2006-01-02 15:04:05.000"))
	case slog.LevelKey:
		// 格式化日志级别
		level := a.Value.String()
		a.Value = slog.StringValue(strings.ToUpper(level))
	case slog.SourceKey:
		if config.ShowCaller {
			if source, ok := a.Value.Any().(*slog.Source); ok {
				// Logback 格式: ClassName.methodName(FileName:LineNumber)
				fileName := filepath.Base(source.File)
				a.Value = slog.StringValue(fmt.Sprintf("%s:%d", fileName, source.Line))
			}
		}
	}
	return a
}

// LogbackHandler Logback 风格的处理器
type LogbackHandler struct {
	handler slog.Handler
	config  *LoggerConfig
}

// NewLogbackHandler 创建 Logback 风格处理器
func NewLogbackHandler(w interface{ Write([]byte) (int, error) }, opts *slog.HandlerOptions, config *LoggerConfig) *LogbackHandler {
	return &LogbackHandler{
		handler: slog.NewTextHandler(w, opts),
		config:  config,
	}
}

// Enabled 实现 slog.Handler 接口
func (h *LogbackHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

// Handle 实现 slog.Handler 接口
func (h *LogbackHandler) Handle(ctx context.Context, r slog.Record) error {
	// 添加线程信息
	if h.config.ShowThread {
		r.Add("thread", fmt.Sprintf("goroutine-%d", runtime.NumGoroutine()))
	}

	return h.handler.Handle(ctx, r)
}

// WithAttrs 实现 slog.Handler 接口
func (h *LogbackHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &LogbackHandler{
		handler: h.handler.WithAttrs(attrs),
		config:  h.config,
	}
}

// WithGroup 实现 slog.Handler 接口
func (h *LogbackHandler) WithGroup(name string) slog.Handler {
	return &LogbackHandler{
		handler: h.handler.WithGroup(name),
		config:  h.config,
	}
}

// LogWithStack 记录带堆栈信息的错误日志
func (l *Logger) LogWithStack(level LogLevel, msg string, err error, args ...any) {
	// 获取堆栈信息
	stack := make([]byte, 4096)
	n := runtime.Stack(stack, false)
	stackTrace := string(stack[:n])

	// 添加堆栈信息到参数
	allArgs := append(args, "error", err.Error(), "stack_trace", stackTrace)

	switch level {
	case LevelError:
		l.Error(msg, allArgs...)
	case LevelWarn:
		l.Warn(msg, allArgs...)
	case LevelInfo:
		l.Info(msg, allArgs...)
	case LevelDebug:
		l.Debug(msg, allArgs...)
	}
}

// LogPanic 记录 panic 信息
func (l *Logger) LogPanic(recovered interface{}) {
	stack := make([]byte, 4096)
	n := runtime.Stack(stack, false)
	stackTrace := string(stack[:n])

	l.Error("Panic recovered",
		"panic", recovered,
		"stack_trace", stackTrace,
		"goroutine_count", runtime.NumGoroutine(),
	)
}
