package types

import (
	"net/http"
	"time"
)

// AccountStatus 账户状态枚举
type AccountStatus int

const (
	AccountStatusUnknown AccountStatus = iota
	AccountStatusActive
	AccountStatusInactive
	AccountStatusLocked
	AccountStatusExpired
)

func (s AccountStatus) String() string {
	switch s {
	case AccountStatusActive:
		return "active"
	case AccountStatusInactive:
		return "inactive"
	case AccountStatusLocked:
		return "locked"
	case AccountStatusExpired:
		return "expired"
	default:
		return "unknown"
	}
}

// SessionStatus 会话状态枚举
type SessionStatus int

const (
	SessionStatusUnknown SessionStatus = iota
	SessionStatusConnecting
	SessionStatusActive
	SessionStatusExpired
	SessionStatusError
)

func (s SessionStatus) String() string {
	switch s {
	case SessionStatusConnecting:
		return "connecting"
	case SessionStatusActive:
		return "active"
	case SessionStatusExpired:
		return "expired"
	case SessionStatusError:
		return "error"
	default:
		return "unknown"
	}
}

// ProxyType 代理类型枚举
type ProxyType int

const (
	ProxyTypeHTTP ProxyType = iota
	ProxyTypeSOCKS5
)

func (p ProxyType) String() string {
	switch p {
	case ProxyTypeHTTP:
		return "http"
	case ProxyTypeSOCKS5:
		return "socks5"
	default:
		return "unknown"
	}
}

// Account 账户信息结构
type Account struct {
	Username   string            `json:"username"`
	Password   string            `json:"password"`
	Status     AccountStatus     `json:"status"`
	LastLogin  time.Time         `json:"last_login"`
	LoginCount int               `json:"login_count"`
	Metadata   map[string]string `json:"metadata"`
}

// ProxyConfig 代理配置结构
type ProxyConfig struct {
	ID       string    `json:"id"`
	Type     ProxyType `json:"type"`
	Host     string    `json:"host"`
	Port     int       `json:"port"`
	Username string    `json:"username,omitempty"`
	Password string    `json:"password,omitempty"`
	Enabled  bool      `json:"enabled"`
}

// Session 会话信息结构
type Session struct {
	ID           string        `json:"id"`
	Account      Account       `json:"account"`
	Proxy        *ProxyConfig  `json:"proxy,omitempty"`
	JSessionID   string        `json:"jsession_id"`
	NavigatorSID string        `json:"navigator_sid"`
	FinalURL     string        `json:"final_url"` // 登录成功后的最终URL
	OTT          string        `json:"ott"`
	CreatedAt    time.Time     `json:"created_at"`
	LastUsed     time.Time     `json:"last_used"`
	Status       SessionStatus `json:"status"`
	Client       *http.Client  `json:"-"`
}

// LoginResult 登录结果结构
type LoginResult struct {
	SessionID    string    `json:"session_id"`
	JSessionID   string    `json:"jsession_id"`
	NavigatorSID string    `json:"navigator_sid"`
	RedirectURL  string    `json:"redirect_url"`
	Success      bool      `json:"success"`
	Message      string    `json:"message"`
	LoginTime    time.Time `json:"login_time"`
	// {{ AURA-X: Add - 添加cookies字段用于会话复制. Approval: 寸止(ID:**********). }}
	Cookies []*http.Cookie `json:"-"` // 不序列化到JSON，仅用于内部传递
}

// BatchResult 批量操作结果结构
type BatchResult struct {
	Total    int                     `json:"total"`
	Success  int                     `json:"success"`
	Failed   int                     `json:"failed"`
	Results  map[string]*LoginResult `json:"results"`
	Errors   map[string]error        `json:"errors"`
	Duration time.Duration           `json:"duration"`
}

// ManagerConfig 管理器配置结构
type ManagerConfig struct {
	MaxConcurrent       int           `json:"max_concurrent"`
	RequestTimeout      time.Duration `json:"request_timeout"`
	SessionTimeout      time.Duration `json:"session_timeout"`
	RetryAttempts       int           `json:"retry_attempts"`
	RetryDelay          time.Duration `json:"retry_delay"`
	ProxyRotation       bool          `json:"proxy_rotation"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	LogLevel            string        `json:"log_level"`
	UserAgent           string        `json:"user_agent"`
}

// DefaultManagerConfig 返回默认配置
func DefaultManagerConfig() *ManagerConfig {
	return &ManagerConfig{
		MaxConcurrent:       10,
		RequestTimeout:      30 * time.Second,
		SessionTimeout:      24 * time.Hour,
		RetryAttempts:       3,
		RetryDelay:          2 * time.Second,
		ProxyRotation:       true,
		HealthCheckInterval: 5 * time.Minute,
		LogLevel:            "info",
		UserAgent:           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
	}
}

// AccountInfo 账户信息摘要
type AccountInfo struct {
	Username   string        `json:"username"`
	Status     AccountStatus `json:"status"`
	LastLogin  time.Time     `json:"last_login"`
	LoginCount int           `json:"login_count"`
}

// ProxyStatus 代理状态信息
type ProxyStatus struct {
	ID           string    `json:"id"`
	Host         string    `json:"host"`
	Port         int       `json:"port"`
	Enabled      bool      `json:"enabled"`
	LastUsed     time.Time `json:"last_used"`
	ResponseTime int64     `json:"response_time_ms"`
	ErrorCount   int       `json:"error_count"`
}

// Statistics 统计信息
type Statistics struct {
	TotalAccounts    int           `json:"total_accounts"`
	ActiveSessions   int           `json:"active_sessions"`
	TotalLogins      int           `json:"total_logins"`
	SuccessfulLogins int           `json:"successful_logins"`
	FailedLogins     int           `json:"failed_logins"`
	AverageLoginTime time.Duration `json:"average_login_time"`
	Uptime           time.Duration `json:"uptime"`
}

// MailItem 单个邮件信息结构
type MailItem struct {
	MailID       string `json:"mail_id"`       // 邮件ID，如：tmai1752471723283315974
	FolderID     string `json:"folder_id"`     // 文件夹ID，如：tfol17a1170054692d20
	From         string `json:"from"`          // 发件人
	FromTitle    string `json:"from_title"`    // 发件人完整信息（title属性）
	Subject      string `json:"subject"`       // 邮件主题
	SubjectTitle string `json:"subject_title"` // 邮件主题完整信息（title属性）
	Date         string `json:"date"`          // 邮件日期
	IsNew        bool   `json:"is_new"`        // 是否为新邮件（未读）
	IsStarred    bool   `json:"is_starred"`    // 是否已标星
}

// MailList 邮件列表结构
type MailList struct {
	Items      []MailItem `json:"items"`       // 邮件列表
	Page       int        `json:"page"`        // 当前页码
	TotalCount int        `json:"total_count"` // 总邮件数（如果可获取）
	HasMore    bool       `json:"has_more"`    // 是否还有更多邮件
}

// MailContent 邮件内容结构
type MailContent struct {
	MailID      string            `json:"mail_id"`     // 邮件ID
	From        string            `json:"from"`        // 发件人
	To          string            `json:"to"`          // 收件人
	Subject     string            `json:"subject"`     // 主题
	Date        string            `json:"date"`        // 日期
	Body        string            `json:"body"`        // 邮件正文（HTML格式）
	TextBody    string            `json:"text_body"`   // 邮件正文（纯文本格式）
	Attachments []MailAttachment  `json:"attachments"` // 附件列表
	Headers     map[string]string `json:"headers"`     // 邮件头信息
}

// MailAttachment 邮件附件结构
type MailAttachment struct {
	ID       string `json:"id"`        // 附件ID
	Name     string `json:"name"`      // 附件名称
	Size     int64  `json:"size"`      // 附件大小（字节）
	MimeType string `json:"mime_type"` // MIME类型
	URL      string `json:"url"`       // 下载URL
}

// HealthReport 健康检查报告
type HealthReport struct {
	Timestamp     time.Time                `json:"timestamp"`
	OverallStatus string                   `json:"overall_status"`
	AccountStatus map[string]AccountStatus `json:"account_status"`
	SessionStatus map[string]SessionStatus `json:"session_status"`
	ProxyStatus   map[string]bool          `json:"proxy_status"`
	ErrorCount    int                      `json:"error_count"`
	WarningCount  int                      `json:"warning_count"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields"`
}

// LogFilter 日志过滤器
type LogFilter struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Level     string    `json:"level"`
	Username  string    `json:"username"`
	Limit     int       `json:"limit"`
}

// AliasEmail 别名邮箱信息
type AliasEmail struct {
	ID        string `json:"id"`         // data-row-id，用于删除操作
	Email     string `json:"email"`      // 完整邮箱地址
	LocalPart string `json:"local_part"` // 邮箱前缀部分
	Domain    string `json:"domain"`     // 域名部分
	IsDefault bool   `json:"is_default"` // 是否为默认发送地址
}

// DomainOption 域名选项
type DomainOption struct {
	Value    string `json:"value"`    // option的value值，如"option138"
	Domain   string `json:"domain"`   // 域名，如"post.com"
	Category string `json:"category"` // 分类，如"Hobbies"
}

// AliasEmailList 别名邮箱列表响应
type AliasEmailList struct {
	Aliases       []AliasEmail   `json:"aliases"`        // 别名邮箱列表
	DomainOptions []DomainOption `json:"domain_options"` // 可用域名选项
}

// CreateAliasRequest 创建别名邮箱请求
type CreateAliasRequest struct {
	LocalPart       string `json:"local_part"`       // 邮箱前缀
	DomainSelection string `json:"domain_selection"` // 域名选项ID
}

// CreateAliasResponse 创建别名邮箱响应
type CreateAliasResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 响应消息
	Email   string `json:"email"`   // 创建的邮箱地址（成功时）
}

// DeleteAliasRequest 删除别名邮箱请求
type DeleteAliasRequest struct {
	RowID string `json:"row_id"` // 别名邮箱的row ID
}

// DeleteAliasResponse 删除别名邮箱响应
type DeleteAliasResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 响应消息
}

// AliasPageInfo 别名邮箱页面信息
type AliasPageInfo struct {
	FocusedElementId string `json:"focused_element_id"` // Wicket-FocusedElementId
	IacToken         string `json:"iac_token"`          // iac_token cookie值
}

// DeleteConfirmInfo 删除确认信息
type DeleteConfirmInfo struct {
	ConfirmURL string `json:"confirm_url"`  // 确认删除的URL
	OkButtonId string `json:"ok_button_id"` // OK按钮的ID
	CancelURL  string `json:"cancel_url"`   // 取消删除的URL (可选)
	DialogText string `json:"dialog_text"`  // 对话框文本内容
}

// CreateAliasEmailRequest 创建别名邮箱请求（用于服务层）
type CreateAliasEmailRequest struct {
	AliasName    string `json:"alias_name"`
	DomainSuffix string `json:"domain_suffix"`
}
