package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// SystemController 系统管理控制器
type SystemController struct {
	adminAPI *AdminAPI
}

// NewSystemController 创建系统管理控制器
func NewSystemController(adminAPI *AdminAPI) *SystemController {
	return &SystemController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册系统管理路由
func (c *SystemController) RegisterRoutes(v1 *gin.RouterGroup) {
	systemGroup := v1.Group("/system")
	systemGroup.Use(middleware.JWTAuth(c.adminAPI.auth))
	{
		systemHandler := handlers.NewSystemHandler(c.adminAPI.database, c.adminAPI.scheduler)
		c.adminAPI.logger.Info("注册系统管理路由", "prefix", "/api/v1/system")
		
		systemGroup.POST("/cleanup", systemHandler.CleanupSystem)
		systemGroup.POST("/backup", systemHandler.BackupSystem)
		systemGroup.GET("/version", systemHandler.GetVersionInfo)
		
		c.adminAPI.logger.Info("系统管理路由注册完成", "routes", []string{
			"POST /api/v1/system/cleanup",
			"POST /api/v1/system/backup",
			"GET /api/v1/system/version",
		})
	}
}
