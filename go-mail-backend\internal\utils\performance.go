package utils

import (
	"context"
	"fmt"
	"log/slog"
	"runtime"
	"sync"
	"time"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	metrics map[string]*Metric
	mutex   sync.RWMutex
	logger  *slog.Logger
}

// Metric 性能指标
type Metric struct {
	Name         string
	Count        int64
	TotalTime    time.Duration
	MinTime      time.Duration
	MaxTime      time.Duration
	LastTime     time.Duration
	ErrorCount   int64
	LastUpdate   time.Time
	mutex        sync.RWMutex
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(logger *slog.Logger) *PerformanceMonitor {
	return &PerformanceMonitor{
		metrics: make(map[string]*Metric),
		logger:  logger,
	}
}

// StartTimer 开始计时
func (pm *PerformanceMonitor) StartTimer(name string) *Timer {
	return &Timer{
		name:    name,
		start:   time.Now(),
		monitor: pm,
	}
}

// Timer 计时器
type Timer struct {
	name    string
	start   time.Time
	monitor *PerformanceMonitor
}

// Stop 停止计时并记录
func (t *Timer) Stop() {
	duration := time.Since(t.start)
	t.monitor.Record(t.name, duration, false)
}

// StopWithError 停止计时并记录错误
func (t *Timer) StopWithError() {
	duration := time.Since(t.start)
	t.monitor.Record(t.name, duration, true)
}

// Record 记录性能指标
func (pm *PerformanceMonitor) Record(name string, duration time.Duration, isError bool) {
	pm.mutex.Lock()
	metric, exists := pm.metrics[name]
	if !exists {
		metric = &Metric{
			Name:       name,
			MinTime:    duration,
			MaxTime:    duration,
			LastUpdate: time.Now(),
		}
		pm.metrics[name] = metric
	}
	pm.mutex.Unlock()

	metric.mutex.Lock()
	defer metric.mutex.Unlock()

	metric.Count++
	metric.TotalTime += duration
	metric.LastTime = duration
	metric.LastUpdate = time.Now()

	if duration < metric.MinTime {
		metric.MinTime = duration
	}
	if duration > metric.MaxTime {
		metric.MaxTime = duration
	}

	if isError {
		metric.ErrorCount++
	}
}

// GetMetrics 获取所有指标
func (pm *PerformanceMonitor) GetMetrics() map[string]*Metric {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	result := make(map[string]*Metric)
	for name, metric := range pm.metrics {
		metric.mutex.RLock()
		result[name] = &Metric{
			Name:       metric.Name,
			Count:      metric.Count,
			TotalTime:  metric.TotalTime,
			MinTime:    metric.MinTime,
			MaxTime:    metric.MaxTime,
			LastTime:   metric.LastTime,
			ErrorCount: metric.ErrorCount,
			LastUpdate: metric.LastUpdate,
		}
		metric.mutex.RUnlock()
	}

	return result
}

// GetAverageTime 获取平均时间
func (m *Metric) GetAverageTime() time.Duration {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.Count == 0 {
		return 0
	}
	return m.TotalTime / time.Duration(m.Count)
}

// GetErrorRate 获取错误率
func (m *Metric) GetErrorRate() float64 {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.Count == 0 {
		return 0
	}
	return float64(m.ErrorCount) / float64(m.Count)
}

// LogMetrics 记录性能指标到日志
func (pm *PerformanceMonitor) LogMetrics() {
	metrics := pm.GetMetrics()
	
	for name, metric := range metrics {
		pm.logger.Info("性能指标",
			"name", name,
			"count", metric.Count,
			"avg_time", metric.GetAverageTime(),
			"min_time", metric.MinTime,
			"max_time", metric.MaxTime,
			"error_rate", fmt.Sprintf("%.2f%%", metric.GetErrorRate()*100),
		)
	}
}

// WorkerPool 工作池
type WorkerPool struct {
	workers    int
	jobQueue   chan Job
	workerPool chan chan Job
	quit       chan bool
	wg         sync.WaitGroup
}

// Job 工作任务
type Job struct {
	ID       string
	Function func() error
	Result   chan error
}

// NewWorkerPool 创建工作池
func NewWorkerPool(workers int, queueSize int) *WorkerPool {
	return &WorkerPool{
		workers:    workers,
		jobQueue:   make(chan Job, queueSize),
		workerPool: make(chan chan Job, workers),
		quit:       make(chan bool),
	}
}

// Start 启动工作池
func (wp *WorkerPool) Start() {
	for i := 0; i < wp.workers; i++ {
		worker := NewWorker(wp.workerPool, wp.quit)
		worker.Start()
	}

	go wp.dispatch()
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() {
	close(wp.quit)
	wp.wg.Wait()
}

// Submit 提交任务
func (wp *WorkerPool) Submit(job Job) {
	wp.jobQueue <- job
}

// dispatch 分发任务
func (wp *WorkerPool) dispatch() {
	for {
		select {
		case job := <-wp.jobQueue:
			go func(job Job) {
				jobChannel := <-wp.workerPool
				jobChannel <- job
			}(job)
		case <-wp.quit:
			return
		}
	}
}

// Worker 工作者
type Worker struct {
	workerPool chan chan Job
	jobChannel chan Job
	quit       chan bool
}

// NewWorker 创建工作者
func NewWorker(workerPool chan chan Job, quit chan bool) *Worker {
	return &Worker{
		workerPool: workerPool,
		jobChannel: make(chan Job),
		quit:       quit,
	}
}

// Start 启动工作者
func (w *Worker) Start() {
	go func() {
		for {
			w.workerPool <- w.jobChannel

			select {
			case job := <-w.jobChannel:
				err := job.Function()
				if job.Result != nil {
					job.Result <- err
				}
			case <-w.quit:
				return
			}
		}
	}()
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	maxFailures  int
	resetTimeout time.Duration
	failures     int
	lastFailTime time.Time
	state        CircuitState
	mutex        sync.RWMutex
}

// CircuitState 熔断器状态
type CircuitState int

const (
	StateClosed CircuitState = iota
	StateOpen
	StateHalfOpen
)

// NewCircuitBreaker 创建熔断器
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        StateClosed,
	}
}

// Execute 执行函数（带熔断保护）
func (cb *CircuitBreaker) Execute(fn func() error) error {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	// 检查是否可以执行
	if cb.state == StateOpen {
		if time.Since(cb.lastFailTime) > cb.resetTimeout {
			cb.state = StateHalfOpen
			cb.failures = 0
		} else {
			return fmt.Errorf("熔断器开启，拒绝执行")
		}
	}

	// 执行函数
	err := fn()
	
	if err != nil {
		cb.failures++
		cb.lastFailTime = time.Now()
		
		if cb.failures >= cb.maxFailures {
			cb.state = StateOpen
		}
		
		return err
	}

	// 成功执行，重置失败计数
	if cb.state == StateHalfOpen {
		cb.state = StateClosed
	}
	cb.failures = 0

	return nil
}

// GetState 获取熔断器状态
func (cb *CircuitBreaker) GetState() CircuitState {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// MemoryPool 内存池
type MemoryPool struct {
	pool sync.Pool
	size int
}

// NewMemoryPool 创建内存池
func NewMemoryPool(size int) *MemoryPool {
	return &MemoryPool{
		pool: sync.Pool{
			New: func() interface{} {
				return make([]byte, size)
			},
		},
		size: size,
	}
}

// Get 获取内存块
func (mp *MemoryPool) Get() []byte {
	return mp.pool.Get().([]byte)
}

// Put 归还内存块
func (mp *MemoryPool) Put(buf []byte) {
	if len(buf) == mp.size {
		mp.pool.Put(buf[:0])
	}
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	CPUUsage    float64
	MemoryUsage float64
	Goroutines  int
	Timestamp   time.Time
}

// GetSystemMetrics 获取系统指标
func GetSystemMetrics() *SystemMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return &SystemMetrics{
		MemoryUsage: float64(m.Alloc) / 1024 / 1024, // MB
		Goroutines:  runtime.NumGoroutine(),
		Timestamp:   time.Now(),
	}
}

// ContextWithTimeout 创建带超时的上下文
func ContextWithTimeout(parent context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	return context.WithTimeout(parent, timeout)
}

// RetryWithBackoff 带退避的重试
func RetryWithBackoff(fn func() error, maxRetries int, initialDelay time.Duration) error {
	var err error
	delay := initialDelay

	for i := 0; i < maxRetries; i++ {
		err = fn()
		if err == nil {
			return nil
		}

		if i < maxRetries-1 {
			time.Sleep(delay)
			delay *= 2 // 指数退避
		}
	}

	return fmt.Errorf("重试 %d 次后仍然失败: %w", maxRetries, err)
}
