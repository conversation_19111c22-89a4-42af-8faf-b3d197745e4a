# 项目上下文信息

# Go-Mail 企业级邮件登录管理系统

基于 Mail.com 登录协议的企业级邮件账户管理系统，支持批量登录、代理轮换、会话管理等功能。

## 🏗️ 架构设计

### 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Management Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  BatchManager   │  │  ProxyManager   │  │  ConfigManager  │ │
│  │  (批量管理)     │  │  (代理管理)     │  │  (配置管理)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Service Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  AccountPool    │  │  SessionPool    │  │  TaskScheduler  │ │
│  │  (账户池)       │  │  (会话池)       │  │  (任务调度)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Core Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  LoginClient    │  │  ProxyClient    │  │  StateManager   │ │
│  │  (登录客户端)   │  │  (代理客户端)   │  │  (状态管理)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```
```
go-mail/
├── internal/
│   ├── types/          # 完整的数据类型定义
│   ├── interfaces/     # 企业级接口设计
│   ├── errors/         # 完善的错误处理体系
│   ├── client/         # Mail.com登录客户端实现
│   ├── pool/           # 账户池、会话池、代理管理器
│   └── manager/        # 主管理器实现
├── example/            # 完整的使用示例
├── mail协议/           # 原始协议分析文档
├── go.mod             # Go模块配置
└── README.md          # 详细的项目文档

```

## 🚀 核心特性

### 企业级功能
- ✅ **批量操作**: 支持批量登录、登出、健康检查
- ✅ **代理支持**: HTTP/SOCKS5代理池管理和自动轮换
- ✅ **会话管理**: 自动会话创建、维护和清理
- ✅ **并发控制**: 可配置的并发登录数量限制
- ✅ **错误处理**: 完善的错误分类和重试机制
- ✅ **监控统计**: 实时状态监控和性能统计
- ✅ **配置管理**: 灵活的配置系统和热更新

### 安全特性
- ✅ **传输安全**: 强制HTTPS连接和证书验证
- ✅ **会话安全**: 安全Cookie处理和会话超时
- ✅ **数据保护**: 敏感信息内存清理和日志脱敏
- ✅ **访问控制**: 账户状态管理和权限控制

## 📁 项目结构

```
go-mail/
├── internal/
│   ├── types/          # 数据类型定义
│   ├── interfaces/     # 接口定义
│   ├── errors/         # 错误处理
│   ├── client/         # 登录客户端实现
│   ├── pool/           # 资源池管理
│   └── manager/        # 主管理器
├── example/            # 使用示例
├── mail协议/           # 协议分析文档
├── go.mod             # Go模块定义
└── README.md          # 项目说明
```
