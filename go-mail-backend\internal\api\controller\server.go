package api

import (
	"context"
	"fmt"
	"go-mail/internal/api/controller/admin"
	"go-mail/internal/api/controller/client"
	"go-mail/internal/api/controller/common"
	"go-mail/internal/api/middleware"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/manager"
	"go-mail/internal/scheduler"
	"go-mail/internal/services"
	"log/slog"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Server HTTP服务器结构
type Server struct {
	engine      *gin.Engine
	mailManager *manager.MailManager
	database    *database.Database
	services    *services.Services
	auth        *auth.Auth
	scheduler   *scheduler.Scheduler
	logger      *slog.Logger
	config      *ServerConfig
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	JWTSecret    string        `json:"jwt_secret"`
	AESKey       string        `json:"aes_key"`
	Debug        bool          `json:"debug"`
}

// DefaultServerConfig 默认服务器配置
func DefaultServerConfig() *ServerConfig {
	return &ServerConfig{
		Host:         "0.0.0.0",
		Port:         8080,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		JWTSecret:    "go-mail-jwt-secret-key",
		AESKey:       "go-mail-aes-key-32-bytes-long!!",
		Debug:        false,
	}
}

// NewServer 创建新的HTTP服务器
func NewServer(mailManager *manager.MailManager, database *database.Database, config *ServerConfig) *Server {
	if config == nil {
		config = DefaultServerConfig()
	}

	// 设置Gin模式
	if !config.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建认证服务
	authService := auth.NewAuth(config.JWTSecret, config.AESKey)

	// 创建业务服务
	servicesInstance := services.NewServices(mailManager, database, authService)

	// 创建任务调度器
	taskScheduler := scheduler.NewScheduler(slog.Default())

	// 添加定时任务
	taskScheduler.AddTask(scheduler.NewMailboxCleanupTask(servicesInstance.Mailbox, slog.Default()))
	taskScheduler.AddTask(scheduler.NewAccountHealthCheckTask(mailManager, database, slog.Default()))
	taskScheduler.AddTask(scheduler.NewSystemCleanupTask(database, slog.Default()))
	taskScheduler.AddTask(scheduler.NewStatisticsUpdateTask(database, slog.Default()))
	taskScheduler.AddTask(scheduler.NewSessionCleanupTask(mailManager, slog.Default()))

	// 添加批量操作清理任务（防止卡住的批量操作）
	taskScheduler.AddTask(scheduler.NewBatchOperationCleanupTask(database, slog.Default()))

	// 添加邮箱管理相关任务
	taskScheduler.AddTask(scheduler.NewMailboxVerificationTask(mailManager, database, servicesInstance.MailboxManagement, slog.Default()))
	taskScheduler.AddTask(scheduler.NewMailboxHealthMonitorTask(mailManager, database, slog.Default()))

	// 创建Gin引擎
	engine := gin.New()

	server := &Server{
		engine:      engine,
		mailManager: mailManager,
		database:    database,
		services:    servicesInstance,
		auth:        authService,
		scheduler:   taskScheduler,
		logger:      slog.Default(),
		config:      config,
	}

	// 设置路由
	server.setupRoutes()

	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 全局中间件
	s.engine.Use(middleware.PanicRecovery())        // Panic恢复中间件（必须在最前面）
	s.engine.Use(middleware.RequestID())            // 请求ID生成
	s.engine.Use(middleware.EnhancedErrorHandler()) // 增强错误处理和日志
	s.engine.Use(middleware.CORS())                 // 跨域处理
	// 注意：移除了默认的 gin.Logger() 和 gin.Recovery()，使用我们自定义的增强版本

	// API v1 路由组
	v1 := s.engine.Group("/api/v1")

	// 创建模块化API控制器
	adminAPI := admin.NewAdminAPI(s.services, s.auth, s.database, s.scheduler, s.logger)
	clientAPI := client.NewClientAPI(s.services, s.auth, s.database, s.logger)
	commonAPI := common.NewCommonAPI()

	// 注册各模块路由
	adminAPI.RegisterRoutes(v1)
	clientAPI.RegisterRoutes(v1)
	commonAPI.RegisterRoutes(s.engine)
}

// Start 启动HTTP服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	s.logger.Info("启动HTTP服务器",
		"host", s.config.Host,
		"port", s.config.Port,
		"debug", s.config.Debug)

	server := &http.Server{
		Addr:         addr,
		Handler:      s.engine,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
	}

	return server.ListenAndServe()
}

// StartWithContext 带上下文启动HTTP服务器
func (s *Server) StartWithContext(ctx context.Context) error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	server := &http.Server{
		Addr:         addr,
		Handler:      s.engine,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
	}

	// 启动任务调度器
	if err := s.scheduler.Start(ctx); err != nil {
		s.logger.Error("任务调度器启动失败", "error", err)
		return err
	}

	// 启动服务器
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP服务器启动失败", "error", err)
		}
	}()

	s.logger.Info("HTTP服务器已启动",
		"host", s.config.Host,
		"port", s.config.Port)

	// 等待上下文取消
	<-ctx.Done()

	// 优雅关闭
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	s.logger.Info("正在关闭HTTP服务器...")

	// 停止任务调度器
	s.scheduler.Stop()
	s.logger.Info("任务调度器已停止")

	if err := server.Shutdown(shutdownCtx); err != nil {
		s.logger.Error("HTTP服务器关闭失败", "error", err)
		return err
	}

	s.logger.Info("HTTP服务器已关闭")
	return nil
}

// GetEngine 获取Gin引擎（用于测试）
func (s *Server) GetEngine() *gin.Engine {
	return s.engine
}
