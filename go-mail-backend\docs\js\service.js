;(function() {
/*! AdService 2
 *
 *  @version 2.36.10
 *
 *  <AUTHOR> <<EMAIL>>
 *  <AUTHOR> <<EMAIL>>
 *  @see {@link https://mam-confluence.1and1.com/display/TAM/AdService+2}
 */

const Const = {
    REQUEST_METHOD: {
        NONFRIENDLY_IFRAME: 'NONFRIENDLY_IFRAME',
        FRIENDLY_IFRAME: 'FRIENDLY_IFRAME',
    },
    OVERRIDE_PARAM_PREFIX: '_ad.',
    LOGLEVEL_PARAM_PREFIX: '_ad_loglevel.',
    MESSAGE_TYPE: {
        CALL: 'CALL',
        ANSWER: 'ANSWER',
        DEMAND_REDO: 'DEMAND_REDO',
    },
    QUERYPARAMS: {},
};
const BOOLEAN = 'boolean';
const FUNCTION = 'function';
const NUMBER = 'number';
const OBJECT = 'object';
const STRING = 'string';
const UNDEFINED = 'undefined';

for (const [key, val] of new URLSearchParams(location.search).entries()) {
    Const.QUERYPARAMS[key] = val;
}

var log = function() {
    log.debug.apply(null, arguments);
};

(function() {
    var _coreLevels = {'false': true},
        _levels = {
            raw: false,
            debug: true,
            info: true,
            warn: true,
            error: true,
        _: 0};
    for (var i in _levels) {
        if (!_levels.hasOwnProperty(i) || i==='_') { continue; }
        _coreLevels[i] = true;
    }
    function prependToArguments(args) {
        var result = Array.prototype.slice.call(args);
        result.unshift.apply(result, Array.prototype.slice.call(arguments, 1));
        return result;
    }
    function appendToArguments(args) {
        var result = Array.prototype.slice.call(args);
        result.push.apply(result, Array.prototype.slice.call(arguments, 1));
        return result;
    }
    function pad(value, len) {
        value = ''+value;
        len = len || 2;
        while (value.length < len) {
            value = '0' + value;
        }
        return value;
    }
    function now() {
        const now = new Date();
        return pad(now.getHours())
            + ':' + pad(now.getMinutes())
            + ':' + pad(now.getSeconds())
            + '.' + pad(now.getMilliseconds(), 3);
    }

    log._output = function() {
        if (!arguments[0].doit) { return; }
        var data = prependToArguments(Array.prototype.slice.call(arguments, 1), '[AdService:'+arguments[0].title+']' + ' '+now()),
            method = console[arguments[0].method || arguments[0].title] || console.log;
        if ('apply' in method) {
            method.apply(console, data);
        } else {
            method(data.join(' '));
        }
    };
    log.consoleDebug = function() {
        log._output.apply(null, prependToArguments(arguments, {doit: _levels.debug && debugMode, title: 'raw', method: 'debug'}));
    };
    log.debug = function() {
        log._output.apply(null, prependToArguments(arguments, {doit: _levels.debug && debugMode, title: 'debug', method: 'log'}));
    };
    log.info = function() {
        log._output.apply(null, prependToArguments(arguments, {doit: _levels.info && debugMode, title: 'info'}));
    };
    log.warn = function() {
        log._output.apply(null, prependToArguments(arguments, {doit: _levels.warn && debugMode, title: 'warn'}));
    };
    log.error = function() {
        var stack = 'Error\n' + ((''+(new Error()).stack).split('\n').splice(2).join('\n') || '');
        log._output.apply(null, prependToArguments(arguments, {doit: _levels.error && debugMode, title: 'error'}, stack));
    };
    log.level = function(level) {
        log._output.apply(null, prependToArguments(Array.prototype.slice.call(arguments, 1), {doit: _levels[level] && debugMode, title: level}));
    };
    log.remind = function() {
        log._output.apply(null, appendToArguments(prependToArguments(arguments, {doit: true, title: 'TODO', method: 'warn'}, '[REMINDER]'), '\n'+(''+(new Error()).stack).split('\n').slice(2).join('\n')));
    };
    log.deprecated = function() {
        log._output.apply(null, prependToArguments(arguments, {doit: true, title: 'deprecated', method: 'warn'}));
    };
    log.nyi = function() {
        log._output.apply(null, prependToArguments(arguments, {doit: true, title: 'interface', method: 'error'}, 'NOT YET IMPLEMENTED:'));
    };
    log.setLevel = function(level, isSet) {
        if (typeof level === OBJECT) {
            for (var i in level) {
                if (!level.hasOwnProperty(i)) { continue; }
                if (i === '_') { continue; }
                _levels[i] = level[i];
            }
        } else {
            if (_coreLevels[level]) {
                var value = false;
                for (var i in _levels) {
                    if (!_levels.hasOwnProperty(i)) { continue; }
                    if (i === level) { value = true; }
                    if (i === '_') { break; }
                    _levels[i] = value;
                }
            }
            _levels[level] = isSet !== false;
        }
    };

    const loglevels = Object.keys(Const.QUERYPARAMS).filter((key) => key.startsWith(Const.LOGLEVEL_PARAM_PREFIX));
    for (const loglevel of loglevels) {
        log.setLevel(loglevel.substring(Const.LOGLEVEL_PARAM_PREFIX.length), Const.QUERYPARAMS[loglevel] !== 'false');
    }
})();

const securedSymbol = typeof Symbol === FUNCTION ? Symbol('secured') : '_'+parseInt((''+Math.random()).substring(2)).toString(16);

var cookieLabel;

(function() {
    if ('cookieDeprecationLabel' in navigator) {
        navigator.cookieDeprecationLabel.getValue().then((label) => {
            if (label && /\w+\d+\.?\d?/.test(label)) {
                cookieLabel = label;
            }
        });
    }
})();

var debugMode = false,
    debugOrigin = [];
if (window.document.documentElement.getAttribute('data-adservice-debug') !== null) {
    debugMode = (''+window.document.documentElement.getAttribute('data-adservice-debug')).toLowerCase() === 'true';
    if (debugMode) { debugOrigin.push('html attribute'); }
}
if (window.adserviceDebug) {
    debugMode = !!window.adserviceDebug;
    if (debugMode) { debugOrigin.push('global variable'); }
}
if (Const.QUERYPARAMS['adservice-debug'] === 'true' || Const.QUERYPARAMS['adservice-debug'] === 'false') {
    debugMode = Const.QUERYPARAMS['adservice-debug'] === 'true';
    if (debugMode) { debugOrigin.push('url parameter'); }
}

const setDebugMode = function(yesno) {
    debugMode = typeof yesno === BOOLEAN ? yesno : debugMode;
    if (debugMode) {
        log.info('Entering Debug Mode. Reason(s):', debugOrigin.length ? debugOrigin : Array.from((''+(new Error()).stack).split('\n').slice(2)).concat('manual call'));
    }
    debugOrigin = [];
};
setDebugMode(debugMode);

const each = function(obj, func, completeWalkthrough) {
    if (obj instanceof Array || isArguments(obj) || obj instanceof window.HTMLCollection) {
        for (var i=0, l=obj.length; i<l; i++) {
            try { func(i, obj[i]); }
            catch (e) {
                log.error('Error in function supplied for each(Array)', e, i, obj[i], arguments, this);
            }
        }
    } else {
        for (var i in obj) {
            if (completeWalkthrough !== true && !obj.hasOwnProperty(i)) { continue; }
            try { func(i, obj[i]); }
            catch (e) {
                log.error('Error in function supplied for each(Array)', e, i, obj[i], arguments, this);
            }
        }
    }
};

const keys = function(obj) {
    var keys = [];
    each(obj, function(key, value) { keys.push(key); });
    return keys;
};

const deepCopy = function(obj) {
    switch (true) {
        case obj === undefined:
        case obj === null:
        case !(obj instanceof Object):
            return obj;
        case obj.constructor === Number:
        case obj.constructor === String:
        case obj.constructor === Boolean:
        case obj.constructor === Date:
        case obj.constructor === RegExp:
        case obj.constructor === Function:
            return new obj.constructor(obj);
        case obj instanceof Array:
            var temp = [];
            for (var i=0, l=obj.length; i<l; i++) {
                temp[i] = deepCopy(obj[i]);
            }
            return temp;
        default:
            return extend({}, obj);
    }
};

const makeArray = function(obj, forcePlainObjectToArray) {
    switch (true) {
        case (obj === undefined):
            return [];
        case (obj instanceof Array):
            return obj;
        case (isArguments(obj)):
        case (obj instanceof window.NodeList):
            var temp = [];
            each(obj, function(i, item) { temp[i] = item; });
            return temp;
        case (obj instanceof Object && forcePlainObjectToArray):
            var temp = [];
            each(obj, function(i, item) { temp[i] = item; });
            return temp.length ? temp : [obj];
        default:
            return [obj];
    }
    return obj;
};

const isArguments = function(obj) {
    return obj && Object.prototype.toString.call(obj) == '[object Arguments]';
};

const extend = function() {
    function _extend(object1, object2) {
        each(object2, function(key, value) {
            if (typeof value === OBJECT && !(value instanceof Array)) {
                if (typeof object1[key] === UNDEFINED) {
                    object1[key] = value;
                } else {
                    _extend(object1[key], value);
                }
            } else {
                object1[key] = value;
            }
        }, (object2 instanceof (window.DOMRect || window.ClientRect)));
        return object1;
    }

    var result = arguments[0];
    for (var i=1, l=arguments.length; i<l; i++) {
        result = _extend(result, arguments[i] || {});
    }
    return result;
};

const getLengthValue = function(value) {
    if (typeof value === NUMBER) { return value + 'px'; }
    if ((''+value).indexOf('%') > 0) { return parseInt(value) + '%'; }
    return parseInt(value) + 'px';
};

const getCookieValue = function(cookie, flags) {
    cookie = String(cookie).replace(/[\.\+\-\*\^\$\|]/g, '\\$&'); 
    try {
        return new RegExp('(?:^|; )' + cookie + '=([^;]*)', flags).exec(document.cookie)?.[1] || '';
    } catch (e) { }
    return '';
};

const parseUrl = function(url) {
    const parts = /^((https?:)?\/\/)?(((?:[\w-]{2,}\.)+[a-z]{2,9}|localhost)(?::(\d+))?)?((?:\/[\w\-\.]+)*\/?)(\?[^#]+)?(#.+)?$/.exec(url);
    if (!parts) { return {}; }
    const result = {
        hash: parts[8] || '',
        host: parts[3] || '',
        hostname: parts[4] || '',
        href: parts[0] || '',
        origin: parts[1]+parts[3] || '',
        pathname: parts[6] || '/',
        port: parts[5] || '',
        protocol: parts[2] || '',
        search: parts[7] || '',
    };
    return result;
};

const isAcceptedHost = function(host) {
    host = parseUrl(host).hostname || host;
    return /(^|\.)(web\.de|(gmx\.(net|at|ch|com|co\.uk|es|fr))|mail\.com|1und1\.de|localhost)$/.test(host);
};

const generatePageId = function() {
    return (Math.random().toString().substring(2, 11));
};

const loadJsonp = function(url, isModule = false) {
    var head = document.getElementsByTagName('head')[0],
        script = document.createElement('script');
    script.src = url;
    if (isModule) {
        script.setAttribute('type', 'module');
    }
    head.appendChild(script);
    return self;
};

const globalHelpers = {
    extend: extend, 
    setDebugMode: setDebugMode, 
};




var precalls = window.AdService instanceof Array ? window.AdService.slice() : [];
window.AdService = new function() {
    var self = this,
        win  = window,
        doc  = win.document,
        loc  = win.location,
    _;
    self.Helper = globalHelpers;
    (function() {
        self._global = {};
    
        self._runOutsourceable = function(method, thisContext, args) {
            var func = self._global[method];
            for (var i=3, l=arguments.length; i<l; i++) {
                if (arguments[i]._global && arguments[i]._global[method]) {
                    func = arguments[i]._global[method];
                    break;
                }
            }
            if (typeof func !== FUNCTION) {
                log.error('Outsourceable function', method, 'is not defined.');
            }
            return func ? func.apply(func.thisContext || thisContext, args) : undefined;
        };
    }).apply(this);

    (function() {
        var version = '2.36.10'.split('.');
        self.version = {major: version[0], minor: version[1], revision: version[2]};
    }).apply(self);
    (function() {
    }).apply(self);
    (function() {
        var exports = {};
    
        self._registerExport = function(name, obj) {
            exports[name] = obj;
        };
        self.gimme = function(name) {
            if (name) {
                return exports[name];
            }
            return keys(exports);
        };
        self.gimmeAll = function(nameMap) {
            nameMap = nameMap || {};
            var items = self.gimme();
            each(items, function(i, item) {
                win[nameMap[item] || item] = self.gimme(item);
            });
        };
    }).apply(self);
        const Code = new function() {
            const Template = function(to, from) {
                var code = this,
                    choice = function(text, toOrFrom) {
                        return toOrFrom === false ? code.from(text) : code.to(text);
                    };
                this.to = choice.to = to;
                this.from = choice.from = from;
                return choice;
            };
    
            this.html = new Template(
                function(text) {
                    return text.toString()
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;')
                        .replace(/\"/g, '&quot;');
                },
                function(text) {
                    return text.toString()
                        .replace(/&quot;/g, '"')
                        .replace(/&gt;/g, '>')
                        .replace(/&lt;/g, '<')
                        .replace(/&amp;/g, '&');
                },
            );
    
            this.css = new Template(
                function(text) {
                    return CSS.escape(text);
                },
                function(text) {
                    return text;
                },
            );
    
            this.url = new Template(
                function(text) {
                    return encodeURIComponent(text).replace(/%2F/gi, '/');
                },
                function(text) {
                    try {
                        return decodeURIComponent(text);
                    } catch (e) { }
                    return text;
                },
            );
        };
        self._registerExport('Code', Code);
    (function() {
        self.FunctionHook = function() {
            this._functionList = [];
        };
        self.FunctionHook.prototype.check = function(func) {
            return false;
        };
        self.FunctionHook.prototype.add = function(func) {
            this._functionList.push(func);
            if (this.check()) {
                func.apply(this, Array.prototype.slice.call(arguments, 1));
            }
        };
        self.FunctionHook.prototype.run = function() {
            var args = arguments;
            each(this._functionList, function(i, item) {
                item.apply(this, args);
            });
        };
        self.FunctionHook.prototype.clear = function() {
            this.onclear();
            this._functionList = [];
        };
        self.FunctionHook.prototype.onclear = function() {
        };
    }).apply(self);
    (function() {
        var storage = {};
        self.Metronome = new function() {
            this.add = function(func, ms) {
                if (!storage[ms]) {
                    storage[ms] = new FunctionHook();
                    storage[ms].onclear = this.onclear(ms);
                    storage[ms].interval = window.setInterval(function() { storage[ms].run(); }, ms);
                }
                storage[ms].add(func);
            };
            this.onclear = function(ms) {
                return function() {
                    win.clearInterval(storage[ms].interval);
                };
            };
            this.clear = function() {
                each(storage, function(ms, item) {
                    storage[ms].clear();
                });
            };
            this.stop = function() {
                each(storage, function(ms, item) {
                    win.clearInterval(item.interval);
                });
            };
            this.go = function() {
                each(storage, function(ms, item) {
                    item.interval = win.setInterval(function() { item.run(); }, ms);
                });
            };
        };
    }).apply(self);
    (function() {
        var config = {
                'data-attribute-namespace': 'adservice',
                'friendlyiframe-adscript-id': 'external-script',
                'friendlyiframe-checkscript-id': 'internal-script',
                'friendlyiframe-readycheck-timeout': 50,
                'nonfriendlyiframe-document': 'https://dl.web.de/uim/connector/live/v2/nonfriendlyiframe.html',
                'visibility-minimum-fraction': 0.5,
                'request-ad-only-if-visible': false,
                'request-async': false,
                'secure': true,
                'adservice-iframe-script-url': 'https://dl.web.de/uim/connector/live/v2/connector.js',
                'auto-set-iframe-size': true,
                'slot-enabled-default': true,
                'abd-sync-cap': 1000*3600*12,
            };
        delete config._;
        self.getConfig = function(name, defaultValue) {
            if (!name) { return config; }
            if (config.hasOwnProperty(name)) { return config[name]; }
            return defaultValue;
        };
    
        self.setConfig = function(name, value) {
            var obj = arguments.length>1 ? {} : name;
            if (arguments.length>1) {
                obj[name] = value;
            }
            each(obj, function(name, value) { config[name] = value; });
            return self;
        };
    }).apply(self);
        self.getDataAttrName = function(suffix) {
            return 'data-' + CSS.escape(self.getConfig('data-attribute-namespace')) + '-' + suffix;
        };
        self.createElement = function(name, attrs, props) {
            const el = document.createElement(name);
            each(attrs, function(n, v) { el.setAttribute(n, v); });
            each(props, function(n, v) { el[n] = v; });
            return el;
        };
        self.buildFriendlyIframe = function(domNode, data, type) {
            const slot = data.slot;
            const width = slot.fixedSize.type === 'fixed' ? slot.fixedSize.width : '100%';
            const height = slot.fixedSize.type === 'fixed' ? slot.fixedSize.height : domNode.offsetHeight||'100%';
            const iframeAttrs = {frameborder: 0, border: 0, scrolling: 'no', marginwidth: 0, marginheight: 0, width, height};
            const sandboxAttrs = slot.getSandboxAttributes();
            if (Object.keys(sandboxAttrs).length) {
                iframeAttrs.sandbox = slot.getSandboxAttributeValue(sandboxAttrs);
            }
            if (slot.fixedSize.type === 'pagecontrol') {
                delete iframeAttrs.width;
                delete iframeAttrs.height;
            }
            const iframe = self.createElement('iframe', iframeAttrs, {src: 'about:blank'});
            iframe.setAttribute('aria-hidden', 'true');
            iframe.setAttribute('tabindex', '-1');
            domNode.appendChild(iframe);
            const iframeWindow = iframe.contentWindow;
            const iframeDocument = iframeWindow.document;
            iframeDocument.open();
    
            self.trigger('ad.request', slot, data);
            iframeDocument.write(''
                + '<!DOCTYPE html>\n'
                + '<html>\n'
                + '<head>\n'
                + '<meta charset="UTF-8"/>\n'
                + '<script>\n'
                + '    var _adServer = "' + data.slot.getAdServer().id + '";\n'
                + '    var _adSlot = "' + data.slot.id + '";\n'
                + '    window.AdService = [];\n'
                + '    function checkAdReady() { document.close(); }\n'
                + '    function checkRunner() {\n'
                + '        if (checkRunner.finished) { return; }\n'
                + '        if (window.AdService instanceof Array && --checkRunner.triesLeft) {\n'
                + '            checkRunner.timeout += 5;\n'
                + '            return window.setTimeout(checkRunner, checkRunner.timeout);\n'
                + '        }\n'
                + '        checkRunner.finished = true;\n'
                + '        checkAdReady();\n'
                + '    }\n'
                + '    checkRunner.timeout = 20;\n'
                + '    checkRunner.triesLeft = 25;\n'
                + '</script>\n'
                + '<script src="' + Code.html(self.getConfig('adservice-iframe-script-url')) + '"><\/script>\n'
                + '<style>\n'
                + 'html { overflow: hidden; }\n'
                + 'html, body { min-width: 100%; min-height: 100%; }\n'
                + 'img, iframe { display: block; }\n'
                + 'a img { border: none; }\n'
                + 'html.testsize { display: table !important; }\n'
                + 'html.testsize * { float: left !important; }\n'
                + 'html.testsize .adp-admarker-icon + .adp-admarker-text { display: none; }\n'
                + '<\/style>\n'
                + '<style id="pageStyles">' + (type ? self.useIframeCss(type) : '') + '</style>\n'
                + '<\/head>\n'
                + '<body><div>\n'
                + data.html
                + '<script id="internal-script">checkRunner();<\/script>\n'
                + '<\/body>\n'
                + '<\/html>',
            );
            self.trigger('ad.requested', slot, data);
            log.level('friendlyiframe', 'iframe.document.created', iframeWindow.frameElement.parentNode.getAttribute(self.getDataAttrName('slot')));
        };
    (function() {
        var eventListener = {};
    
        var AdServiceEvent = function(evn, slot, data) {
            this.type = evn;
            this.data = data || {};
            this.slot = slot || null;
            this.target = this.data.target || (this.slot?.getTarget?.()) || null;
            this.request = this.data.request || (this.slot?.getRequest?.()) || null;
        };
        AdServiceEvent.prototype.get = function(name, defaultValue) {
            return this.data.hasOwnProperty(name) ? this.data[name] : (defaultValue || undefined);
        };
        AdServiceEvent.prototype.set = function(name, value) {
            this.data[name] = value;
        };
    
        self.on = function(evn, condition, func) {
            if (!func) {
                func = condition;
                condition = undefined;
            }
            if (evn === 'now') {
                return func.apply(self);
            }
            self._initEventName(evn);
            if (eventListener[evn].isUnique && eventListener[evn].triggered && self._eventConditionFulfilled(condition)) {
                return self._runEventListener(func);
            }
            var eventFunc = function() {
                var event = arguments[0] instanceof AdServiceEvent ? arguments[0] : null;
                if (!this.condition || self._eventConditionFulfilled(this.condition, event)) {
                    this.func.apply(this, arguments);
                }
            };
            eventFunc.condition = condition;
            eventFunc.func = func;
            eventFunc = eventFunc.bind(eventFunc);
            eventListener[evn].add(eventFunc);
            self.trigger('eventhandler.added', {on: evn, condition: condition, func: func, eventFunc: eventFunc});
            return self;
        };
        self.trigger = function(evn, slot, data) {
            log.level('event.trigger', evn, slot, data && extend({}, data), arguments);
            if (!eventListener[evn]) { return; }
            if (eventListener[evn].isUnique && eventListener[evn].triggered) { return; }
            if (slot && !(slot instanceof self.AdSlot)) {
                data = slot;
                slot = null;
            }
            eventListener[evn].triggered = true;
            eventListener[evn].run(new AdServiceEvent(evn, slot, data));
            return self;
        };
        self._from_iframe_triggerUp = function(evn, slot, data) {
            self.trigger(evn, self.AdSlot(slot), data);
        };
        self._triggerFlat = function(evn) {
            log.level('event._triggerFlat', evn, arguments);
            if (!eventListener[evn]) { return; }
            if (eventListener[evn].isUnique && eventListener[evn].triggered) { return; }
            eventListener[evn].triggered = true;
            eventListener[evn].run.apply(eventListener[evn], [].splice.call(arguments, 1));
            return self;
        };
        self.triggerOnce = function(evn, slot, data) {
            self.setUniqueEvent(evn, true);
            return self.trigger(evn, slot, data);
        };
        self.setUniqueEvent = function(evn, isUnique) {
            self._initEventName(evn);
            eventListener[evn].isUnique = isUnique===false ? false : true;
            return self;
        };
        self.eventHandlerCount = function(evn) {
            return (eventListener[evn] || {_functionList: []})._functionList.length;
        };
        self.hasEventHandler = function(evn) {
            return self.eventHandlerCount(evn) > 0;
        };
    
        self._initEventName = function(evn) {
            if (!eventListener[evn]) {
                eventListener[evn] = new self.FunctionHook();
            }
            return eventListener[evn];
        };
        self._eventConditionFulfilled = function(condition, event) {
            if (condition === undefined) { return true; }
            if (typeof condition === STRING) { return event?.slot?.id === condition; }
            try {
                return condition(event);
            } catch (e) {
                return condition;
            }
            return true;
        };
        self._runEventListener = function(func) {
            try {
                return func.apply(self);
            } catch (e) {
                return undefined;
            }
        };
    }).apply(self);
    (function() {
        function bodyBegin() {
            self.setUniqueEvent('body.begin', true);
            var done = false,
                animationFrame;
            if (doc.getElementsByTagName('body').length) {
                return self.trigger('body.begin');
            }
            if (win.requestAnimationFrame) {
                animationFrame = win.requestAnimationFrame(function() {
                    if (done || doc.getElementsByTagName('body').length === 0) { return; }
                    done = true;
                    win.cancelAnimationFrame(animationFrame);
                    self.trigger('body.begin');
                });
            }
            var interval = win.setInterval(function() {
                if (doc.getElementsByTagName('body').length === 0) { return; }
                if (done) { return win.clearInterval(interval); }
                done = true;
                win.clearInterval(interval);
                self.trigger('body.begin');
            }, 20);
        }
        function bodyEnd() {
            self.on('body.end', function() { self.trigger('body.begin'); }); 
            self.setUniqueEvent('body.end', true);
            if (doc.readyState === 'complete') {
                return self.trigger('body.end');
            }
            if (doc.addEventListener) {
                doc.addEventListener('DOMContentLoaded', function() {
                    self.trigger('body.end');
                }, false);
            } else if (doc.attachEvent) { 
                doc.attachEvent('onreadystatechange', function() {
                    if (doc.readyState !== 'complete') { return; }
                    self.trigger('body.end');
                });
            }
        }
        function pagePositionChange() {
            var minWait = 100,
                timeoutHandler = null;
            function positionChangeHandler() {
                timeoutHandler = null;
                self.trigger('page.positionchange');
            }
            function positionChangeRunner() {
                if (timeoutHandler) { return; }
                timeoutHandler = win.setTimeout(positionChangeHandler, minWait);
            }
            win.addEventListener('resize', positionChangeRunner, false);
            doc.addEventListener('scroll', positionChangeRunner, true);
        }
    
        self.on('initialize.begin', function() {
            bodyBegin.apply(self); 
            bodyEnd.apply(self); 
            pagePositionChange.apply(self);
        });
    }).apply(self);
    (function() {
        self.Module = function(name, func) {
            if (this===self) {
                return self.Module.modules[name];
            }
            if (self.Module.modules[name]) {
                throw new Error('Module "' + name + '" already defined');
            }
            self.Module.modules[name] = this;
            if (typeof func === FUNCTION) {
                this.exec(func);
            }
        };
        self.Module.modules = {};
        self.Module.prototype._locals = {};
        self.Module.prototype._globals = {};
        self.Module.prototype.exec = function(func) {
            func.apply(this);
            return this;
        };
        self.Module.prototype._registerMember = function(name, member, reference, collection, desc) {
            if (name in reference) {
                throw new Error(desc + ' member "' + name + '" already defined');
            }
            reference[name] = typeof member === FUNCTION ? member.bind(reference) : member;
            collection[name] = reference[name];
            return this;
        };
        self.Module.prototype.extendBaseObject = function(name, member) {
            return this._registerMember(name, member, self, this._globals, 'Global');
        };
        self.Module.prototype.set = self.Module.prototype.extend = function(name, member) {
            return this._registerMember(name, member, this, this._locals, 'Local');
        };
        self.Module.logError = function(e) {
            log.error('Error in Module:', e);
        };
    }).apply(self);
    self.push = function(func) {
        precalls.push(func);
    };
    self.on('initialize.begin', function() {
        self.push = function() {
            each(arguments, function(i, func) {
                if (typeof func === STRING) {
                    if (func === '#cleanup') {
                        return self.cleanup();
                    } else if (typeof self.info.abd === FUNCTION && self.info.abd().didCheck === false && self.info.abd().initialized) {
                        return AdService.on('adblock.detectionfinished', function() {
                            return self.ad(func);
                        });
                    } else {
                        return self.ad(func);
                    }
                }
                try { func.apply(self); }
                catch (e) { log.error('Error calling function via AdService.push():', e, func); }
            });
        };
        self.push.apply(self, precalls);
    });
    (function() {
        var queues = {};
    
        function execute(func, name, reason) {
            var data = {name: name, reason: reason};
            try {
                func(data);
            } catch (e) {
                log.error('Error in waiting queue function', e, arguments, data, this);
            }
        }
    
        self.registerWaitingQueue = function(name) {
            queues[name] = queues[name] || [];
            return self;
        };
        self.waitFor = function(name, func) {
            if (!queues[name]) {
                execute(func, name, queues.hasOwnProperty(name) ? 'completed' : 'noqueue');
                return self;
            }
            queues[name].push(func);
            return self;
        };
        self.waitingComplete = function(name) {
            var queue = queues[name];
            queues[name] = false;
            each(queue, function(i, func) {
                execute(func, name, 'processing');
            });
            return self;
        };
        self.isQueueCompleted = function(name) {
            return queues[name] === false;
        };
    
        self.getQueue = function(name) {
            return name ? queues[name] : queues;
        };
    }).apply(self);

    (function() {
        self.info = {};
        var _infoStack = {};
    
        self._determineInfo = function(name) {
            var data = {
                info: name,
            };
            self.trigger('determineSystem.begin', data);
            try {
                self.info['_'+name] = _infoStack[name]();
            } catch (e) {
                self.info['_'+name] = {};
            }
            self.trigger('determineSystem.end', data);
        };
        self.registerInfo = function(name, func, isPersistent, options) {
            if (typeof name === STRING) {
                return self.registerInfo(extend({name: name, func: func, persistent: isPersistent}, options));
            }
            var data = extend({persistent: false, event: 'now', initial: {}}, name);
            _infoStack[data.name] = data.func;
            self.info[data.name] = data.persistent
                            ? function() {  return self.info['_'+data.name]; }
                            : function() { self._determineInfo(data.name); return self.info['_'+data.name]; }
            ;
            self.info['_'+data.name] = data.initial;
            self.on(data.event, function() {
                self._determineInfo(data.name);
            });
        };
    }).apply(self);
    (function() {
        function getAdSizeTable() {
            var sizetable = function(adSize) {
                var lookup = {
                    '640x480': {
                        'format': 'maxiad',
                    },
                    '800x600': {
                        'format': 'maxiad',
                    },
                    '1260x690': {
                        'format': 'maxiad',
                    },
                    '300x250': {
                        'format': 'rectangle',
                    },
                    '300x120': {
                        'format': 'half_rectangle',
                    },
                    '728x90': {
                        'format': 'superbanner',
                    },
                    '120x600': {
                        'format': 'sky',
                    },
                    '160x600': {
                        'format': 'widesky',
                    },
                    '200x600': {
                        'format': 'widesky',
                    },
                    '800x250': {
                        'format': 'billboard',
                    },
                    '970x250': {
                        'format': 'billboard',
                    },
                    '994x250': {
                        'format': 'billboard',
                    },
                    '220x600': {
                        'format': 'sitebar',
                    },
                    '300x600': {
                        'format': 'halfpage',
                    },
                    '640x960': {
                        'format': 'understitial',
                    },
                    '600x338': {
                        'format': 'intextvideoad',
                        'useAutoSetIframeSize': false,
                    },
                    '70x20': {
                        'format': 'promoline',
                    },
                    '274x75': {
                        'format': 'notification',
                    },
                    '300x300': {
                        'format': '1zu1',
                    },
                    '300x150': {
                        'format': '2zu1',
                    },
                    '300x75': {
                        'format': '4zu1',
                    },
                    '320x75': {
                        'format': '4zu1',
                    },
                    '300x50': {
                        'format': '6zu1',
                    },
                    '320x50': {
                        'format': '6zu1',
                    },
                    '300x100': {
                        'format': '3zu1',
                    },
                    '1x1': {
                        'format': 'ambient',
                    },
                    '3x3': {
                        'format': 'lounge',
                    },
                    'fallback': {
                        'format': 'fallback',
                        'useAutoSetIframeSize': true,
                        'checkAutoSetIframeSize': false,
                    },
                };
                var slot = self.AdSlot(self._getCurrentAdSlot());
                return slot.getConfig('adformatLookup') || lookup[adSize] || lookup.fallback;
            };
            var useAutoSetIframeSize = function(adSize) {
                return sizetable(adSize).useAutoSetIframeSize ?? true;
            };
            var checkAutoSetIframeSize = function(adSize) {
                return sizetable(adSize).checkAutoSetIframeSize ?? false;
            };
            var evaluateResizeValues = function(adSize, resizeTo) {
                if (!sizetable(adSize).maxResize || !resizeTo) { return false; }
                var check = {};

                if (resizeTo.height) {
                    check = !resizeTo.height.toString().includes('%') ? {value: sizetable(adSize).maxResize.height, unit: 'px'} : {value: 100, unit: '%'};
                    resizeTo.height = Math.min(parseInt(resizeTo.height), check.value) + check.unit;
                }
                if (resizeTo.width) {
                    check = !resizeTo.width.toString().includes('%') ? {value: sizetable(adSize).maxResize.width, unit: 'px'} : {value: 100, unit: '%'};
                    resizeTo.width = Math.min(parseInt(resizeTo.width), check.value) + check.unit;
                }

                return resizeTo;
            };

            return {sizetable, useAutoSetIframeSize, checkAutoSetIframeSize, evaluateResizeValues};
        }
        self.registerInfo({
            name: 'adformat',
            func: getAdSizeTable,
            persistent: true,
        });
    }).apply(self);
    (function() {
        function generateFallbackConsent(uiConsent) {
            try {
                uiConsent.permissionFeature = JSON.parse(decodeURIComponent(getCookieValue('uiconsent') || '[]')).permissionFeature || [];
            } catch (e) { }

            uiConsent.fullConsent = uiConsent.permissionFeature instanceof Array && uiConsent.permissionFeature.includes('fullConsent');
            if (uiConsent.fullConsent) {
                uiConsent.acString = '1~fallback';
                uiConsent.vendorConsent = true;
                uiConsent.publisher = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
                uiConsent.customPurpose = {
                    adition: [1, 4],
                    google: [1, 3],
                };
                uiConsent.purpose = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
                uiConsent.specialFeature = [2];
            }
            return uiConsent;
        }
        function generateConsent(uiConsent, response) {
            var vendorList = [10, 21, 28, 32, 34, 39, 40, 42, 44, 45, 50, 52, 68, 69, 70, 76, 78, 91, 115, 126, 152, 154, 164, 165, 177, 242, 253, 278, 312, 371, 539, 559, 580, 597, 647, 755, 775, 786, 793, 795, 831, 937, 967, 1009];
            function createConsentArray(data) {
                if (!data) { return []; }
                if (data.consents) { data = data.consents; }
                return Object.keys(data).filter(function(i) { return data[i]; }).map(function(i) { return parseInt(i); });
            }
            if (!!response.tcString) {
                uiConsent.tcString = response.tcString;
                uiConsent.publisher = createConsentArray(response.publisher);
                uiConsent.customPurpose.adition = createConsentArray(response.publisher.customPurpose);
                uiConsent.customPurpose.google = createConsentArray(response.publisher.customPurpose);
                uiConsent.purpose = createConsentArray(response.purpose);
                uiConsent.specialFeature = createConsentArray(response.specialFeatureOptins);
                uiConsent.vendorConsent = Object.keys(obj = response.vendor?.consents || {}).reduce(function(x, y) { return x || obj[y]; }, false);
                uiConsent.vendorList = vendorList.filter(function(vendor) { return response.vendor.consents[vendor] || response.vendor.legitimateInterests[vendor]; });
                if (uiConsent.hasConsent(uiConsent.purpose, [1, 2, 3, 4, 7]) && response.vendor.consents[755]) {
                    uiConsent.google = true;
                }
                if (uiConsent.hasConsent(uiConsent.purpose, [1, 2, 3, 4, 7]) && response.specialFeatureOptins[2] && response.publisher.customPurpose.consents[1]) {
                    uiConsent.acString = response.addtlConsent;
                }
                uiConsent.vendorListVersion = response.vendorListVersion;
                uiConsent.tcfPolicyVersion = response.tcfPolicyVersion;

                uiConsent.fallback = false;
            }
            return uiConsent;
        }
        var uiConsentInfo = function() {
            var uiConsent = {
                tcString: getCookieValue('euconsent-v2'),
                acString: '',
                permissionFeature: [],
                publisher: [],
                customPurpose: {adition: [], google: []},
                purpose: [],
                specialFeature: [],
                vendorList: [],
                google: false,
                fallback: true,
                hasConsent: function(userConsent, consentToCheck) {
                    return consentToCheck.reduce(function(result, i) {
                        return result && userConsent.includes(i);
                    }, true);
                },
            };
            var tcData = window.__tcData;
            uiConsent = tcData ? generateConsent(uiConsent, tcData) : generateFallbackConsent(uiConsent);

            if (tcData || !window.__tcfapi || typeof window.__tcfapi !== FUNCTION) { return uiConsent; }

            try {
                window.__tcfapi('addEventListener', 2, function(response, success) {
                    if (!success) { return; }
                    uiConsent = generateConsent(uiConsent, response);
                });
            } catch (e) { }

            return uiConsent;
        }();
        function getConsentInfo() {
            return uiConsentInfo;
        }
        self.registerInfo({
            name: 'consent',
            func: getConsentInfo,
            persistent: true,
        });
    }).apply(self);
    (function() {
        var userAgentInfo = {
            ua: navigator.userAgent.toLowerCase(),
            details: {},
        };
        userAgentInfo.ua.replace(/[\(\)]/g, ';').replace(/\s*(\w*?\ ?\w+[\:\s]?[^\d\s;]*?)[\/\ ;](\d[.\w]*|)/g, function(pattern, g1, g2) {
            userAgentInfo.details[g1] = userAgentInfo.details[g1] || g2 || true;
        });
        var assignVersionNumber = function(userAgentInfo, name, type) {
            if (!userAgentInfo.details[name]) { return; }
            userAgentInfo[type] = {};
            userAgentInfo[type].name = function() {
                if (/(mobile )?safari/.test(name) && userAgentInfo.details.android) { return 'native'; }
                if (name === 'chrome' && +userAgentInfo.details.version === 1) { return 'native'; }
                if (name === 'os x' || name === 'mac os') { return userAgentInfo.details.macintosh ? 'mac os' : 'ios'; }
                if (name === 'cros') { return 'chromium os'; }
                if (name === 'fxios' || name === 'focus') { return 'firefox'; }
                if (name === 'crios' || name === 'headlesschrome') { return 'chrome'; }
                if (name === 'opios' || name === 'opr') { return 'opera'; }
                if (name === 'mobile safari') { return 'safari'; }
                return userAgentInfo[type].name = name;
            }();
            userAgentInfo[type].version = function() {
                if (type === 'browser' && userAgentInfo.details.version && name != 'focus' && userAgentInfo.browser.name != 'chrome') { return userAgentInfo.details.version; }
                if (name === 'os x' || name === 'mac os') {
                    const version = /(?:cpu(?:\siphone)?|mac)\sos(?:\sx)?\s([\d_\.]+)/.exec(userAgentInfo.ua)?.[1];
                    if (version) { return version.replace(/_/g, '.'); }
                }
                if (name === 'cros') {
                    const version = /cros\s[^\s]+\s([\d\.]+)/.exec(userAgentInfo.ua)?.[1];
                    if (version) { return version; }
                }
                return userAgentInfo.details[name] === true ? undefined : userAgentInfo.details[name];
            }();
            return userAgentInfo[type];
        };
        ['edge', 'edg', 'edga', 'msie', 'opr', 'opera', 'aurora', 'avast', 'focus', 'iron', 'maxthon', 'seamonkey', 'vivaldi', 'firefox', 'samsungbrowser', 'crios', 'fxios', 'opios', 'headlesschrome', 'chrome', 'mobile safari', 'gsa', 'safari'].forEach(function(name) {
            if (userAgentInfo.browser) { return; }
            userAgentInfo.browser = assignVersionNumber(userAgentInfo, name, 'browser');
        });
        ['windows', 'windows nt', 'windows phone', 'windows rt', 'mac os', 'os x', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'rim', 'linux', 'cros'].forEach(function(name) {
            if (userAgentInfo.os) { return; }
            userAgentInfo.os = assignVersionNumber(userAgentInfo, name, 'os');
        });
        if (userAgentInfo.details.android && userAgentInfo.details.applewebkit && userAgentInfo.details.mozilla && userAgentInfo.details.version && (parseInt(userAgentInfo.details.applewebkit) < 537 || parseInt(userAgentInfo.details.chrome) < 37)) {
            userAgentInfo.browser = {
                name: 'android',
                version: userAgentInfo.details.version,
                android: userAgentInfo.details.version,
            };
        } else if (!userAgentInfo.browser && userAgentInfo.details.applewebkit && userAgentInfo.details.mobile) {
            userAgentInfo.browser = {
                name: 'webkit',
                version: userAgentInfo.details.applewebkit,
                webkit: userAgentInfo.details.applewebkit,
            };
        } else if (userAgentInfo.details.trident) {
            userAgentInfo.browser = {
                name: 'msie',
                version: +userAgentInfo.details.trident + 4,
                msie: +userAgentInfo.details.trident + 4,
            };
        }
        userAgentInfo.browser = userAgentInfo.browser || {name: '', version: ''};
        if (typeof navigator.brave?.isBrave === FUNCTION) {
            userAgentInfo.browser.name = 'brave';
        }
        userAgentInfo.os = userAgentInfo.os || {name: '', version: ''};
        userAgentInfo.connection = navigator.connection?.effectiveType || navigator.connection?.type;
        userAgentInfo.format = function() {
            if (navigator.userAgentData?.mobile) { return 's'; }
            if (/android(?!.*mobile)|ipad|kindle|playbook|\btab|surface/.test(userAgentInfo.ua)) { return /huawei|ecosia/i.test(userAgentInfo.ua) ? 's' : 'm'; }
            if (/mobi|windows phone|iphone|blackberry/.test(userAgentInfo.ua)) { return 's'; }
            return 'b';
        }();
        userAgentInfo.result = {
            browser: {
                name: userAgentInfo.browser.name || undefined,
                version: /(\d+)([^\d]|$)/.exec(userAgentInfo.browser.version)?.[1],
            },
            os: {
                name: userAgentInfo.os.name || undefined,
                version: userAgentInfo.os.version ?? undefined,
            },
            type: userAgentInfo.format === 's' ? 'mobile' : 'desktop',
            deviceclass: userAgentInfo.format,
            net: userAgentInfo.connection,
        };
        function getUAInfo() {
            return userAgentInfo.result;
        }
    
        self.registerInfo('ua', getUAInfo, true);
    }).apply(self);
    (function() {
        function determineView() {
            return {
                screen: {width: screen.width, height: screen.height},
                window: {
                    width: win.outerWidth,
                    height: win.outerHeight,
                    x: ('screenLeft' in win) ? win.screenLeft : win.screenX,
                    y: ('screenTop' in win) ? win.screenTop : win.screenY,
                },
                viewport: {width: win.innerWidth, height: win.innerHeight},
            };
        };
        self.registerInfo('view', determineView, false);
    }).apply(self);
    (function() {
        function determineDevice() {
            return {
                devicetype: {b: 'desktop', m: 'tablet', s: 'mobile'}[self.info.ua().deviceclass],
                retina: (typeof window.matchMedia === FUNCTION && !!window.matchMedia('(-webkit-min-device-pixel-ratio: 2)').matches),
                touchscreen: 'ontouch' in doc.documentElement, 
            };
        }
        self.registerInfo('device', determineDevice, true);
    }).apply(self);
    (function() {
        function determineView() {
            return {
                canvas: 'getContext' in doc.createElement('canvas'),
            };
        };
        self.registerInfo('features', determineView, true);
    }).apply(self);
    (function() {
        function getConnection() {
            return {
                type: self.info.ua().net,
                speed: undefined,
            };
        }
        self.registerInfo('connection', getConnection, true);
    }).apply(self);

    (function() {
        self.ObjectList = function() {
            this.list = {};
            this.register = function(obj) {
                this.list[obj.id] = obj;
            };
            this.getList = function() {
                return this.list;
            };
            this.get = function(id) {
                return this.list[id];
            };
        };
    }).apply(self);
    (function() {
        const AdServerList = new self.ObjectList();
        const paramOverride = {global: {}, slots: {}, secured: {}};
    
        each(Const.QUERYPARAMS, (key, val) => {
            if (!key.startsWith(Const.OVERRIDE_PARAM_PREFIX)) { return; }
            const keyParts = key.substring(Const.OVERRIDE_PARAM_PREFIX.length).split(':');
            if (['portal', 'category', 'section', 'tagid', 'layoutclass'].includes(keyParts[1] || keyParts[0])) { return; }
            if (keyParts[1]) {
                paramOverride.slots[keyParts[0]] = paramOverride.slots[keyParts[0]] || {};
                paramOverride.slots[keyParts[0]][keyParts[1]] = val;
            } else {
                paramOverride.global[keyParts[0]] = val;
            }
        });
    
        self.AdServer = function(id, config, params) {
            if (this === self) {
                return id instanceof self.AdServer ? id : (AdServerList.get(id) || new self.AdServer(id, config, params));
            }
            this.id = id;
            this.activated = false;
            this[securedSymbol] = {
                setSecuredParam: function(name, value) {
                    paramOverride.secured[name] = value;
                },
                getSecuredParam: function() { return paramOverride.secured; },
            };
            this.isSecured = (key) => this[securedSymbol].getSecuredParam().hasOwnProperty(key);
            this._global = {};
            this.setConfig(extend({
                enabled: true,
                supportRefresh: true,
                protocol: '//',
                host: '',
                path: '',
                sepQuery: '?',
                sepParams: '&',
                sepNameValue: '=',
                sepFragment: '#',
                paramOrder: {
                    begin: [],
                    end: [],
                    ignore: [],
                },
                requestTypes: {
                    javascript: undefined,
                    iframe: undefined, 
                    friendlyIframe: undefined,
                    json: undefined,
                },
                evenIfEmpty: [],
                checkParams: undefined, 
                nonContentElements: [],
            }, config));
            this.setParam(params);
            this.setParam('asv',`${self.version.major}${self.version.minor.padStart(3, '0')}${self.version.revision.padStart(2, '0')}`);
    
            if (typeof this.getConfig('init') === FUNCTION) {
                try {
                    this.getConfig('init').bind(this)();
                } catch (e) {
                    log.error('Error in initialization of AdServer "' + id + '"', e, this);
                }
            }
    
            this.slotConfig = {};
            this.slotParams = {};
    
            this.AdServer = self.AdServer.bind(self);
            AdServerList.register(this);
            if (this.config.probableDefault && !self.getDefaultAdServer()) {
                self.setDefaultAdServer(this);
            }
        };
    
        self.AdServer.getList = function() {
            return AdServerList.getList();
        };
    
        self.AdServer.prototype.activate = function() {
            if (this.activated) { return this; }
            this.activated = true;
            if (typeof this.config.firstUse === FUNCTION) {
                this.config.firstUse.bind(this)();
            }
            return this;
        };
        self.AdServer.prototype.firstUse = function() {
        };
        self.AdServer.prototype.setParam = function(params) {
            if (typeof params !== OBJECT || params === null) {
                var tmp = {};
                tmp[arguments[0]] = arguments[1];
                params = tmp;
            }
            cleanedParams = {};
            for (var key in params) {
                if (!paramOverride.secured.hasOwnProperty(key)) {
                    cleanedParams[key] = params[key];
                }
            }
            var data = {
                server: this,
                params: cleanedParams,
            };
            self.trigger('server.setparam', data);
            this.params = extend(this.params || {}, data.params);
            return this;
        };
        self.AdServer.prototype.setDirectParam = function(params) {
            if (typeof params !== OBJECT || params === null) {
                var tmp = {};
                tmp[arguments[0]] = arguments[1];
                params = tmp;
            }
            var data = {
                server: this,
                params: params,
            };
            self.trigger('server.setdirectparam', data);
            this.directParams = extend(this.directParams || {}, data.params);
            return this;
        };
        self.AdServer.prototype.setConfig = function(config) {
            if (typeof config === STRING) {
                var tmp = {};
                tmp[arguments[0]] = arguments[1];
                config = tmp;
            }
            var data = {
                server: this,
                config: config,
            };
            self.trigger('server.setconfig', data);
            this.config = extend(this.config || {}, data.config);
            return this;
        };
        self.AdServer.prototype.enable = function() {
            this.setConfig('enabled', true);
            self.trigger('server.enabled', self._getCurrentAdSlot(), {server: this});
            return this;
        };
        self.AdServer.prototype.disable = function() {
            this.setConfig('enabled', false);
            self.trigger('server.disabled', self._getCurrentAdSlot(), {server: this});
            return this;
        };
        self.AdServer.prototype.isEnabled = function() {
            return this.getConfig('enabled');
        };
        self.AdServer.prototype.setSlotParam = function(slot, key, value) {
            var slotParams = {};
            if (typeof slot === STRING) {
                if (typeof key === STRING) {
                    var tmp = {};
                    tmp[key] = value;
                    slotParams[slot] = tmp;
                } else {
                    slotParams[slot] = key;
                }
            } else {
                slotParams = slot;
            }
            this.slotParams = extend(this.slotParams, slot);
            return this;
        };
        self.AdServer.prototype.setSlotConfig = function(slot, key, value) {
            var slotConfig = {};
            if (typeof slot === STRING) {
                if (typeof key === STRING) {
                    var tmp = {};
                    tmp[key] = value;
                    slotConfig[slot] = tmp;
                } else {
                    slotConfig[slot] = key;
                }
            } else {
                slotConfig = slot;
            }
    
            this.slotConfig = extend(this.slotConfig, slot);
            return this;
        };
        self.AdServer.prototype.getUrl = function(slot, requestMethod) {
            slot = self._getAdSlot(slot);
            var data = {
                server: this,
                slot: slot,
                requestMethod: requestMethod,
            };
            self.trigger('server.getUrl', slot, data);
            var url = this._getUrl(slot, requestMethod);
            return url;
        };
        self.AdServer.prototype.getConfig = function(key, defaultValue) {
            if (typeof key !== UNDEFINED) {
                return this.config.hasOwnProperty(key) ? this.config[key] : defaultValue;
            } else {
                return this.config;
            }
        };
        self.AdServer.prototype.getParams = function() {
            var params = extend({}, this.params || {}, paramOverride.global || {}, paramOverride.secured || {});
            each(params, function(key, value) {
                if (typeof params[key] === FUNCTION) {
                    params[key] = params[key](this);
                }
            });
            return params;
        };
        self.AdServer.prototype.getParam = function(key, defaultValue) {
            if (key instanceof Array) {
                var result = {};
                var params = this.getParams();
                each(key, function(i, name) {
                    result[name] = params[name];
                });
                return result;
            }
            return this.getParams()[key] || defaultValue;
        };
    
        self.AdServer.prototype.getParamsForSlot = function(slot) {
            var params = extend({}, this.params || {}, slot.params || {}, paramOverride.global, paramOverride.slots[slot] || {}, slot.onetimeParams || {}, paramOverride.secured || {}, slot.getExpiringParams() || {});
            each(params, function(key, value) {
                if (typeof value === FUNCTION) {
                    params[key] = params[key](slot);
                }
            });
            return params;
        };
    
        self.AdServer.prototype.getParamForSlot = function(slot, key, defaultValue) {
            return this.getParamsForSlot(slot)[key] || defaultValue;
        };
    
        self.AdServer.prototype._getUrl = function(slot, requestMethod) {
            var config = this.config;
            var params = this.getParams();
            var paramString = this._getParamString(params, slot);
            return config.protocol + config.host + '/' + config.path + config.sepQuery + paramString;
        };
    
        self.AdServer.prototype.getUrlParams = function(slot, requestMethod) {
            var params = this.getParamsForSlot(slot, requestMethod);
            params = this.prepareParamsForUrl ? this.prepareParamsForUrl(params, slot) : params;
            var config = this.config;
    
            var data = {
                    params: params,
                    keyList: [],
                    sortedParams: {},
                },
                paramOrder = config.paramOrder;
    
            var evenIfEmpty = config.evenIfEmpty;
            for (var i=0, l=evenIfEmpty.length; i<l; i++) {
                if (typeof data.params[evenIfEmpty[i]] === UNDEFINED) {
                    data.params[evenIfEmpty[i]] = undefined;
                }
            }
    
            data.keyList = keys(data.params);
            self.trigger('server.getParams.begin', slot, data);
    
            var beginLength = paramOrder.begin.length;
            while (beginLength > 0) {
                var index = data.keyList.indexOf(paramOrder.begin[--beginLength]);
                if (index !== -1) {
                    data.keyList.unshift(data.keyList.splice(index, 1)[0]);
                }
            }
    
            for (var i=0, l=paramOrder.end.length; i<l; i++) {
                var index = data.keyList.indexOf(paramOrder.end[i]);
                if (index !== -1) {
                    data.keyList.push(data.keyList.splice(index, 1)[0]);
                }
            }
    
            for (var i=0, l=paramOrder.ignore.length; i<l; i++) {
                var index = data.keyList.indexOf(paramOrder.ignore[i]);
                if (index !== -1) {
                    data.keyList.splice(index, 1);
                }
            }
    
            var sortedParams = {};
            for (var i=0, l=data.keyList.length; i<l; i++) {
                sortedParams[data.keyList[i]] = data.params[data.keyList[i]];
            }
    
            data.sortedParams = sortedParams;
            self.trigger('server.getParams.end', slot, data);
            return data.sortedParams;
        };
    
        self.AdServer.prototype._getParamName = function(key, value, slot) {
            return key;
        };
    
        self.AdServer.prototype._getParamString = function(params, slot) {
            var paramStrings = [],
                config = this.config,
                server = this;
            each(params, function(key, value) {
                if (typeof value !== UNDEFINED || config.evenIfEmpty.includes(key)) {
                    if (typeof value === FUNCTION) {
                        value = value(slot);
                    }
                    paramStrings.push(server._getParamName(key, value, slot) + config.sepNameValue + Code.url(typeof value === UNDEFINED ? '' : value));
                }
            });
            return paramStrings.join(config.sepParams);
        };
        self.AdServer.prototype.isEmptyAd = function(domNode) {
            var elements = ['script', 'style'].concat(this.getConfig('nonContentElements'));
            var hasContent = false;
            each(domNode.childNodes, function(i, node) {
                if (node.nodeType !== document.ELEMENT_NODE) { return; }
                hasContent = hasContent || !elements.includes(node.nodeName.toLowerCase());
            });
            return !hasContent;
        };
        self.AdServer.prototype.renderMeta = function(html, meta) {
            return html;
        };
        self.AdServer.prototype.newPageContext = function() {
            log.nyi('AdService.AdServer(\''+this.id+'\').newPageContext()');
            return this;
        };
        self.AdServer.prototype.renewPageId = function() {
            log.nyi('AdService.AdServer(\''+this.id+'\').renewPageId()');
            return this;
        };
    
        self.AdServer.prototype.getAdSize = function(domNode) {
            log.nyi('AdService.AdServer(\''+this.id+'\').getAdSize(domNode)');
            return {width: undefined, height: undefined};
        };
    
        self.AdServer.prototype.splitMultiResponse = function(domNode) {
            log.nyi('AdService.AdServer(\''+this.id+'\').splitMultiResponse()');
            return [];
        };
    
        self.AdServer.prototype.exportMetaData = function() {
            return this;
        };
        self.AdServer.prototype.importMetaData = function(data) {
        };
    
        self.AdServer.prototype.getAdFormByMetaData = function(meta, fallbackAllowed) {
            log.nyi('AdService.AdServer(\''+this.id+'\').getAdFormByMetaData()');
            return undefined;
        };
    
        self.AdServer.prototype.getEventsByMetaData = function(meta) {
            log.nyi('AdService.AdServer(\''+this.id+'\').getEventsByMetaData()');
            return undefined;
        };
        self.AdServer.prototype.isAlive = function() {
            log.nyi('AdService.AdServer(\''+this.id+'\').isAlive()');
            return new Promise(function(done, reject) {
                window.setTimeout(function() {
                    return Math.random() < 0.5 ? done(true) : reject(false);
                }, 500 + Math.random()*1000);
            });
        };
    
        self.AdServer.prototype.checkUrlValidity = function(url) {
            log.nyi('AdService.AdServer(\''+this.id+'\').checkUrlValidity()');
            return false;
        };
    
        self.AdServer.prototype.back = function() {
            return self;
        };
    
        self.getParams = function() {
            return self.getDefaultAdServer().getParams();
        };
        self.getParam = function(name) {
            if (name instanceof Array) {
                var result = {};
                var params = self.getDefaultAdServer().getParams();
                each(name, function(i, name) {
                    result[name] = params[name];
                });
                return result;
            }
            return self.getDefaultAdServer().getParams()[name];
        };
        self.setParam = function() {
            var server = self.getDefaultAdServer();
            server.setParam.apply(server, arguments);
            return self;
        };
        self.setDirectParam = function() {
            var server = self.getDefaultAdServer();
            server.setDirectParam.apply(server, arguments);
            return self;
        };
    }).apply(self);
    (function() {
        var AdSlotList = new self.ObjectList();
    
        self.AdSlot = function(id, config, params) {
            if (this === self) {
                if (id instanceof self.AdSlot) { return id; }
                if (id instanceof Node) { return self.findAdSlotByNode(id); }
                return AdSlotList.get(id) || new self.AdSlot(id, config, params);
            }
            this.id = id;
            this.config = config || {};
            this.params = params || {};
            this.onetimeParams = {};
            this.expiringParams = {};
            this.directParams = {};
            this.sandboxAttributes = {};
            this.clones = [];
            this.parent = null;
            this.lastRequestedUrl = null;
            this.lastUsedAdHtml = null;
            this._pendingAdHtml = null;
            this.htmlQueue = [];
            this.fixedSize = {type: 'auto'};
            this.timeout = {id: undefined, note: '', callback: undefined, time: null};
            this.isIndependent = false;
            this.skipCheck = false;
    
            if (!this.config.hasOwnProperty('enabled')) {
                this.setConfig('enabled', self.getConfig('slot-enabled-default', true));
            }
    
            if (this.config.domNode) {
                this.setDomNode(this.config.domNode);
            }
            AdSlotList.register(this);
            if (!this.getConfig('enabled')) {
                self.trigger('slot.disabled', this);
            }
        };
    
        var isSecured = function(key) {
            return AdService.getDefaultAdServer().isSecured(key);
        };
    
        self.AdSlot.getList = function() {
            return AdSlotList.getList();
        };
    
        self.AdSlot.get = function(id) {
            log.deprecated('AdService.AdSlot.get("'+id+'"): Please use AdService.AdSlot("'+id+'") instead. The method .get() will be removed soon!');
            return AdSlotList.get(id) || new self.AdSlot(id);
        };
    
        self.AdSlot.getDomNodes = function() {
            var selectors = [];
            each(arguments, function(i, item) {
                var slotname = (item instanceof self.AdSlot || typeof item !== STRING)
                             ? item.id
                             : item;
                selectors.push('[' + self.getDataAttrName('slot') + '="' + Code.css(slotname) + '"]');
            });
            return doc.querySelectorAll(selectors.join(', '));
        };
    
        self.AdSlot.prototype.getDomNode = function() {
            return this.setDomNode((self.getConfig('slot-getdomnode-function') || function() { return; })(this));
        };
        function getDomNode(slot) {
            return slot.domNode || self.AdSlot.getDomNodes(slot)[0];
        }
        self.setConfig('slot-getdomnode-function', getDomNode);
    
        self.AdSlot.prototype.setDomNode = function(domNode) {
            if (!domNode) { return; }
            if (this.domNode === domNode) { return domNode; }
            this.domNode = domNode;
            this.domNode.adSlot = this;
            this.domId = this.domNode.id = this.domNode.id || this.generateDomId();
            this.domNode.setAttribute(self.getDataAttrName('slot') + '-initialized', 'true');
            var paramRoot = self.getDataAttrName('param-'),
                slot = this;
            each(this.domNode.attributes, function(i, attr) {
                if (!attr.nodeName.includes(paramRoot)) { return; }
                slot.params[attr.nodeName.substring(paramRoot.length)] = attr.nodeValue;
            });
            self._runOutsourceable('slotDomNodeInit', this, [this], this.getAdServer());
            return domNode;
        };
        self._global.slotDomNodeInit = function() { return self; };
    
        self.AdSlot.prototype.generateDomId = function() {
            var namespace = self.getConfig('data-attribute-namespace');
            return namespace + '-' + this.id;
        };
    
        self.AdSlot.prototype.getTarget = function() {
            return this.target || this.getDomNode();
        };
        self.AdSlot.prototype.setTarget = function(domNode) {
            this.target = domNode;
        };
    
        self.AdSlot.prototype.toString = function() {
            return this.id;
        };
        self.AdSlot.prototype.getParam = function(key, defaultValue) {
            if (key instanceof Array) {
                var result = {};
                var slot = this;
                each(key, function(i, name) {
                    result[name] = slot.getParam(name);
                });
                return result;
            }
            if (typeof this.params[key] !== UNDEFINED) {
                if (typeof this.params[key] === FUNCTION) {
                    return this.params[key](this);
                } else {
                    return this.params[key];
                }
            } else {
                return this.getAdServer().getParamForSlot(this, key, defaultValue);
            }
        };
        self.AdSlot.prototype.setParam = function(key, value) {
            var data = typeof key === OBJECT ? key : {[key]: value};
            for (var objectKey in data) {
                if (isSecured(objectKey)) { continue; }
                self.trigger('slot.setparam', this, {name: objectKey, value: data[objectKey]});
                this.params[objectKey] = data[objectKey];
            }
            each(this.clones, function(i, clone) { clone.setParam(data); });
            return self;
        };
        self.AdSlot.prototype.setDirectParam = function(key, value) {
            var data = typeof key === OBJECT ? key : {[key]: value};
            for (var objectKey in data) {
                if (isSecured(objectKey)) { continue; }
                self.trigger('slot.setdirectparam', this, {name: objectKey, value: data[objectKey]});
                this.directParams[objectKey] = data[objectKey];
            }
            each(this.clones, function(i, clone) { clone.setDirectParam(data); });
            return self;
        };
        self.AdSlot.prototype.setOnetimeParam = function(key, value) {
            var data = typeof key === OBJECT ? key : {[key]: value};
            for (var objectKey in data) {
                if (isSecured(objectKey)) { continue; }
                self.trigger('slot.setonetimeparam', this, {name: objectKey, value: data[objectKey], onetime: true});
                this.onetimeParams[objectKey] = data[objectKey];
            }
            each(this.clones, function(i, clone) { clone.setOnetimeParam(data); });
            return self;
        };
        self.AdSlot.prototype.removeOnetimeParam = function() {
            var slot = this;
            each(arguments, function(i, key) {
                delete slot.onetimeParams[key];
            });
        };
        self.AdSlot.prototype.clearOnetimeParams = function() {
            this.onetimeParams = {};
        };
        self.AdSlot.prototype.setExpiringParam = function(key, value, expires) {
            if (typeof key === OBJECT) {
                var slot = this,
                    expires = key.expires;
                each(key.params, function(key, value) {
                    slot.setExpiringParam(key, value, expires);
                });
                return self;
            }
            var data = {
                name: key,
                value: value,
                onetime: true,
                expires: (+ new Date()) + expires,
            };
            self.trigger('slot.setexpiringparam', this, data);
            this.expiringParams[data.name] = {value: data.value, expires: data.expires};
            each(this.clones, function(i, clone) { clone.setExpiringParam(key, data.value, expires); });
            return self;
        };
        self.AdSlot.prototype.removeExpiringParam = function() {
            var slot = this;
            each(arguments, function(i, key) {
                delete slot.expiringParams[key];
            });
        };
        self.AdSlot.prototype.clearExpiringParams = function() {
            this.expiringParams = {};
        };
        self.AdSlot.prototype.getExpiringParams = function() {
            var slot = this,
                params = {};
            each(this.expiringParams, function(key, data) {
                if (data.expires && data.expires < +new Date()) {
                    slot.removeExpiringParam(key);
                } else {
                    params[key] = data.value;
                }
            });
            return params;
        };
    
        self.AdSlot.prototype.getConfig = function(key, defaultValue) {
            return this.config.hasOwnProperty(key) ? this.config[key] : defaultValue;
        };
        self.AdSlot.prototype.setConfig = function(key, value) {
            var data = {
                name: key,
                value: value,
            };
            switch (data.name) {
                case 'adserver':
                    data.value = self.AdServer(data.value);
                    break;
                default:
                    break;
            }
            self.trigger('slot.setconfig', this, data);
            this.config[data.name] = data.value;
            each(this.clones, function(i, clone) { clone.setConfig(key, data.value); });
            return self;
        };
    
        self.AdSlot.prototype.setSandboxAttributes = function(attr) {
            if (attr === false) {
                this.sandboxAttributes = {};
                return self;
            }
            this.sandboxAttributes = extend(this.sandboxAttributes, attr);
            return self;
        };
        self.AdSlot.prototype.getSandboxAttributeValue = function(attrs) {
            var attr = Object.keys(attrs || this.sandboxAttributes).filter(function(key) {
                return (attrs || this.sandboxAttributes)[key];
            }.bind(this));
            return attr.join(' ');
        };
        self.AdSlot.prototype.getSandboxAttribute = function(attrs) {
            if (Object.keys(attrs || this.sandboxAttributes).length) {
                return ' sandbox="' + this.getSandboxAttributeValue(attrs) + '"';
            }
            return '';
        };
        self.AdSlot.prototype.getSandboxAttributes = function() {
            return extend({}, self.getSandboxAttributes(), this.sandboxAttributes);
        };
    
        self.AdSlot.prototype.getAdServer = function() {
            return (typeof this.config.adserver !== UNDEFINED) ? self.AdServer(this.config.adserver) : self.getDefaultAdServer();
        };
        self.AdSlot.prototype.setAdServer = function(adserver) {
            this.setConfig('adserver', adserver);
        };
        self.AdSlot.prototype.enable = function() {
            this.setConfig('enabled', true);
            self.trigger('slot.enabled', this);
            return this;
        };
        self.AdSlot.prototype.disable = function(collapseParent) {
            this.setConfig('enabled', false);
            self.trigger('slot.disabled', this);
            if (collapseParent) { self.collapseParentContainer(this); }
            return this;
        };
        self.AdSlot.prototype.isEnabled = function() {
            return this.getConfig('enabled', true);
        };
        self.AdSlot.prototype.isEmpty = function() {
            if (!this.getDomNode()) { return; }
            return this.getAdServer().isEmptyAd(this.getDomNode());
        };
        self.AdSlot.prototype.getUrl = function(requestMethod, callback) {
            const server = this.getAdServer().activate();
            if (typeof requestMethod === FUNCTION) {
                callback = requestMethod;
                requestMethod = undefined;
            }
            if (typeof callback === FUNCTION) {
                const sendResult = function() {
                    callback(server.getUrl(this, requestMethod));
                };
                return self.waitFor('auction.finished', sendResult.bind(this)), undefined;
            }
            return server.getUrl(this, requestMethod);
        };
        self.AdSlot.prototype.getAdSize = function(doc) {
            var server = this.getAdServer();
            return server.getAdSize(doc || this.domNode);
        };
        self.AdSlot.prototype.setFixedSize = function(width, height) {
            if (width && height) {
                this.fixedSize.type = 'fixed';
                this.fixedSize.width = width;
                this.fixedSize.height = height;
            } else if (width === false) {
                this.fixedSize.type = 'pagecontrol';
            } else {
                this.fixedSize.type = 'auto';
            }
            return this;
        };
        self.AdSlot.prototype.getVisibleFraction = function() {
            var dom = this.getDomNode();
            if (!dom || !dom.getBoundingClientRect) { return 0; }
            var rect = dom.getBoundingClientRect(),
                winrect = {
                    width: win.innerWidth || win.clientWidth,
                    height: win.innerHeight || win.clientHeight,
                },
                rectSize = (rect.width || rect.right-rect.left) * (rect.height || rect.bottom-rect.top),
                areaSize = (Math.min(rect.right, winrect.width) - Math.max(rect.left, 0)) * (Math.min(rect.bottom, winrect.height) - Math.max(rect.top, 0));
            log.level('adreq', '[AdSlot.getVisibleFraction]', 'slot', this.id, 'rect', rect, 'winrect', winrect, 'rectSize', rectSize, 'areaSize', areaSize, '+(rect.left >= 0 && rect.right <= winrect.width && rect.top >= 0 && rect.bottom <= winrect.height)', +(rect.left >= 0 && rect.right <= winrect.width && rect.top >= 0 && rect.bottom <= winrect.height));
            return rectSize ? areaSize / rectSize : +(rect.left >= -2 && Math.floor(rect.right)-2 <= winrect.width && rect.top >= -2 && Math.floor(rect.bottom)-2 <= winrect.height);
        };
        self.AdSlot.prototype.isVisible = function(minFraction, precheckConfig = undefined) {
            var precheckConfig = precheckConfig || 'visibility-precheck';
            var preCheck = this.getConfig(precheckConfig, this.getAdServer().getConfig(precheckConfig, self.getConfig(precheckConfig, this.getConfig('visibility-precheck', this.getAdServer().getConfig('visibility-precheck', self.getConfig('visibility-precheck'))))));
            if (typeof preCheck === FUNCTION) {
                if (debugMode) { return true; }
                try {
                    var result = preCheck(this, minFraction);
                    if (typeof result === BOOLEAN) {
                        return result;
                    }
                } catch (e) {
                    log.error('Error in function supplied for AdService.AdSlot(\''+this.id+'\').setConfig(\'visibility-precheck\')', e, arguments, this);
                }
            }
    
            var lookahead = this.getConfig('visibility-lookahead', self.getConfig('visibility-lookahead', false));
    
            if (lookahead && (typeof minFraction !== BOOLEAN || !minFraction)) {
                var domnode = this.getDomNode();
                log.level('adreq', '[AdSlot.isVisible lookahead]', 'slot', this.id, 'lookahead', lookahead, 'domnode', domnode ? domnode.getBoundingClientRect().top <= win.innerHeight + lookahead : false);
                return domnode ? domnode.getBoundingClientRect().top <= win.innerHeight + lookahead : false;
            }
            minFraction = undefined;
    
            log.level('adreq', '[AdSlot.isVisible end]', 'slot', this.id, 'preCheck', preCheck, 'minFraction', minFraction || self.getConfig('visibility-minimum-fraction', 0.5));
            return this.getVisibleFraction() >= (minFraction || self.getConfig('visibility-minimum-fraction', 0.5));
        };
        self.AdSlot.prototype.visibilityCheck = function() {
            if (this.skipCheck) {
                this.skipCheck = !this.skipCheck;
                return true;
            }
            return !this.getConfig('refreshOnlyIfVisible', self.getConfig('refreshOnlyIfVisible', false)) || this.isVisible(true, 'refresh-visibility-precheck');
        };
        self.AdSlot.prototype.clone = function(id, config, params) {
            var slot = new self.AdSlot(id, config, params);
            slot.parent = this;
            slot.config = extend(slot.config, this.config);
            slot.params = extend(slot.params, this.params);
            this.clones.push(slot);
            return slot;
        };
    
        self.AdSlot.prototype.queueAdHtml = function(html, type) {
            this.htmlQueue.push({html: html, type: type});
            return this;
        };
        self.AdSlot.prototype.hasQueuedAdHtml = function() {
            return this.htmlQueue.length > 0;
        };
        self.AdSlot.prototype.getQueuedAdHtml = function() {
            var data = this.htmlQueue.shift();
            this._pendingAdHtml = data.html;
            return data.html;
        };
        self.AdSlot.prototype.getNextQueuedAdType = function() {
            return (this.htmlQueue[0] || [{}]).type;
        };
    
        self.AdSlot.prototype.empty = function() {
            var slot = this;
            return this.modify('empty', function(data) { data.domNode.innerHTML = ''; });
        };
        self.AdSlot.prototype.kill = function() {
            var slot = this;
            return this.modify('kill', function(data) { data.domNode.parentNode.removeChild(data.domNode); }, function(data) { slot.disable(); });
        };
        self.AdSlot.prototype.hide = function(method) {
            var slot = this;
            switch (method) {
                case 'visibility':
                    method = function(data) { data.domNode.style.visibility = 'hidden'; };
                    break;
                case 'display':
                default:
                    if (typeof method !== FUNCTION) {
                        method = function(data) { data.domNode.style.display = 'none'; };
                    }
            }
            return this.modify('hide', method, function(data) { slot.disable(); });
        };
        self.AdSlot.prototype.show = function(method) {
            var slot = this;
            switch (method) {
                case 'visibility':
                    method = function(data) { data.domNode.style.visibility = 'visible'; };
                    break;
                case 'display':
                    method = function(data) { data.domNode.style.display = ''; if (!data.domNode.offsetHeight) { data.domNode.style.display = 'inline-block'; } };
                    break;
                default:
                    if (typeof method !== FUNCTION) {
                        method = function(data) { data.domNode.style.visibility = 'visible'; data.domNode.style.display = ''; if (!data.domNode.offsetHeight) { data.domNode.style.display = 'inline-block'; } };
                    }
            }
            return this.modify('show', method, function(data) { slot.enable(); });
        };
        self.AdSlot.prototype.modify = function(action, func, toggleFunc) {
            var slot = this;
            var data = {
                    action: action, 
                    domNode: slot.getDomNode(),
                    method: func,
                    autoToggleEnabled: true,
                };
            self.trigger('slot.' + action.toLowerCase(), slot, data);
            if (data.domNode) {
                try {
                    data.method(data);
                    if (toggleFunc && data.autoToggleEnabled) {
                        toggleFunc(data);
                    }
                } catch (e) {
                    log.error('Error in function supplied for AdService.modifySlot(slot, \''+action+'\', func, toggleFunc)', e, arguments, data, this);
                }
            }
            return this;
        };
    
        self.AdSlot.prototype.resize = function(width, height, elements) {
            if (this.fixedSize.type === 'pagecontrol') { return; }
            if (this.fixedSize.type === 'fixed') {
                width = this.fixedSize.width;
                height = this.fixedSize.height;
            }
            if (typeof width !== UNDEFINED) {
                if (!/\%/.test(width)) { width += 'px'; }
                this.domNode.style.width = width;
            }
            if (typeof height !== UNDEFINED) {
                if (!/\%/.test(height)) { height += 'px'; }
                this.domNode.style.height = height;
            }
            each(elements, function(i, element) {
                width && (element.style.width = width);
                height && (element.style.height = height);
            });
        };
    
        self.AdSlot.prototype.setTimeout = function(time, note, callback) {
            win.clearTimeout(this.timeout.id);
            if (typeof note === FUNCTION) {
                callback = note;
                note = undefined;
            }
            this.timeout.note = note || 'deferred';
            this.timeout.callback = callback;
            this.timeout.id = win.setTimeout(this.timeoutReached.bind(this), time);
            this.timeout.time = new Date();
            log.level('slottimeout', 'setTimeout', this.id, extend({}, this.timeout), 0);
            return this;
        };
        self.AdSlot.prototype.timeoutReached = function() {
            var timeout = extend({}, this.timeout);
            log.level('slottimeout', 'timeoutReached', this.id, extend({}, this.timeout), new Date()-this.timeout.time);
            this.clearTimeout();
            if (timeout.callback) {
                return timeout.callback.call(this);
            }
            AdService.ad(this.id);
        };
        self.AdSlot.prototype.clearTimeout = function(note) {
            if (note && note !== this.timeout.note) {
                log.level('slottimeout', 'clearTimeout', '( wrong timeout cancel attempt:', note, ')', this.id, extend({}, this.timeout), new Date()-this.timeout.time);
                return false;
            }
            log.level('slottimeout', 'clearTimeout', this.id, extend({}, this.timeout), new Date()-this.timeout.time);
            this.timeout = {
                id: win.clearTimeout(this.timeout.id),
                note: '',
                callback: undefined,
                time: null,
            };
            return true;
        };
        self.AdSlot.prototype.isDeferred = function() {
            return this.timeout.note;
        };
    
        self.AdSlot.prototype.setIndependent = function(isIndependent) {
            this.isIndependent = isIndependent === undefined ? true : isIndependent;
        };
        self.setIndependentSlot = function(slot, isIndependent) {
            return self.AdSlot(slot).setIndependent(isIndependent);
        };
    
        self.AdSlot.prototype._metaData = [];
        self.AdSlot.prototype.clearMetaData = function() {
            this._metaData = [];
            return this;
        };
        self.AdSlot.prototype.addMetaData = function(meta) {
            this._metaData.push(meta);
            return this;
        };
        self.AdSlot.prototype.getMetaData = function() {
            if (this._metaData.length == 0 && typeof this.getAdServer().getMetaData === FUNCTION) {
                this.addMetaData(this.getAdServer().getMetaData(this));
            }
            return this._metaData;
        };
    
    
        self.AdSlot.prototype.getContainerWidth = function() {
            var node = this.getDomNode();
            return node ? node.offsetWidth : undefined;
        };
    
        self.AdSlot.prototype.getCurrentUrl = function() {
            return this.lastRequestedUrl || this.getUrl();
        };
    
        self.findAdSlots = function(rootNode) {
            var attrName = self.getDataAttrName('slot'),
                nodes = rootNode.querySelectorAll('[' + attrName + ']');
            each(nodes, function(index, node) {
                if (node.adSlot) { return; }
                new self.AdSlot(node.getAttribute(attrName), {domNode: node});
            });
            return nodes;
        };
    
        self.findAdSlotByNode = function(node) {
            var attrName = self.getDataAttrName('slot'),
                origNode = node;
            while (!node.hasAttribute(attrName) && node !== doc.documentElement) {
                node = node.parentNode;
            }
            return node.hasAttribute(attrName) ? self.AdSlot(node.getAttribute(attrName)) : self.AdSlot(origNode.id);
        };
    
        self.getVisibleSlots = function(minFraction) {
            var slots = self.AdSlot.getList(),
                visible = [];
            each(slots, function(i, slot) {
                if (slot.isVisible(minFraction)) {
                    visible.push(slot);
                }
            });
            return visible;
        };
    
        self.on('ad.requested', function(evt) {
            if (evt.slot._pendingAdHtml) {
                evt.slot.lastUsedAdHtml = evt.slot._pendingAdHtml;
                evt.slot._pendingAdHtml = null;
            } else {
                evt.slot.lastRequestedUrl = evt.data.url;
            }
        });
        self.on('ad.finished', function(evt) {
            evt.slot.lastRequestSuccessful = true;
        });
    
        self.on('ad.property.passback', function(evt) {
            evt.slot.skipCheck = true;
        });
    }).apply(self);

    (function() {
        var MethodList = new self.ObjectList(),
            storage = [];
    
        self.Communication = new function() {
            this.handlePostMessage = function(evt) {
                var sourceIframe = this.getIframe(evt.source),
                    data = this.parsePostMessage(evt.data);
    
                if (!sourceIframe && evt.source !== parent) {
                    return this.demandRedo(data);
                }
                this.handleMessage(data, sourceIframe.contentWindow || evt.source, evt);
            };
            this.handleMessage = function(message, source, evt) {
                var message = this.normalizeMessage(message),
                    sourceMessage = typeof message.sourceId !== UNDEFINED ? storage[message.sourceId] : undefined;
    
                if (message.type === Const.MESSAGE_TYPE.CALL) {
                    if (source !== win.parent) {
                        var currentAdSlot = self._getCurrentAdSlot(),
                            iframe = this.getIframe(source),
                            callerSlot;
    
                        var copy = iframe.parentNode;
                        var attr = self.getDataAttrName('slot');
                        while (copy !== document.body && !copy.getAttribute(attr)) {
                            copy = copy.parentNode;
                        }
                        if (copy !== document.body) {
                            callerSlot = self._getAdSlot(copy.getAttribute(attr));
                        }
    
                        self._setCurrentAdSlot(callerSlot);
                        try {
                            var method = AdService['_from_iframe_'+message.name] || (MethodList.get(message.name)||{}).func || AdService[message.name],
                                result,
                                result = method.apply({
                                    isPostMessage: true,
                                    message: message,
                                    window: source,
                                    iframe: iframe,
                                    slot: callerSlot,
                                    event: evt,
                                }, makeArray(message.data, true));
                            if (result !== null && source.window !== null) {
                                this.answerMessage(message, source, message.name, result);
                            }
                        } catch (e) {
                            log.error('Communication.handleMessage', 'Error in function request', e, arguments, this);
                        }
                        self._setCurrentAdSlot(currentAdSlot);
                    } else {
                        try {
                            var method = AdService[message.name],
                                result,
                                result = method.apply({
                                    isPostMessage: true,
                                    message: message,
                                    window: source,
                                    iframe: iframe,
                                    slot: callerSlot,
                                    event: evt,
                                }, makeArray(message.data, true));
                            if (result !== null) {
                                this.answerMessage(message, source, message.name, result);
                            }
                        } catch (e) {
                            log.error('Communication.handleMessage', 'Error in function request in IFrame', e, arguments, this);
                        }
                    }
                } else if (message.type === Const.MESSAGE_TYPE.DEMAND_REDO) {
                    var test = storage[message.id];
                    if (test?.name === message.name) {
                        this.sendPostMessage(source, test);
                    }
                }
    
                if (typeof sourceMessage !== UNDEFINED && typeof sourceMessage.callback === FUNCTION) {
                    try {
                        sourceMessage.callback(message.data);
                    } catch (e) {
                        log.error('Communication.handleMessage', 'Error in callback function', e, arguments, this);
                    }
                }
            };
            this.answerMessage = function(sourceMessage, target, name, data) {
                var message = this.createMessage('answer#'+name, data);
                if (typeof sourceMessage !== UNDEFINED && typeof sourceMessage.id !== UNDEFINED) {
                    message.sourceId = sourceMessage.id;
                }
                this._sendMessage(target, message);
            };
            this.sendMessage = function(target, name, data, callback) {
                if (!callback && typeof data === FUNCTION) {
                    callback = data;
                    data = {};
                }
                var message = this.createMessage(name, data, callback);
                this._sendMessage(target, message);
            };
            this._sendMessage = function(target, message) {
                this.createMessageId(message);
                if (target.window === target) {
                    this.sendPostMessage(target, message);
                } else if (target.nodeName?.toLowerCase() === 'iframe') {
                    this.sendPostMessage(target.contentWindow, message);
                } else if (target instanceof HTMLElement) {
                    this.sendDirectMessage(target, message);
                }
            };
            this.sendPostMessage = function(targetWindow, message) {
                message = typeof message === STRING ? message : JSON.stringify(message);
                try {
                    targetWindow.postMessage(message, '*');
                } catch (e) {
                    log.error('Communication.sendPostMessage', 'Error in sending postmessage', e, arguments, this);
                }
            };
            this.sendDirectMessage = function(target, message) {
                var evt = new CustomEvent(message.name, message);
                target.dispatchEvent(evt);
            };
    
            this.addMethod = function(name, func) {
                MethodList.register({
                    id: name,
                    func: func,
                });
                return this;
            };
            this.demandRedo = function(sourceMessage) {
                return;
            };
    
            this.getIframe = function(windowObject) {
                if (!windowObject) { return false; }
                var iframes = doc.getElementsByTagName('iframe');
                for (var i=0, l=iframes.length; i<l; i++) {
                    if (iframes[i].contentWindow === windowObject) {
                        return iframes[i];
                    }
                }
                return false;
            };
            this.parsePostMessage = function(message) {
                try {
                    return JSON.parse(message);
                } catch (e) { }
                return message;
            };
            this.normalizeMessage = function(message) {
                if (typeof message === STRING) {
                    message = {name: message};
                };
                return message;
            };
            this.createMessage = function(name, data, callback) {
                var type = Const.MESSAGE_TYPE.CALL;
                if (name.startsWith('answer#')) {
                    type = Const.MESSAGE_TYPE.ANSWER;
                    name = name.substring('answer#'.length);
                } else if (name.startsWith('demandredo#')) {
                    type = Const.MESSAGE_TYPE.DEMAND_REDO;
                    name = name.substring('demandredo#'.length);
                }
                var message = {
                    type: type,
                    name: name,
                    data: data,
                    callback: callback,
                };
                return message;
            };
            this.createMessageId = function(message) {
                message.id = storage.push(message) - 1;
            };
    
            this.init = function() {
                win.addEventListener('message', function(evt) {
                    const origin = parseUrl(evt.origin).hostname;
                    if (!origin) { return; }
                    if (!(isAcceptedHost(origin) || /^[a-f0-9]{32}\.safeframe\.googlesyndication\.com$/.test(origin))) { return; }
                    self.Communication.handlePostMessage(evt);
                });
            };
            this.init();
        };
    
        self.sendMessage = function() {
            self.Communication.sendMessage.apply(self.Communication, arguments);
        };
    }).apply(self);
    (function() {
        self.Communication.addMethod('resolution', function(message, source, evt) {
            var info = self.info.view();
            return info.screen;
        });
        self.Communication.addMethod('windowSize', function(message, source, evt) {
            var info = self.info.view();
            return info.viewport;
        });
        self.Communication.addMethod('hasCanvas', function(message, source, evt) {
            var info = self.info.features();
            return info.canvas;
        });
        self.Communication.addMethod('os', function(message, source, evt) {
            var info = self.info.ua();
            return info.os.name;
        });
        self.Communication.addMethod('browser', function(message, source, evt) {
            var info = self.info.ua();
            return info.browser.name;
        });
        self.Communication.addMethod('browserVersion', function(message, source, evt) {
            var info = self.info.ua();
            return info.browser.version.majorminor;
        });
        self.Communication.addMethod('adOffset', function(message, source, evt) {
            var info = source.getBoundingClientRect();
            return {x: info.left, y: info.top};
        });
        self.Communication.addMethod('scrollPosition', function(message, source, evt) {
            var info = doc.body.scrollTop;
            return info;
        });
        self.Communication.addMethod('pageHeight', function(message, source, evt) {
            var info = doc.body.offsetHeight;
            return info;
        });
    }).apply(self);

    (function() {
        var head = document.getElementsByTagName('head')[0];
        var style = document.createElement('style');
        var obaIconUrl = 'data:image/png;base64,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';
        style.innerText = '.oba-corner-position{top:0;right:0;}.oba-wrapper{position:relative;}div.oba-wrapper div.oba-panel,div.oba-wrapper div.oba-admarker{font-family:Arial,Verdana,Tahoma,sans-serif;font-size:11px;line-height:1.3;font-weight:normal;font-style:normal;text-align:left;color:#000;}.oba-admarker:hover .oba-admarker-text,.oba-admarker-hover .oba-admarker-text{display:block;white-space:nowrap;}.oba-admarker-icon{background:url('+obaIconUrl+') no-repeat top left #ccc;width:19px;height:15px;position:absolute;cursor:pointer;}div.oba-wrapper div.oba-admarker .oba-admarker-text,div.oba-wrapper div.oba-panel .oba-panel-close{background:#ccc;font-size:10px;line-height:15px;padding:0 5px;position:absolute;cursor:pointer;}.oba-admarker-text{display:none;}.oba-corner-position .oba-admarker-text{right:15px;}';
        head.appendChild(style);
    }).apply(self);
    /*!
     * mustache.js - Logic-less {{mustache}} templates with JavaScript
     * http://github.com/janl/mustache.js
     */
    
    
    (function defineMustache (global, factory) {
      if (typeof exports === 'object' && exports && typeof exports.nodeName !== 'string') {
        factory(exports); 
      } else if (typeof define === 'function' && define.amd) {
        define(['exports'], factory); 
      } else {
        global.Mustache = {};
        factory(global.Mustache); 
      }
    }(this, function mustacheFactory (mustache) {
    
      var objectToString = Object.prototype.toString;
      var isArray = Array.isArray || function isArrayPolyfill (object) {
        return objectToString.call(object) === '[object Array]';
      };
    
      function isFunction (object) {
        return typeof object === 'function';
      }
    
      function typeStr (obj) {
        return isArray(obj) ? 'array' : typeof obj;
      }
    
      function escapeRegExp (string) {
        return string.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
      }
    
      function hasProperty (obj, propName) {
        return obj != null && typeof obj === 'object' && (propName in obj);
      }
    
      function primitiveHasOwnProperty (primitive, propName) {
        return (
          primitive != null
          && typeof primitive !== 'object'
          && primitive.hasOwnProperty
          && primitive.hasOwnProperty(propName)
        );
      }
    
      var regExpTest = RegExp.prototype.test;
      function testRegExp (re, string) {
        return regExpTest.call(re, string);
      }
    
      var nonSpaceRe = /\S/;
      function isWhitespace (string) {
        return !testRegExp(nonSpaceRe, string);
      }
    
      var entityMap = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;',
        '/': '&#x2F;',
        '`': '&#x60;',
        '=': '&#x3D;'
      };
    
      function escapeHtml (string) {
        return String(string).replace(/[&<>"'`=\/]/g, function fromEntityMap (s) {
          return entityMap[s];
        });
      }
    
      var whiteRe = /\s*/;
      var spaceRe = /\s+/;
      var equalsRe = /\s*=/;
      var curlyRe = /\s*\}/;
      var tagRe = /#|\^|\/|>|\{|&|=|!/;
    
      function parseTemplate (template, tags) {
        if (!template)
          return [];
    
        var sections = [];     
        var tokens = [];       
        var spaces = [];       
        var hasTag = false;    
        var nonSpace = false;  
    
        function stripSpace () {
          if (hasTag && !nonSpace) {
            while (spaces.length)
              delete tokens[spaces.pop()];
          } else {
            spaces = [];
          }
    
          hasTag = false;
          nonSpace = false;
        }
    
        var openingTagRe, closingTagRe, closingCurlyRe;
        function compileTags (tagsToCompile) {
          if (typeof tagsToCompile === 'string')
            tagsToCompile = tagsToCompile.split(spaceRe, 2);
    
          if (!isArray(tagsToCompile) || tagsToCompile.length !== 2)
            throw new Error('Invalid tags: ' + tagsToCompile);
    
          openingTagRe = new RegExp(escapeRegExp(tagsToCompile[0]) + '\\s*');
          closingTagRe = new RegExp('\\s*' + escapeRegExp(tagsToCompile[1]));
          closingCurlyRe = new RegExp('\\s*' + escapeRegExp('}' + tagsToCompile[1]));
        }
    
        compileTags(tags || mustache.tags);
    
        var scanner = new Scanner(template);
    
        var start, type, value, chr, token, openSection;
        while (!scanner.eos()) {
          start = scanner.pos;
    
          value = scanner.scanUntil(openingTagRe);
    
          if (value) {
            for (var i = 0, valueLength = value.length; i < valueLength; ++i) {
              chr = value.charAt(i);
    
              if (isWhitespace(chr)) {
                spaces.push(tokens.length);
              } else {
                nonSpace = true;
              }
    
              tokens.push([ 'text', chr, start, start + 1 ]);
              start += 1;
    
              if (chr === '\n')
                stripSpace();
            }
          }
    
          if (!scanner.scan(openingTagRe))
            break;
    
          hasTag = true;
    
          type = scanner.scan(tagRe) || 'name';
          scanner.scan(whiteRe);
    
          if (type === '=') {
            value = scanner.scanUntil(equalsRe);
            scanner.scan(equalsRe);
            scanner.scanUntil(closingTagRe);
          } else if (type === '{') {
            value = scanner.scanUntil(closingCurlyRe);
            scanner.scan(curlyRe);
            scanner.scanUntil(closingTagRe);
            type = '&';
          } else {
            value = scanner.scanUntil(closingTagRe);
          }
    
          if (!scanner.scan(closingTagRe))
            throw new Error('Unclosed tag at ' + scanner.pos);
    
          token = [ type, value, start, scanner.pos ];
          tokens.push(token);
    
          if (type === '#' || type === '^') {
            sections.push(token);
          } else if (type === '/') {
            openSection = sections.pop();
    
            if (!openSection)
              throw new Error('Unopened section "' + value + '" at ' + start);
    
            if (openSection[1] !== value)
              throw new Error('Unclosed section "' + openSection[1] + '" at ' + start);
          } else if (type === 'name' || type === '{' || type === '&') {
            nonSpace = true;
          } else if (type === '=') {
            compileTags(value);
          }
        }
    
        openSection = sections.pop();
    
        if (openSection)
          throw new Error('Unclosed section "' + openSection[1] + '" at ' + scanner.pos);
    
        return nestTokens(squashTokens(tokens));
      }
    
      function squashTokens (tokens) {
        var squashedTokens = [];
    
        var token, lastToken;
        for (var i = 0, numTokens = tokens.length; i < numTokens; ++i) {
          token = tokens[i];
    
          if (token) {
            if (token[0] === 'text' && lastToken && lastToken[0] === 'text') {
              lastToken[1] += token[1];
              lastToken[3] = token[3];
            } else {
              squashedTokens.push(token);
              lastToken = token;
            }
          }
        }
    
        return squashedTokens;
      }
    
      function nestTokens (tokens) {
        var nestedTokens = [];
        var collector = nestedTokens;
        var sections = [];
    
        var token, section;
        for (var i = 0, numTokens = tokens.length; i < numTokens; ++i) {
          token = tokens[i];
    
          switch (token[0]) {
            case '#':
            case '^':
              collector.push(token);
              sections.push(token);
              collector = token[4] = [];
              break;
            case '/':
              section = sections.pop();
              section[5] = token[2];
              collector = sections.length > 0 ? sections[sections.length - 1][4] : nestedTokens;
              break;
            default:
              collector.push(token);
          }
        }
    
        return nestedTokens;
      }
    
      function Scanner (string) {
        this.string = string;
        this.tail = string;
        this.pos = 0;
      }
    
      Scanner.prototype.eos = function eos () {
        return this.tail === '';
      };
    
      Scanner.prototype.scan = function scan (re) {
        var match = this.tail.match(re);
    
        if (!match || match.index !== 0)
          return '';
    
        var string = match[0];
    
        this.tail = this.tail.substring(string.length);
        this.pos += string.length;
    
        return string;
      };
    
      Scanner.prototype.scanUntil = function scanUntil (re) {
        var index = this.tail.search(re), match;
    
        switch (index) {
          case -1:
            match = this.tail;
            this.tail = '';
            break;
          case 0:
            match = '';
            break;
          default:
            match = this.tail.substring(0, index);
            this.tail = this.tail.substring(index);
        }
    
        this.pos += match.length;
    
        return match;
      };
    
      function Context (view, parentContext) {
        this.view = view;
        this.cache = { '.': this.view };
        this.parent = parentContext;
      }
    
      Context.prototype.push = function push (view) {
        return new Context(view, this);
      };
    
      Context.prototype.lookup = function lookup (name) {
        var cache = this.cache;
    
        var value;
        if (cache.hasOwnProperty(name)) {
          value = cache[name];
        } else {
          var context = this, intermediateValue, names, index, lookupHit = false;
    
          while (context) {
            if (name.indexOf('.') > 0) {
              intermediateValue = context.view;
              names = name.split('.');
              index = 0;
    
              while (intermediateValue != null && index < names.length) {
                if (index === names.length - 1)
                  lookupHit = (
                    hasProperty(intermediateValue, names[index])
                    || primitiveHasOwnProperty(intermediateValue, names[index])
                  );
    
                intermediateValue = intermediateValue[names[index++]];
              }
            } else {
              intermediateValue = context.view[name];
    
              lookupHit = hasProperty(context.view, name);
            }
    
            if (lookupHit) {
              value = intermediateValue;
              break;
            }
    
            context = context.parent;
          }
    
          cache[name] = value;
        }
    
        if (isFunction(value))
          value = value.call(this.view);
    
        return value;
      };
    
      function Writer () {
        this.cache = {};
      }
    
      Writer.prototype.clearCache = function clearCache () {
        this.cache = {};
      };
    
      Writer.prototype.parse = function parse (template, tags) {
        var cache = this.cache;
        var cacheKey = template + ':' + (tags || mustache.tags).join(':');
        var tokens = cache[cacheKey];
    
        if (tokens == null)
          tokens = cache[cacheKey] = parseTemplate(template, tags);
    
        return tokens;
      };
    
      Writer.prototype.render = function render (template, view, partials, tags) {
        var tokens = this.parse(template, tags);
        var context = (view instanceof Context) ? view : new Context(view);
        return this.renderTokens(tokens, context, partials, template, tags);
      };
    
      Writer.prototype.renderTokens = function renderTokens (tokens, context, partials, originalTemplate, tags) {
        var buffer = '';
    
        var token, symbol, value;
        for (var i = 0, numTokens = tokens.length; i < numTokens; ++i) {
          value = undefined;
          token = tokens[i];
          symbol = token[0];
    
          if (symbol === '#') value = this.renderSection(token, context, partials, originalTemplate);
          else if (symbol === '^') value = this.renderInverted(token, context, partials, originalTemplate);
          else if (symbol === '>') value = this.renderPartial(token, context, partials, tags);
          else if (symbol === '&') value = this.unescapedValue(token, context);
          else if (symbol === 'name') value = this.escapedValue(token, context);
          else if (symbol === 'text') value = this.rawValue(token);
    
          if (value !== undefined)
            buffer += value;
        }
    
        return buffer;
      };
    
      Writer.prototype.renderSection = function renderSection (token, context, partials, originalTemplate) {
        var self = this;
        var buffer = '';
        var value = context.lookup(token[1]);
    
        function subRender (template) {
          return self.render(template, context, partials);
        }
    
        if (!value) return;
    
        if (isArray(value)) {
          for (var j = 0, valueLength = value.length; j < valueLength; ++j) {
            buffer += this.renderTokens(token[4], context.push(value[j]), partials, originalTemplate);
          }
        } else if (typeof value === 'object' || typeof value === 'string' || typeof value === 'number') {
          buffer += this.renderTokens(token[4], context.push(value), partials, originalTemplate);
        } else if (isFunction(value)) {
          if (typeof originalTemplate !== 'string')
            throw new Error('Cannot use higher-order sections without the original template');
    
          value = value.call(context.view, originalTemplate.slice(token[3], token[5]), subRender);
    
          if (value != null)
            buffer += value;
        } else {
          buffer += this.renderTokens(token[4], context, partials, originalTemplate);
        }
        return buffer;
      };
    
      Writer.prototype.renderInverted = function renderInverted (token, context, partials, originalTemplate) {
        var value = context.lookup(token[1]);
    
        if (!value || (isArray(value) && value.length === 0))
          return this.renderTokens(token[4], context, partials, originalTemplate);
      };
    
      Writer.prototype.renderPartial = function renderPartial (token, context, partials, tags) {
        if (!partials) return;
    
        var value = isFunction(partials) ? partials(token[1]) : partials[token[1]];
        if (value != null)
          return this.renderTokens(this.parse(value, tags), context, partials, value);
      };
    
      Writer.prototype.unescapedValue = function unescapedValue (token, context) {
        var value = context.lookup(token[1]);
        if (value != null)
          return value;
      };
    
      Writer.prototype.escapedValue = function escapedValue (token, context) {
        var value = context.lookup(token[1]);
        if (value != null)
          return mustache.escape(value);
      };
    
      Writer.prototype.rawValue = function rawValue (token) {
        return token[1];
      };
    
      mustache.name = 'mustache.js';
      mustache.version = '3.0.1';
      mustache.tags = [ '{{', '}}' ];
    
      var defaultWriter = new Writer();
    
      mustache.clearCache = function clearCache () {
        return defaultWriter.clearCache();
      };
    
      mustache.parse = function parse (template, tags) {
        return defaultWriter.parse(template, tags);
      };
    
      mustache.render = function render (template, view, partials, tags) {
        if (typeof template !== 'string') {
          throw new TypeError('Invalid template! Template should be a "string" ' +
                              'but "' + typeStr(template) + '" was given as the first ' +
                              'argument for mustache#render(template, view, partials)');
        }
    
        return defaultWriter.render(template, view, partials, tags);
      };
    
      mustache.to_html = function to_html (template, view, partials, send) {
    
        var result = mustache.render(template, view, partials);
    
        if (isFunction(send)) {
          send(result);
        } else {
          return result;
        }
      };
    
      mustache.escape = escapeHtml;
    
      mustache.Scanner = Scanner;
      mustache.Context = Context;
      mustache.Writer = Writer;
    
      return mustache;
    }));

    (function() {
        var config = {
            enabled: true,
            iframeUrl: undefined,
            timeout: {auctionIframe: 1000, initial: 1000, slots: [1000], config: 5000},
            disableonadblock: true,
            expires: 10000,
            maxTries: 3,
        };
        var auctionIframe = null;
        var auctionResults = {};
        var copiedAuctionResults = {};
        const timeoutCounter = {};
        var slotStack = {
            initial: {},
            delayed: {},
            called: {},
            isRefresh: {},
        };
        var adblockDetected = false;
        var configTimeout = setTimeout(function() {
            self.triggerOnce('auction.end');
        }, config.timeout.config);
    
        function auctionCommunicator(method, data, slot) {
            self.Communication.sendMessage(slot?.getDomNode().querySelector('iframe') || auctionIframe.contentWindow, method, data);
        }
    
        function callSlot(slot, isInitial) {
            if (slotStack.called[slot]) {
                if (Object.keys(slotStack.initial).length === 0 || isInitial) {
                    self.ad(slot, slotStack.isRefresh[slot] || false);
                } else {
                    slotStack.delayed[slot] = true;
                }
            }
        }
    
        function reviveSlots() {
            each(slotStack.delayed, function(slot) {
                delete slotStack.delayed[slot];
                callSlot(slot);
            });
        }
    
        var inits = {
            disableonadblock: function(value, config) {
                if (!value) { return; }
                if (!config.enabled) { return; }
                var isBlocked = self.info.abd().isBlocked;
                if (isBlocked) {
                    self.disableAuctions();
                }
            },
            slotSets: function(value, config) {
                config.slotConfig = {};
                each(value, function(i, slotSet) {
                    if (slotSet instanceof Array) {
                        each(slotSet, function(i, slot) {
                            config.slotConfig[slot] = slotSet;
                        });
                    } else {
                        config.slotConfig[slotSet] = [slotSet];
                    }
                });
                config.prebidAvailableSlots = Object.keys(config.slotConfig);
            },
            passover: function(value, config) {
                if (!value) { return; }
                value.initialSlots = config.initial;
            },
            initial: function(value, config) {
                each(value, function(i, slot) {
                    slotStack.initial[slot] = true;
                });
            },
            iframeUrl: function(value, config) {
    log('iframeUrl', value, config);
                if (!self.auctionsIsEnabled()) { return; }
                self.registerWaitingQueue('auction.finished');
                if (!value) { return; }
                if (auctionIframe) { return; }
                var passOverData = config.passover;
                var namespace = self.getConfig('data-attribute-namespace');
                let sandbox = '';
                if (config.sandbox) {
                    sandbox = 'sandbox="allow-forms allow-same-origin allow-scripts"';
                }
                var html = `<iframe src="${Code.html(value)}" name="${Code.html(JSON.stringify(passOverData))}" data-${namespace}-auction="true"
                    ${sandbox} frameborder="0" border="0" scrolling="no" marginwidth="0" marginheight="0" width="1" height="1"
                    style="position: fixed; top: -1000px; left: -1000px;"><\/iframe>`;
                doc.body.insertAdjacentHTML('afterbegin', html);
                auctionIframe = doc.body.querySelector(`[data-${namespace}-auction="true"]`);
                log.level('auction', 'Auction IFrame created', html);
                if (config.timeout?.auctionIframe) {
                    each(config.initial, function(i, slot) {
    log('initializing timeout', slot);
                        self.AdSlot(slot).setTimeout(config.timeout.auctionIframe, 'auctionIframe', function() {
    log.error('auctionIframe', 'timeout reached', this);
                            self.disableAuctions();
                            delete slotStack.initial[slot];
                            callSlot(this.id, true);
                            if (i === config.initial.length-1) { reviveSlots(); }
                        });
                    });
                }
    
                self.on('ad.begin', function(evt) {
                    if (!config.enabled) { return; }
                    if (adblockDetected) { return; }
                    if (!config.slotConfig.hasOwnProperty(evt.slot.id)) { return; }
                    if (!auctionIframe) { return; }
                    if (evt.slot.hasQueuedAdHtml()) {
                        if (Array.isArray(config.maxWidth) && config.maxWidth.includes(evt.slot.id)) {
                            delete config.maxWidth;
                            reviveSlots();
                        }
                        return;
                    }
                    if (auctionResults[evt.slot.id]) {
                        if (auctionResults[evt.slot.id].param && auctionResults[evt.slot.id].expires < +new Date()) {
                            evt.slot.removeExpiringParam(Object.keys(auctionResults[evt.slot.id].param));
                            delete auctionResults[evt.slot.id];
                        } else {
                            return;
                        }
                    }
                    if (evt.slot.getConfig('skipped')) { return; }
                    var maxWidth = {},
                        refreshInformation = {},
                        slots = config.slotConfig[evt.slot.id];
                    evt.data.deferred = true;
                    slotStack.called[evt.slot.id] = true;
                    if (evt.slot.isDeferred() !== 'auctionResult' && Array.isArray(config.maxWidth)) {
                        maxWidth['maxWidth'] = {};
                        for (index in config.maxWidth) {
                            var tagid = config.maxWidth[index];
                            if (!slots.includes(tagid)) { continue; }
                            if (!self.AdSlot(tagid).isEnabled()) { continue; }
                            maxWidth['maxWidth'][tagid] = self.AdSlot(tagid).getParam('maxwidth') || self.AdSlot(tagid).getContainerWidth();
                        }
                        for (tagid in maxWidth['maxWidth']) {
                            if (!maxWidth['maxWidth'][tagid]) {
                                slotStack.delayed[evt.slot.id] = true;
                                evt.slot.setTimeout(1000, 'maxWidthTimeout', function() { 
                                    log('maxWidthTimeout triggered', slots);
                                    delete config.maxWidth;
                                    reviveSlots();
                                });
                                return;
                            } else {
                                reviveSlots();
                            }
                        }
                    }
                    if (evt.data.isRefresh) {
                        slotStack.isRefresh[evt.slot.id] = true;
                        slots = config.prebidAvailableSlots.includes(evt.slot.id) && evt.slot.visibilityCheck() ? function(sets) { 
                            for (timer in sets) {
                                if (sets[timer].includes(evt.slot.id)) { return sets[timer].filter(function(slotId) { return config.prebidAvailableSlots.includes(slotId) && self._getAdSlot(slotId).visibilityCheck(); }); }
                            }
                            return slots;
                        }(self.getConfig('intervalSets', {})) : slots;
                        slots.forEach(function(slot, index) {
                            slot = self._getAdSlot(slot);
                            if (!self.getDefaultAdServer().isTagIdSame(slot)) {
                                refreshInformation[slot] = true;
                            }
                            if (slot.getConfig('skipped')) {
                                slots.splice(index, 1);
                            }
                        });
                    }
                    if (!auctionIframe.ready) {
                        if (evt.slot.isDeferred()) { return; }
                        slotStack.delayed[evt.slot.id] = true;
                        return;
                    }
                    if (evt.slot.isDeferred() !== 'auctionResult') {
                        var message = extend({}, {slots: slots}, maxWidth);
                        if (Object.keys(refreshInformation).length > 0) {
                            message = extend(message, {refresh: refreshInformation});
                        }
                        self.Communication.sendMessage(auctionIframe.contentWindow, 'getAuctionForSlots', message);
                        each(slots, function(i, name) {
                            config.slotConfig[name] = [name]; 
                            self.AdSlot(name).setTimeout(
                                config.timeout.slots[Math.min(slots.length, config.timeout.slots.length)-1],
                                'auctionResult',
                                function() {
                                    log.error('auctionResult slotSets', 'timeout reached', name, this);
                                    delete slotStack.initial[name];
                                    auctionResults[name] = {};
                                    if (config.maxTries) {
                                        timeoutCounter[name] = timeoutCounter[name] ?? 0;
                                        timeoutCounter[name]++;
                                    }
                                    callSlot(name);
                                },
                            );
                        });
                    }
                });
                self.on('ad.finished', function(evt) {
                    if (auctionResults[evt.slot.id]?.expires < +new Date()) {
                        delete auctionResults[evt.slot.id];
                    }
                    if (config.maxTries && timeoutCounter[evt.slot.id] <= config.maxTries) {
                        self.removeEmptyAuctionResult(evt.slot.id);
                    }
                    delete slotStack.called[evt.slot.id];
                });
            },
        };
    
        self.on('adblock.detected', function(evt) {
            self.cancelAuctions();
            self.registerWaitingQueue('auction.finished');
            self.waitingComplete('auction.finished');
            self.triggerOnce('auction.end');
    log('blocker detected - auction.finished', evt);
        });
    
        self._from_iframe_copyAuctionResult = function(from, to) {
            if (!from || !to || (from === to)) { return; }
            from = self._getAdSlot(from);
            if (!auctionResults[from.id] || copiedAuctionResults[from.id]) { return; }
            to = self._getAdSlot(to);
            to.timeout.note = 'auctionResult';
    
                    self.setAuctionResult({
                [to.id]: auctionResults[from.id],
            });
            copiedAuctionResults[to.id] = from.id;
            return;
        }
    
        self.getAuctionConfig = function(name, defaultValue) {
            if (!name) { return config; }
            if (config.hasOwnProperty(name)) { return config[name]; }
            return defaultValue;
        };
        self.setAuctionConfig = function(obj) {
            clearTimeout(configTimeout);
            if (typeof obj === FUNCTION) {
                try {
                    obj = obj();
                } catch (e) {
                    return log.error('Error in function supplied for AdService.setAuctionConfig()', e, arguments, this);
                }
            }
            config = extend(config, obj);
            each(inits, function(name, value) {
                if (typeof value !== FUNCTION) { return; }
                value(config[name], config);
            });
            log.level('auction', 'Auction config set', 'New values:', obj, 'Resulting config:', config);
    log('setAuctionConfig end', config);
            return self;
        };
    
        self.enableAuctions = function() {
            config.enabled = true;
        };
        self.disableAuctions = function() {
            self.triggerOnce('auction.end');
            config.enabled = false;
        };
        self.auctionsIsEnabled = function() {
            if (!config.enabled) { self.triggerOnce('auction.end'); }
            return config.enabled;
        };
    
        self.cancelAuctions = function() {
            const calledSlots = Object.keys(slotStack.called);
            const initialSlots = Object.keys(slotStack.initial);
            const timeoutSlots = calledSlots.concat(initialSlots.filter((initialSlot) => !calledSlots.includes(initialSlot)));
            each(timeoutSlots, function(i, slot) {
                if (auctionIframe?.ready) { self.AdSlot(slot).clearTimeout('auctionResult'); }
                self.AdSlot(slot).clearTimeout('auctionIframe');
                callSlot(slot, true);
            });
            slotStack.initial = slotStack.called = {};
            self.waitingComplete('auction.finished');
            self.disableAuctions();
            adblockDetected = true;
            reviveSlots();
        };
    
        self.auctionBlockedByAdBlock = function() {
            return adblockDetected;
        };
    
        self.initiateNativeTracking = function(data, slot) {
            return self._runOutsourceable('initiateNativeTracking', self, arguments, self.getDefaultAdServer());
        };
    
        self.sendNativeTrackingInfo = function(data) {
            if (!data) { return self; }
            auctionIframe.contentWindow.postMessage(data, '*');
        };
    
        self.auctionIframeReady = function() {
            if (auctionIframe.ready) { return; }
            if (adblockDetected) { return; }
            if (!self.auctionsIsEnabled()) { return self.enableAuctions(); }
            auctionIframe.ready = true;
            each(config.initial, function(i, slot) {
    log('initializing timeout', slot);
                self.AdSlot(slot).clearTimeout('auctionIframe');
                self.AdSlot(slot).setTimeout(config.timeout.initial, 'auctionResult', function() {
    log.error('auctionResult initial', 'timeout reached', slot, this);
                    self.waitingComplete('auction.finished');
                    self.triggerOnce('auction.end');
                    delete slotStack.initial[slot];
                    callSlot(slot, true);
                });
                if (i === config.initial.length-1) { reviveSlots(); }
            });
        };
    
        self.removeEmptyAuctionResult = function(slot) {
            var slot = self._getAdSlot(slot);
            if (auctionResults[slot.id] && Object.keys(auctionResults[slot.id]).length > 0) { return; }
            delete auctionResults[slot.id];
        };
    
        self.setAuctionResult = function(obj) {
            log('setAuctionResult start', extend({}, obj), extend({}, auctionResults));
            var beforeAuctionIframeReady = false,
                expires = +new Date() + config.expires;
    
            if (!auctionIframe.ready) {
                beforeAuctionIframeReady = true;
                self.auctionIframeReady();
            }
            each(obj, function(slot, data) {
                slot = self.AdSlot(slot);
                if (slot.isDeferred() !== 'auctionResult') { return; }
                auctionResults[slot.id] = data;
                slot.setExpiringParam({params: data.param, expires: config.expires});
                auctionResults[slot.id].expires = expires;
            });
            each(obj, function(slot, data) {
                var isInitial = !!slotStack.initial[slot];
                delete slotStack.initial[slot];
                if (!self.AdSlot(slot).clearTimeout('auctionResult')) { return; }
                if (!(beforeAuctionIframeReady && !!self.AdSlot(slot).lastRequestedUrl)) {
                    callSlot(slot, isInitial);
                }
            });
            log.level('auction', 'setAuctionResult', extend({}, obj), extend({}, auctionResults));
            if (Object.keys(slotStack.initial).length === 0) {
                reviveSlots();
            }
    log('setAuctionResult end', extend({}, auctionResults));
            self.waitingComplete('auction.finished');
            self.triggerOnce('auction.end');
        };
    
        self.applyAuction = function(type, obj = {}) {
    log('applyAuction start', type, obj, arguments, this);
            log.level('auction', 'AdServer selected auction result to be used', this);
            var slot = self._getAdSlot(this.slot);
            slot.clearExpiringParams();
            if (!!slot.domNode.dataset.prioSlot) {
                auctionResults[slot.id] = auctionResults[self.getAuctionConfig().initial[0]];
                slot['tempId'] = self.getAuctionConfig().initial[0];
            }
            if (!auctionResults[slot.id] || !auctionResults[slot.id].type || !auctionResults[slot.id].type[type]) {
                log.level('auction', 'Empty applyAuction', slot, this);
                return;
            }
            var result = auctionResults[slot.id].type[type];
            result = extend({}, result, obj);
            if (type === 'prebid') {
                var resultData = function(resultList, resultId) {
                    if (typeof resultId !== UNDEFINED) {
                        for (var resultType in resultList) {
                            if (resultList[resultType][resultId]) { return {key: resultType, code: resultList[resultType][resultId]}; }
                        }
                    }
                    if (resultList.html?.default) { return {key: 'html', code: resultList.html.default}; }
                    var resultKey = resultList.json ? 'json' : 'html';
                    return {key: resultKey, code: resultList[resultKey]};
                }(auctionResults[slot.id].type[type], obj.id);
                switch (resultData.key) {
                    case 'json':
                        self.Communication.sendMessage(slot.getDomNode().querySelector('iframe').contentWindow, 'auctionResultJson', {slot: slot.id, json: resultData.code});
                        if (result.adId) {
                            self.initiateNativeTracking({message: 'Prebid Native', adId: result.adId}, slot.getDomNode());
                        }
                        break;
                    case 'html':
                        self.Communication.sendMessage(slot.getDomNode().querySelector('iframe').contentWindow, 'auctionResultHtml', {size: {width: result.size.width, height: result.size.height}, html: resultData.code, skipRender: !!result.skipRender});
                        break;
                    default:
                        break;
                }
            }
            if (!!result.size) {
                slot.resize(undefined, result.size.height, [this.iframe]);
            }
    
            self.Communication.sendMessage(auctionIframe.contentWindow, 'auctionSuccess', {slot: slot.tempId || slot.id, type: type, id: obj.id});
            const parentSlot = copiedAuctionResults[slot.id];
            if (parentSlot) { 
                delete copiedAuctionResults[slot.id];
                delete auctionResults[parentSlot];
            }
            delete auctionResults[slot.id];
        };
    
        var viewabilityStack = {};
    
        self.applyViewability = function() {
            var slot = self._getAdSlot(this.slot),
                domNode = slot.getDomNode(),
                iframe = domNode.querySelector('iframe'),
                message = {
                    'event': 'parentFrameGeometryChange',
                    'scrollY': window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 1,
                    'offsetTop': domNode.offsetTop,
                    'windowHeight': window.innerHeight,
                };
            if (AdService.AdSlot(slot).getVisibleFraction() > 0) {
                iframe.contentWindow.postMessage(message, '*');
            }
    
            if (viewabilityStack[slot.id]) { return; }
            viewabilityStack[slot.id] = true;
    
            document.addEventListener('scroll', function(e) {
                if (AdService.AdSlot(slot).getVisibleFraction() > 0) {
                    var message = {
                        'event': 'parentFrameGeometryChange',
                        'scrollY': window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 1,
                        'offsetTop': domNode.offsetTop,
                        'windowHeight': window.innerHeight,
                    };
                    iframe.contentWindow.postMessage(message, '*');
                }
            });
        };
    
    }).apply(self);


    (function() {
        var defaultAdServer;
        self.setDefaultAdServer = function(adserver) {
            defaultAdServer = self.AdServer(adserver);
            return self;
        };
        self.getDefaultAdServer = function() {
            return defaultAdServer;
        };
        self.enableServer = function(server) {
            server = server ? self.AdServer(server) : self._getCurrentAdSlot().getAdServer();
            server.enable();
            return self;
        };
        self.disableServer = function(server) {
            server = server ? self.AdServer(server) : self._getCurrentAdSlot().getAdServer();
            server.disable();
            return self;
        };
        self.newPageContext = function(wi) {
            each(self.AdServer.getList(), function(i, server) {
                server.newPageContext(wi);
            });
            return self;
        };
        self.getAcAndIncrement = function() {
            server = self.getDefaultAdServer();
            return server.getAcAndIncrement();
        };
    }).apply(self);

    (function() {
        self.setAdHook = function(type, func) {
            self.on('adhook.' + type, func);
            return self;
        };
        self.createAd = function(obj) {
            obj = obj || {};
            var type = obj.type || '_unknown',
                slot = self._getCurrentAdSlot(),
                meta = slot?.getMetaData()[0] || {};
            self._triggerFlat('adhook._global:prepare', obj, meta, slot);
            self._triggerFlat(self.hasEventHandler('adhook.' + type + ':prepare') ? ('adhook.' + type + ':prepare') : ('adhook._default:prepare'), obj, meta, slot);
            self._triggerFlat('adhook._global', obj, meta, slot);
            self._triggerFlat('adhook._global:after', obj, meta, slot);
            self._triggerFlat(self.hasEventHandler('adhook.' + type) ? ('adhook.' + type) : ('adhook._default'), obj, meta, slot);
            self._triggerFlat(self.hasEventHandler('adhook.' + type + ':after') ? ('adhook.' + type + ':after') : ('adhook._default:after'), obj, meta, slot);
            self._triggerFlat('adhook.:finished', obj, meta, slot);
        };
    
        var adPropertyFunctions = {
            'resize': {
                pre: function(property, slot, data) {
                    var adformatInformation = self.info.adformat(),
                        resizePrecheck = slot.getConfig('resize-precheck', slot.getAdServer().getConfig('resize-precheck', self.getConfig('resize-precheck')));
                    if (typeof resizePrecheck === FUNCTION) {
                        try {
                            var result = resizePrecheck(slot, data, adformatInformation);
                            data = result;
                        } catch (e) {
                            self.log.error('Error in function supplied for \'resize-precheck\'', e, arguments, slot);
                        }
                    }
                },
                post: function(property, slot, data) {
                    slot.setConfig('auto-set-iframe-size', false);
                },
            },
        };
        self.setAdProperty = function(property) {
            if (!property) {
                log.error('setAdProperty', 'No property given');
                return self;
            }
            var slot = this instanceof self.AdSlot ? this : self._getCurrentAdSlot(),
                data = {
                    arguments: Array.prototype.slice.call(arguments, 1),
                };
            data[property] = arguments[1];
            if (adPropertyFunctions[property]?.pre) {
                adPropertyFunctions[property].pre(property, slot, data);
            }
            if (data.stopAction) {
                self.trigger('ad.property.' + property + '.stopped', slot, data);
                return self;
            }
            self.trigger('ad.property.' + property, slot, data);
            if (adPropertyFunctions[property]?.post) {
                adPropertyFunctions[property].post(property, slot, data);
            }
            return self;
        };
    
        self.expandAd = function(data) {
            var slot = self._getCurrentAdSlot();
            return self._from_iframe_expandAd.apply({
                isPostMessage: false,
                message: null,
                window: win,
                iframe: slot.getDomNode()?.getElementsByTagName('iframe')[0],
                slot: slot,
                event: null,
            }, makeArray(arguments, true));
        };
        self._from_iframe_expandAd = function(data) { 
            data = extend({width: this.iframe.style.width, height: this.iframe.style.height}, data);
            var originalSize = {}; 
    
            if (this.iframe.getAttribute('data-_ad-expandad-width')) {
                originalSize = {
                    width: this.iframe.getAttribute('data-_ad-expandad-width'),
                    height: this.iframe.getAttribute('data-_ad-expandad-height'),
                };
            } else {
                originalSize = {
                    width: this.iframe.offsetWidth,
                    height: this.iframe.offsetHeight,
                };
            }
    
            data.done = false;
            data.isExpandAllowed = true;
    
            self.trigger('addemand.expand', self._getCurrentAdSlot(), data);
    
            if (data.isExpandAllowed && !data.done) {
                this.iframe.setAttribute('data-_ad-expandad-width', originalSize.width);
                this.iframe.setAttribute('data-_ad-expandad-height', originalSize.height);
                this.iframe.style.width = getLengthValue(data.width);
                this.iframe.style.height = getLengthValue(data.height);
                data.done = true;
            }
            if (!data.isExpandAllowed) { 
                data.done = false;
            }
            self.Communication.answerMessage(data, this.iframe.contentWindow, 'expandAd', data);
        };
    
        self._from_iframe_contractAd = function(data) {
            data.done = false;
            data.isContractAllowed = true;
            self.trigger('addemand.contract', self._getCurrentAdSlot(), data);
    
            if (data.isContractAllowed && !data.done) {
                if (this.iframe.getAttribute('data-_ad-expandad-width')) {
                    this.iframe.style.width = this.iframe.getAttribute('data-_ad-expandad-width')+'px' || '';
                    this.iframe.style.height = this.iframe.getAttribute('data-_ad-expandad-height')+'px' || '';
                    this.iframe.removeAttribute('data-_ad-expandad-width');
                    this.iframe.removeAttribute('data-_ad-expandad-height');
                    data.done = true;
                }
            }
            self.Communication.answerMessage(data, this.iframe.contentWindow, 'contractAd', data);
        };
    
        self._from_iframe_closeAd = function(data) {
            data.done = false;
            self.trigger('addemand.close', self._getCurrentAdSlot(), data);
            if (data.isCloseAllowed) {
                self.killSlot(this.slot);
            } else {
                self.Communication.answerMessage(data, this.iframe.contentWindow, 'closeAd', data);
            }
        };
    
        self._from_iframe_openLayer = function(data) {
            data.done = false;
            self.trigger('addemand.openlayer', self._getCurrentAdSlot(), data);
            self.Communication.answerMessage(data, this.iframe.contentWindow, 'openLayer', data);
        };
    
        self._from_iframe_closeLayer = function(data) {
            data.done = false;
            self.trigger('addemand.closelayer', self._getCurrentAdSlot(), data);
            self.Communication.answerMessage(data, this.iframe.contentWindow, 'closeLayer', data);
        };
    }).apply(self);

    (function() {
    
        var exclusionRuleSet = {};
    
        self.setExclusionRuleSet = function(obj) {
            exclusionRuleSet = obj;
            each(obj, function(adform, properties) {
                if (adform === '_prio') { return; }
                self.on(
                    'ad.finished',
                    (function(adform) { return function(evt) {
                        var slot = evt.slot || self._getCurrentAdSlot(),
                            adformByMeta = slot.getAdServer().getAdFormByMetaData(slot.getMetaData()[0], true);
                        if (!adformByMeta && slot.getParam('isEmpty', false)) {
                            adformByMeta = 'isEmpty';
                        }
                        return adform === adformByMeta;
                    }; })(adform),
                    generateRuleRunnerFunction(adform, properties),
                );
            });
            if (obj._prio) {
                deferAdRequests = true;
                self.registerWaitingQueue('prioad');
                startPrioMechanism(obj._prio);
            }
            return self;
        };
        self.getExclusionRuleSet = function() {
            return exclusionRuleSet;
        };
    
        var deferAdRequests = false,
            deferredAdSlots = [];
        function startPrioMechanism(prio) {
            self.on('ad.begin', function(evt) {
                if (!deferAdRequests) { return; }
                if (evt.slot.id !== prio) {
                    evt.data.deferred = true;
                    if (deferredAdSlots.includes(evt.slot)) {
                        return;
                    }
                    deferredAdSlots.push(evt.slot);
                    self.waitFor('prioad', function() {
                        if ((evt.slot.lastRequestedUrl || evt.slot.lastUsedAdHtml) && evt.slot.lastRequestSuccessful) { return; }
                        self.ad(evt.slot);
                    });
                }
            });
            self.on('ad.finished', prio, function(evt) {
                deferAdRequests = false;
                self.trigger('ad.priofinished', evt.slot);
                self.waitingComplete('prioad');
                deferredAdSlots = [];
            });
        }
    
        self.getExclusionRules = function(adform, slot) {
            for (var i in exclusionRuleSet) {
                if (!exclusionRuleSet.hasOwnProperty(i)) { continue; }
                if (adform === '_prio') { continue; }
                var properties = exclusionRuleSet[i];
                if (!(properties instanceof Array)) { properties = [properties]; }
                for (var j=0, k=properties.length; j<k; j++) {
                    var data = properties[j];
                    if (!checkConditions(data.conditions, {slot: slot})) { continue; }
                    return data.rules || [];
                }
            }
            return [];
        };
        function checkConditions(conditions, evt) {
            if (conditions && conditions.length) {
                for (var i=0, l=conditions.length; i<l; i++) {
                    var cond = conditions[i];
                    if (!cond) { continue; }
                    if (!checkCondition(cond, evt)) { return false; }
                };
            }
            return true;
        }
        function checkCondition(cond, evt) {
            switch (cond.check) {
                case 'prio':
                    return evt.slot && evt.slot.id === self.getExclusionRuleSet()._prio;
                case 'slot':
                    return evt.slot && evt.slot.id === cond.slot;
                case 'param':
                    return evt.slot && evt.slot.getParam(cond.name) === cond.value;
                default:
                    return false;
            }
        }
    
        function generateRuleRunnerFunction(adform, properties) {
            return function(evt) {
                if (!(properties instanceof Array)) { properties = [properties]; }
                for (var i=0, l=properties.length; i<l; i++) {
                    var data = properties[i];
                    if (!checkConditions(data.conditions, evt)) { continue; }
                    each(data.rules, function(i, rule) {
                        if (rule) { executeRule(adform, rule, evt); }
                    });
                    return;
                }
            };
        }
        function executeRule(adform, rule, evt) {
            switch (rule.action) {
                case 'disableSlot':
                    return self.disableSlot(rule.slot, rule.collapse);
                case 'enableSlot':
                    return self.enableSlot(rule.slot);
                case 'requestSlot':
                    return self.ad(rule.slot);
                case 'setParam':
                    if (!rule.slot || rule.slot==='*') {
                        return self.setParam(rule.name, rule.value);
                    }
                    return self.setSlotParam(rule.slot, rule.name, rule.value);
                case 'moveAdToSlot':
                    self._currentAdSlot = self._getAdSlot(evt.slot);
                    return self.moveAdToSlot(rule.slot);
                case 'moveAdToDom':
                    log.nyi('exclusionRule', rule.action, rule);
                    return null;
                case 'applyCreateAd':
                    if (slotCreateAd[evt.slot.id]) {
                        self.trigger('exclusionrule.applycreatead.prematurecreatead', evt.slot, {adform: adform});
                    } else {
                        var nodes = makeArray(evt.slot.getDomNode().childNodes).filter(function(node) { return node.nodeType === doc.ELEMENT_NODE; });
                        self.createAd({type: adform, origin: 'exclusionrule', rule: rule, domlist: nodes});
                    }
                    return true;
                case 'refresh':
                    var slotSet = rule.slot.split(/ +/);
                    return self.refresh(slotSet);
                case 'useIframeCss':
                    return self.useIframeCss(rule.css || adform, rule.slot || evt.slot);
                case 'setAdProperty':
                    return self.setAdProperty.apply(self.AdSlot(rule.slot), [rule.property].concat(rule.args));
                case 'log':
                    log[rule.type || 'debug']('exclusionRule log', adform, rule.text, rule, evt);
                    return null;
                case 'clearOnetimeParams':
                    return self.AdSlot(rule.slot).clearOnetimeParams();
                default:
                    log.warn('exclusionRule unknown:', rule.action, rule);
                    return undefined;
            }
        }
    
        var slotCreateAd = {};
        self.on('ad.request', function(evt) {
            delete slotCreateAd[evt.slot.id];
        });
        self.on('adhook:finished', function(obj, meta, slot) {
            if (slot && slot instanceof self.AdSlot) {
                slotCreateAd[slot.id] = true;
            }
        });
    }).apply(self);

    (function() {
        self.disableSlot = function(slot, collapseParent) {
            self._getAdSlot(slot).disable(collapseParent);
            return self;
        };
        self.enableSlot = function(slot) {
            self._getAdSlot(slot).enable();
            return self;
        };
    
        function changeEnabledForAllSlots(enabled) {
            self.setConfig('slot-enabled-default', enabled);
            each(self.AdSlot.getList(), function(id, slot) {
                enabled ? slot.enable() : slot.disable();
            });
        }
        self.disableAllSlots = function() {
            changeEnabledForAllSlots(false);
        };
        self.enableAllSlots = function() {
            changeEnabledForAllSlots(true);
        };
    
        self.getUrlForSlot = function(slot) {
            self.AdSlot(slot).setParam('prf[ref]', encodeURIComponent(self.getParam('ref')));
            return self.AdSlot(slot).getUrl.apply(self.AdSlot(slot), Array.prototype.slice.call(arguments, 1));
        };
    
        self.initSlots = function(slots) {
            each(slots, function(slot, paramsAndConfig) {
                var params = paramsAndConfig.params,
                    config = paramsAndConfig.config || {};
                slot = self.AdSlot(slot);
                each(params, function(key, value) {
                    slot.setParam(key, value);
                });
                each(config, function(key, value) {
                    slot.setConfig(key, value);
                });
            });
        };
        self.getSlotParam = function(slot, key) {
            if (!(slot instanceof Array)) {
                return self._getAdSlot(slot).getParam(key);
            }
            var result = {};
            each(slot, function(i, slot) {
                result[slot] = self.getSlotParam(slot, key);
            });
            return result;
        };
        self.setSlotParam = function(slot, key, value) {
            if (!(slot instanceof Array)) { slot = [slot]; }
            each(slot, function(i, slot) {
                self._getAdSlot(slot).setParam(key, value);
            });
            return self;
        };
        self.setDirectSlotParam = function(slot, key, value) {
            if (!(slot instanceof Array)) { slot = [slot]; }
            each(slot, function(i, slot) {
                self._getAdSlot(slot).setDirectParam(key, value);
            });
            return self;
        };
        self.setOnetimeSlotParam = function(slot, key, value) {
            if (!(slot instanceof Array)) { slot = [slot]; }
            each(slot, function(i, slot) {
                self._getAdSlot(slot).setOnetimeParam(key, value);
            });
            return self;
        };
        self.getSlotConfig = function(slot, key, defaultValue) {
            return self._getAdSlot(slot).getConfig(key, defaultValue);
        };
        self.setSlotConfig = function(slot, key, value) {
            return self._getAdSlot(slot).setConfig(key, value);
        };
    
        self.emptySlot = function(slot) {
            self._getAdSlot(slot).empty();
            return self;
        };
        self.killSlot = function(slot) {
            self._getAdSlot(slot).kill();
            return self;
        };
        self.hideSlot = function(slot, method) {
            self._getAdSlot(slot).hide(method);
            return self;
        };
        self.showSlot = function(slot, method) {
            self._getAdSlot(slot).show(method);
            return self;
        };
        self.modifySlot = function(slot, action, func, toggleFunc) {
            self._getAdSlot(slot).modify(action, func, toggleFunc);
            return self;
        };
    
        self.createSlot = function(slotName, target, attr, adServiceData) {
            var dom = self.createSlotDomNode(slotName, attr, adServiceData);
            if (typeof target === STRING) {
                target = doc.querySelector(target);
            }
            try {
                target.appendChild(dom);
                self.ad(slotName);
            } catch (e) {
                log.error('Error in target supplied for AdService.createSlot()', e, arguments, this);
            }
            return self;
        };
    
        self._getAdSlot = function(slot) {
            if (slot instanceof self.AdSlot) {
                return slot;
            }
            return self.AdSlot(slot);
        };
    
        var currentAdSlot = null;
        self._getCurrentAdSlot = function() {
            if (this._currentAdSlot) { self._setCurrentAdSlot(this._currentAdSlot); }
            return currentAdSlot;
        };
        self._setCurrentAdSlot = function(slot) {
            currentAdSlot = slot;
        };
    
        self._currentPositionInTarget = function(slot) {
            const target = slot.getDomNode();
            const id = self.getConfig('data-attribute-namespace')+'-cursor';
            if (doc.readyState !== 'loading') { return false; }
            doc.write('<div id="'+id+'"></div>');
            const cursor = doc.getElementById(id);
            const cursorParentIsTarget = cursor.parentElement === target;
            cursor.parentNode.removeChild(cursor);
            return cursorParentIsTarget;
        };
    }).apply(self);

    (function() {
        const checkForVisibility = {};
        let sandboxAttributes = {
            'allow-forms': true,
            'allow-popups': true,
            'allow-popups-to-escape-sandbox': true,
            'allow-same-origin': true,
            'allow-scripts': true,
        };
    
        getRequestMethod = function(slot) {
            if (!slot.getConfig('secure', self.getConfig('secure', true))) {
                return Const.REQUEST_METHOD.FRIENDLY_IFRAME;
            }
            return Const.REQUEST_METHOD.NONFRIENDLY_IFRAME;
        };
    
        self.ad = function(slot, isRefresh) {
            return self._runOutsourceable('ad', self, [slot, isRefresh], self._getAdSlot(slot).getAdServer());
        };
        self._global.ad = function(slot, isRefresh) {
            slot = self._getAdSlot(slot);
            var data = {
                slot: slot,
                target: slot.getTarget(),
                deferred: false,
                isRefresh: !!isRefresh,
            };
            if (!slot.isEnabled()) {
                data.cancelReason = 'slot.disabled';
                self.trigger('ad.cancel', slot, data);
                return self;
            }
            if (!slot.getAdServer().isEnabled()) {
                data.cancelReason = 'server.disabled';
                self.trigger('ad.cancel', slot, data);
                return self;
            }
            var checkEnabled = slot.getConfig('checkEnabled', slot.getAdServer().getConfig('checkEnabled', self.getConfig('checkEnabled', true)));
            if (typeof checkEnabled === FUNCTION) {
                try {
                    checkEnabled = checkEnabled(slot, data);
                } catch (e) {
                    log.error('Error in function supplied for AdService.AdSlot('+slot.id+').setConfig(\'checkEnabled\', func)', e, arguments, data, this);
                }
            }
            if (!checkEnabled) {
                data.cancelReason = 'checked.disabled';
                self.trigger('ad.cancel', slot, data);
                return self;
            }
            if (slot.id !== self.getExclusionRuleSet()._prio) {
                if (slot.getConfig('request-ad-only-if-visible', self.getConfig('request-ad-only-if-visible', false)) && !slot.isVisible(data.isRefresh)) {
                    checkForVisibility[slot.id] = {slot: slot, data: data};
                    self.trigger('ad.notvisible', slot, data);
                    log.level('adreq', '[AdService.ad]', 'slot', slot.id, 'notVisible');
                    return self;
                }
                log.level('adreq', '[AdService.ad]', 'slot', slot.id, 'visible');
            }
            if (!self.getSlotConfig(slot, 'slotSetContext', false)) { 
                self.getDefaultAdServer().renewPageId();
            }
            self.trigger('ad.begin', slot, data);
            if (data.deferred && !slot.isIndependent) {
                self.trigger('ad.deferred', slot, data);
                return self;
            }
    
            if (typeof data.target !== UNDEFINED) {
                const requestMethod = getRequestMethod(slot);
                slot.setConfig('requestMethod', requestMethod);
                data.requestMethod = requestMethod;
                data.url = slot.getUrl(requestMethod);
                self.trigger('ad.urlReady', slot, data);
                slot.clearOnetimeParams();
                slot.clearMetaData();
                switch (requestMethod) {
                    case Const.REQUEST_METHOD.FRIENDLY_IFRAME:
                        self._request_iframeFriendly(slot, data);
                        break;
                    case Const.REQUEST_METHOD.NONFRIENDLY_IFRAME:
                    default:
                        self._request_iframeNonfriendly(slot, data);
                        break;
                }
            } else {
                if (doc.readyState === 'loading') {
                    log.remind('AdService.ad()', 'no target, not yet document.ready', slot, data);
                } else {
                    log.remind('AdService.ad()', 'no target, document.ready', slot, data);
                }
            }
    
            self.trigger('ad.end', slot, data);
            return self;
        };
    
        self.getAdUrl = function(slot) {
            slot = self._getAdSlot(slot);
            return slot.getCurrentUrl();
        };
    
        self.cleanup = function() {
            const slot = self._getCurrentAdSlot();
            if (!slot) { return; } 
            return self._ad_finished(slot);
        };
    
        self._ad_finished = function(slot, data) {
            slot = self._getAdSlot(slot);
            var meta = slot.getMetaData();
            if (meta[0]) {
                var events = slot.getAdServer().getEventsByMetaData(meta[0]);
                each(events, function(name, value) {
                    if (name === '_') { return; }
                    self.trigger('ad.' + name + ':' + value, slot);
                });
            }
            self.trigger('ad.finished', slot, data);
            if (slot.isEmpty()) {
                self.trigger('ad.empty', slot, data);
            }
            return self;
        };
    
        self._from_iframe_adFinished = function(data) {
            return self._ad_finished(data.slot, data);
        };
        self._from_iframe_eic = function(data) {
            self.trigger('ad.eic', self.AdSlot(data.slot), data);
        };
    
        function evaluateSize(measuredWidth, measuredHeight, metaWidth, metaHeight, adsize) {
            var result = {width: measuredWidth, height: measuredHeight},
                slot = self._getCurrentAdSlot(),
                adformatInfo = self.info.adformat(),
                useAutoSetIframeSize = adformatInfo.useAutoSetIframeSize(adsize),
                checkAutoSetIframeSize = adformatInfo.checkAutoSetIframeSize(adsize);
    
            if ((!useAutoSetIframeSize && !checkAutoSetIframeSize) && ((10 < metaWidth && metaWidth < measuredWidth) || (10 < metaHeight && metaHeight < measuredHeight))) {
                result = {width: metaWidth, height: metaHeight};
            }
            if (checkAutoSetIframeSize) {
                var maxResize = adformatInfo.sizetable(adsize).maxResize;
                if (measuredWidth > maxResize.width) {
                    result.width = maxResize.width;
                }
                if (measuredHeight > maxResize.height) {
                    result.height = maxResize.height;
                }
            }
    
            return result;
        }
        self._from_iframe_effectiveAdSize = function(data) {
            if (this.slot.getConfig('auto-set-iframe-size') === false) { return; }
            if (!self.getConfig('auto-set-iframe-size')) { return; }
            if (this.slot.fixedSize.type === 'pagecontrol') { return; }
            if (meta = this.slot._metaData[0]) {
                data = evaluateSize(data.width, data.height, meta.width, meta.height, meta.adsize);
            }
            if (this.slot.fixedSize.type === 'fixed') {
                data.width = this.slot.fixedSize.width;
                data.height = this.slot.fixedSize.height;
            }
            this.iframe.style.width = data.width + 'px';
            this.iframe.style.height = data.height + 'px';
        };
        self._from_iframe_setAdSize = function(data) {
            var width  = ((+data.width)+''===data.width+'') ? data.width + 'px' : data.width,
                height = ((+data.height)+''===data.height+'') ? data.height + 'px' : data.height;
            this.slot.domNode.style.width = this.iframe.style.width = width;
            this.slot.domNode.style.height = this.iframe.style.height = height;
        };
        self._from_iframe_importMetaData = function(data) {
            this.slot.getAdServer().importMetaData(this);
        };
    
        self._request_iframeNonfriendly = function(slot, data) {
            self.trigger('ad.request', slot, data);
            const passOverData = {
                slot: slot.id,
                server: slot.getAdServer().id,
                connector: self.getConfig('adservice-iframe-script-url'),
                external: {
                    location: self.getParams().ref,
                },
            };
            if (self.getConfig('fullsize', false)) { 
                passOverData.fullsize = true;
            }
            if (self.getConfig('eic', false)) {
                passOverData.eic = true;
            }
            if (slot.hasQueuedAdHtml()) {
                passOverData.css = self.getIframeCssFromExclusionRules(slot.getNextQueuedAdType(), slot);
                passOverData.html = slot.getQueuedAdHtml();
            } else {
                passOverData.url = data.url;
            }
    
            var fillIn = {
                namespace: self.getConfig('data-attribute-namespace'),
                nfiDocument: Code.html(self.getConfig('nonfriendlyiframe-document') + `?v=${+self.version.revision || +self.version.minor}`),
                data: Code.html(JSON.stringify(passOverData)),
                slot: Code.html(slot.id),
                sandbox: slot.getSandboxAttribute(slot.getSandboxAttributes()),
                size: slot.fixedSize.type === 'pagecontrol' ? '' : `width="${slot.fixedSize.type === 'fixed' ? slot.fixedSize.width : '100%'}" height="${slot.fixedSize.type === 'fixed' ? slot.fixedSize.height : data.target.offsetHeight||'100%'}"`,
                accessibility: 'aria-hidden="true" tabindex="-1"',
            };
    
            var html = `<iframe src="${fillIn.nfiDocument}" name="${fillIn.data}" data-${fillIn.namespace}-slot="${fillIn.slot}" ${fillIn.accessibility} referrerpolicy="strict-origin-when-cross-origin" frameborder="0" border="0" scrolling="no" marginwidth="0" marginheight="0"${fillIn.sandbox} ${fillIn.size}><\/iframe>`;
    
            data.target.insertAdjacentHTML('beforeend', html);
            self.trigger('ad.requested', slot, data);
            return true;
        };
    
        self.setSandboxAttributes = function(attr) {
            if (attr === false) {
                sandboxAttributes = {};
                return self;
            }
            sandboxAttributes = extend(sandboxAttributes, attr);
            return self;
        };
    
        self.getSandboxAttributes = function() {
            return sandboxAttributes;
        };
    
        self._request_iframeFriendly = function(slot, data) {
            let type;
            if (slot.hasQueuedAdHtml()) {
                type = slot.getNextQueuedAdType();
                data.html = slot.getQueuedAdHtml();
            } else {
                data.html = '<script id="external-script" src="' + Code.html(data.url) + '" onload="window.setTimeout(checkRunner, 100);" onerror="checkRunner();"><\/script>\n';
            }
    
            self.buildFriendlyIframe(data.target, data, type);
        };
    
        self.on('page.positionchange', function() {
            each(checkForVisibility, function(id, info) {
                if (info.slot.isVisible()) {
                    delete checkForVisibility[id];
                    self.ad(id, info.data.isRefresh);
                }
            });
        });
    }).apply(self);
    (function() {
        var intervalSlots = {};
        var skipRefreshSlots = {};
    
        self.refresh = function() {
            return self._runOutsourceable('refresh', self, arguments, self.getDefaultAdServer());
        };
        self._global.refresh = function() {
            var slots = arguments.length ? (arguments[0] instanceof Array ? arguments[0] : arguments) : self.AdSlot.getList();
            var is  = arguments.length > 1 ? arguments[1] : {timed: false}; 
            each(slots, function(index, slot) {
                self.setSlotConfig(slot, 'timed', is.timed);
                self.refreshSlot(slot);
            });
            return self;
        };
    
        self._from_iframe_refresh = function() {
            self.refresh(this.slot);
        };
        function isTimedRefreshSlot(slot) {
            const intervalSets = self.getConfig('intervalSets', {});
            return Object.values(intervalSets).some((array) => array.includes(slot.id));
        }
        function hasDocumentFocus() {
            return window === top ? document.hasFocus() : !document.hidden;
        }
    
        self.on('ad.property.skipRefresh', (evt) => {
            skipRefreshSlots[evt.slot.id] = evt.data.skipRefresh;
        });
    
        function skipRefresh(slot) {
            if (!skipRefreshSlots.hasOwnProperty(slot.id) || skipRefreshSlots[slot.id] <= 0) {
                delete slot.config.skipped;
                delete skipRefreshSlots[slot.id];
                return false;
            }
            slot.setConfig('skipped', true);
            skipRefreshSlots[slot.id]--;
            return true;
        }
    
        self.refreshSlot = function(slot) {
            slot = self._getAdSlot(slot);
            if (!slot.skipCheck && isTimedRefreshSlot(slot) && !hasDocumentFocus()) { return self; }
            if (skipRefresh(slot)) { return self; }
            if (!slot.getConfig('refreshDisabled', false) && slot.visibilityCheck() && slot.getAdServer().getConfig('supportRefresh', false)) {
                slot.setConfig('auto-set-iframe-size', self.getConfig('auto-set-iframe-size'));
                slot.empty();
                self.ad(slot, true);
            } else {
                self.trigger('ad.norefresh', slot);
            }
            return self;
        };
    
        self.disableRefresh = function() {
            var slots = arguments.length ? arguments : self.AdSlot.getList();
            each(slots, function(index, slot) {
                slot = self._getAdSlot(slot);
                slot.setConfig('refreshDisabled', true);
            });
            return self;
        };
    
        self.enableRefresh = function() {
            var slots = arguments.length ? arguments : self.AdSlot.getList();
            each(slots, function(index, slot) {
                slot = self._getAdSlot(slot);
                slot.setConfig('refreshDisabled', false);
            });
            return self;
        };
    
        self.setRefreshInterval = function(config) {
            if (!config) { return; }
            if (Const.QUERYPARAMS['adservice-refresh'] === 'false') { return; }
    
            function isBlocked(slots) {
                var isBlocked = false;
                slots.forEach((slot) => {
                    if (self.getSlotConfig(slot, 'timed') === false) {
                        self.setSlotConfig(slot, 'timed', true);
                        isBlocked = true;
                    }
                });
                return isBlocked;
            }
    
            for (var intervalTimer in intervalSlots) {
                clearInterval(intervalSlots[intervalTimer]);
            }
    
            var refreshSets = {};
            each(config, function(timer, slotConfig) {
                refreshSets[timer] = slotConfig.slots;
                if (slotConfig.newPageContext) {
                    slotConfig.slots.forEach((slot) => self.setSlotConfig(slot, 'slotSetContext', true));
                }
                intervalSlots[timer] = setInterval(function() {
                    if (isBlocked(slotConfig.slots)) { return; }
                    if (slotConfig.newPageContext) {
                        wi = generatePageId();
                        slotConfig.slots.forEach(function(slot, index) {
                            self.AdSlot(slot).setParam('wi', wi);
                            self.AdSlot(slot).setDirectParam('ac', index+1);
                        });
                    }
                    self.refresh(slotConfig.slots, {timed: true});
                }, timer * 1000);
            });
            AdService.setConfig('intervalSets', refreshSets);
            return self;
        };
    }).apply(self);

    (function() {
        var iframeCss = {};
    
        self.setIframeCss = function(data) {
            iframeCss = extend(iframeCss, data);
            return self;
        };
    
        self.getIframeCss = function(type) {
            if (type) { return iframeCss[type]; }
            else { return iframeCss; }
        };
    
        self.getIframeCssFromExclusionRules = function(adform, slot) {
            var rules = self.getExclusionRules(adform, slot);
            for (var i=0, l=rules.length; i<l; i++) {
                if (rules[i].action === 'useIframeCss') {
                    return (iframeCss[rules[i].css || adform] || '').css;
                }
            }
        };
        self.useIframeCss = function(type, slot) {
            var css = (iframeCss[type] || '').css;
            if (!slot) {
                return css || '';
            }
            slot = self._getAdSlot(slot);
            var target = slot.getDomNode().querySelector('iframe');
            if (target) {
                var data = {css: css};
                self.trigger('ad.useiframecss', slot, data);
                self.Communication.sendMessage(target, 'useIframeCss', data);
            } else {
                log.warn('IFrame CSS not applied: slot does not (yet) have an IFrame content.', 'type:', type, 'slot:', slot);
            }
            return self;
        };
    
        self.collapseParentContainer = function(slot) {
            if (!slot) { return; }
            var target = self._getAdSlot(slot).getDomNode();
            if (target?.parentElement) {
                target.parentElement.style['minHeight'] = '0px';
            }
            return self;
        };
    
        self.setIframeCss({'default': {css: ''}});
    }).apply(self);

    (function() {
        var templates = {},
            mustacheTemplateUrl;
    
        self.setTemplateEngine = function(engine, templateUrl) {
            if (engine && engine !== 'mustache') {
                log.error('AdService.setTemplateEngine(engine, templateUrl) MUST be called with \'mustache\' as the engine.');
            }
            return self.setTemplateUrl(templateUrl);
        };
        self.setTemplateUrl = function(templateUrl) {
            var data = {url: templateUrl};
            self.trigger('render.templateurl', data);
            mustacheTemplateUrl = data.url;
            return self;
        };
        self.getTemplate = function(tpl) {
            if (!tpl) { return templates; }
            var parts = tpl.split('-');
            for (var i=parts.length; i>0; i--) {
                tpl = parts.slice(0, i).join('-');
                if (templates[tpl]) {
                    return templates[tpl];
                }
            }
            return '';
        };
        self.setTemplates = function(data) {
            self.trigger('render.templates', data);
            templates = extend(templates, data);
            self.trigger('render.templateloaded');
        };
        self._loadTemplates = function() {
            log.level('render', 'Loading Templates from', mustacheTemplateUrl);
            loadJsonp(mustacheTemplateUrl);
            return null;
        };
    
        function renderTemplate(data, template, message) {
            return AdService.Mustache.render(template, data.render);
        }
        function doRender(data, template, message) {
            var html = self.getConfig('render-template-callback')(data, template, message);
            if (data.render._oba_direct && /^(http(s)?:\/\/)?([\w-]{2,}\.)+[a-z]{2,6}(\/[\w\-\.]+)*\/?([\?#][\w!#&'\(\)\*+,-\.\/:;=\?@~]*)?$/.test(decodeURIComponent(data.render._oba_direct))) {
                html = '<a class="oba-admarker" href="'+data.render._oba_direct+'" target="_blank"><div class="oba-wrapper oba-corner-position" style="z-index:28;" onmouseover="this.className += \' oba-admarker-hover\';" onmouseout="this.className = this.className.replace(/oba-admarker-hover/, \'\');"><div class="oba-admarker"><div class="oba-admarker-icon oba-corner-position"></div><div class="oba-admarker-text oba-corner-position">Datenschutzinfo</div></div></div></a>' + html;
            }
            if (data.slot) {
                var meta = data.slot.getMetaData()[0],
                    server = data.slot.getAdServer();
                html = server.renderMeta(html, meta);
            }
            if (data.render.debug && /^<div( ((style="([\w\-]+:[\w# ',\.-]+;)*" ?)|(class="[\w ]*" ?)){1,2})?>(<\/?[biu]>|<br ?\/>|[\w\-\.,:_# \u20AC\u0024]+)*<\/div>$/.test(data.render.debug)) {
                data.render.debug = data.render.debug.replace('#close#', '<span style="float: right; cursor: pointer;" onclick="this.parentElement.style.display=\'none\';">[x]</span>');
                html += data.render.debug;
            }
            switch (data._method) {
                case 'write':
                    document.write(html);
                    data._rendered = true;
                    break;
                case 'dom':
                default:
                    if (data.render.visibility) {
                        data.slot.setConfig('auto-set-iframe-size', false);
                        data.target.style.position = 'relative'; 
                        data.target.querySelector('iframe').style.cssText = 'min-height:0px; visibility:hidden; position:absolute; top:0; left:0; z-index: -1;';
                        data.target.insertAdjacentHTML('afterbegin', html);
                        data._rendered = true;
                    } else {
                        data.target.innerHTML = html;
                        data._rendered = true;
                        self._ad_finished(data.slot, data);
                    }
            }
            if (data.render.count?.length) {
                var countFunction = self.getConfig('render-count-function');
                if (typeof countFunction === FUNCTION) {
                    try {
                        countFunction(makeArray(data.render.count), data);
                    } catch (e) {
                        log.error('Error in function supplied for AdService.setConfig(\'render-count-function\', func)', e, arguments, data, this);
                    }
                }
            }
            self.trigger('render.end', data);
        }
        function render(data, target, message) {
            data = {render: data, target: target, slot: data.slot};
            self.trigger('render.begin', data);
            self.trigger('render._builddata', data);
            if ((data.target === undefined) || (data.target && data.target.self === data.target.window)) {
                data.target = data.slot.getDomNode() || {};
                data._method = 'dom';
            } else if (data.target === false) {
                data._method = 'write';
            } else {
                data._method = 'dom';
            }
            var template = data.render._type;
            if (self.getConfig('render-template-callback') === renderTemplate) {
                template = self.getTemplate(template);
                if (!template) {
                    self.on('render.templateloaded', (function(data, doRender, message) {
                        return function() {
                            if (data._rendered) { return; }
                            doRender(data, self.getTemplate(data.render._type), message);
                        };
                    })(data, doRender, message));
                    return self._loadTemplates();
                }
            }
            doRender(data, template, message);
            return self;
        }
        self.setConfig('render-callback', render);
        self.setConfig('render-template-callback', renderTemplate);
        self.render = function(data, target, message) {
            if (!data.slot) { data.slot = self._getCurrentAdSlot(); }
            self.getConfig('render-callback')(data, target, message);
        };
        self._from_iframe_render = function(data) {
            data.slot = this.slot;
            self.render(data, this.window, this.message);
            return null;
        };
    
        self.setConfig('render-count-function', function(count, data) {
            each(count, function(i, url) {
                new Image(1, 1).src = url;
            });
        });
    
        self.on('render._builddata', function(evt) {
            var render = evt.data.render;
            each(render._splitlines, function(i, property) {
                var lineProperty = property + '_lines';
                render[lineProperty] = render[property].split(/\r\n|\r|\n|\s*##br##\s*/);
                for (var i=0, l=render[lineProperty].length; i<l; i++) {
                    render[lineProperty][i] = {
                        line: render[lineProperty][i],
                        isLast: render[lineProperty].length === i+1,
                        notLast: render[lineProperty].length !== i+1,
                        isFirst: i === 0,
                        notFirst: i !== 0,
                    };
                }
            });
        });
    }).apply(self);

    (function() {
        self.param = function(obj, sepParam, sepValue, escFuncName, escFuncValue) {
            var param = [];
            each(obj, function(name, value) {
                param.push((escFuncName||Code.url)(''+name) + (sepValue||'=') + (escFuncValue||escFuncName||Code.url)(''+value));
            });
            return param.join(sepParam||'&');
        };
    
        self.createHtmlFromDom = function(domNode) {
            var el = document.createElement('div');
            el.appendChild(domNode);
            return el.innerHTML;
        };
        self.createSlotHtml = function(slot, attr, adServiceData) {
            return self.createHtmlFromDom(self.createSlotDomNode.apply(self, arguments));
        };
        self.createSlotDomNode = function(slot, attr, adServiceData) {
            var el = document.createElement('div');
            el.setAttribute(self.getDataAttrName('slot'), slot);
            attr && each(attr, function(name, value) {
                el.setAttribute(name, value);
            });
            adServiceData && each(adServiceData, function(name, value) {
                el.setAttribute(self.getDataAttrName(name), value);
            });
            return el;
        };
    
        self.loadScript = function(url, isModule = false) {
            if (document.head.querySelectorAll('script[src="'+url+'"]').length > 0) { return self; }
            if (!/^(https:)?\/\/(((origin-)?(advideo|adimg)\.uimserv\.net|i0(-qa)?\.(web\.de|gmx\.(net|at|ch|es|com|co\.uk|fr)|mail\.com|1und1\.de))\/tam|(js|img)\.ui-portal\.de|uimqs-adtestfe02\.server\.lan)\/[-\w\.\/]+\.m?js([\?#][\w!#&'\(\)\*+,-\.\/:;=\?@~]+)?$/.test(decodeURIComponent(url))) { return self; }
            loadJsonp(url, isModule);
        };
    
        self.getContainerWidth = function(domNode) {
            return domNode.offsetWidth;
        };
    }).apply(self);

    self._initialize = function() {
        self.triggerOnce('initialize.begin');
        self.triggerOnce('initialize.end');
    };
};
try {
    new AdService.Module('abd', function() {
            if (AdService._isConnector) { return; }
            var AdBlockDetection;
            var adBlockDetectionConfig = {
                onlyTheseChecks: undefined,
                notTheseChecks: undefined,
                resultFunction: resultFunction,
                eventname: null,
                informOn: ['everything'],
                localData: true,
                detectionAttributes: {
                    css_sync: {
                        id: 'adservice-abd-container-sync',
                    },
                    css_async: {
                        id: 'adservice-abd-container-async',
                        maxTries: {at: 5, ch: 10}[(window.urlParams?.prf_portal || location.hostname).slice?.(-2)] || 3, 
                        timeout: 100,
                    }
                },
            };
            var result = {isBlocked: undefined, didCheck: false};
            function resultFunction(abdResult) {
                result = {
                    isBlocked: abdResult.detected,
                    didCheck: abdResult.pending === 0 ? true : false,
                    initialized: true,
                    details: abdResult,
                    directBlocked: abdResult.type.css || false,
                    indirectBlocked: abdResult.type.request || false,
                };
                if (result.directBlocked) {
                    AdService.triggerOnce('adblock.detected');
                    AdService.triggerOnce('adblock.direct');
                } else if (result.indirectBlocked) {
                    AdService.triggerOnce('adblock.detected');
                    AdService.triggerOnce('adblock.indirect');
                }
                if (result.didCheck) {
                    if (!result.isBlocked) {
                        AdService.triggerOnce('adblock.notdetected');
                    }
                    AdService.triggerOnce('adblock.detectionfinished', result);
                }
            }
            (function() {
                const BOOLEAN = 'boolean';
                const FUNCTION = 'function';
                const NUMBER = 'number';
                const OBJECT = 'object';
                const STRING = 'string';
                const UNDEFINED = 'undefined';
                const STATE = {FINISHED: 'finished', INITIALIZED: 'initialized', OMITTED: 'omitted', QUEUED: 'queued', RUNNING: 'running', TIMEOUT: 'timeout'};
                const COOKIE = 'adtrgtng';
                const win = window;
                const doc = win.document;
                const docElement = doc.documentElement;
                AdBlockDetection = {
                    getCurrentStatus: () => ({detected: undefined, pending: undefined, type: {}, fyi: {}, detection: {}}),
                    start: () => { AdBlockDetection.autostart = true; },
                };
                function createAbd() {
                    const abdCopy = AdBlockDetection;
                    AdBlockDetection = new function() {
                        this.version = '1.8.2';
                        const self = this;
                        const detection = {};
                        const detectionList = {
                            known: {},
                            created: {},
                            getsResult: [],
                            results: {},
                        };
                        const abdList = [{abd: this, notifyOn: {}, runs: {}}];
                        const abdConfig = this.conf = (() => {
                            const configCustom = typeof adBlockDetectionConfig !== UNDEFINED ? deepCopy(adBlockDetectionConfig) : {};
                            const configDefault = {
                                onlyTheseChecks: undefined,
                                notTheseChecks: undefined,
                                resultFunction: undefined,
                                eventname: 'abdresult',
                                informOn: ['everything'],
                                timeout: 5000,
                                autostart: true,
                                localData: false,
                                detectionAttributes: {},
                            };
                            return mergeObjects(configCustom, configDefault);
                        })();
                        const abdStatus = {
                            detected: undefined,
                            pending: 0,
                            running: 0,
                            timeout: false,
                            type: {},
                            fyi: {},
                            detection: detection,
                        };
                        const localData = getLocalData();
                        let isFinished = false;
                        let requestedSync = false;
                        if (!Array.isArray(abdConfig.informOn)) { abdConfig.informOn = [abdConfig.informOn]; }
                        for (const mode of ['onlyTheseChecks', 'notTheseChecks']) {
                            if (!Array.isArray(abdConfig[mode])) { continue; }
                            abdConfig[mode] = abdConfig[mode].map((val) => Array.isArray(val) ? val : [val]);
                        }
            
                        if (typeof docElement.AdBlockDetection?.registerABD === FUNCTION) {
                            let result = docElement.AdBlockDetection.registerABD(this);
                            detectionList.known = result.known.reduce((result, item) => {
                                result[item] = true;
                                return result;
                            }, detectionList.known);
                            detectionList.created = result.needed.reduce((result, item) => {
                                result[item] = false;
                                return result;
                            }, detectionList.created);
                            detectionList.getsResult = result.getsResult;
                            detectionList.results = result.results;
                            abdConfig.notTheseChecks = (abdConfig.notTheseChecks || []).concat(docElement.AdBlockDetection.externalExclusions || []);
                            for (const detection of Object.values(docElement.AdBlockDetection.getCurrentStatus().detection)) {
                                abdConfig.notTheseChecks.push(detection.types);
                            }
                            docElement.AdBlockDetection.externalExclusions = abdConfig.notTheseChecks;
                        } else {
                            this._isCentralInstance = true;
                            docElement.AdBlockDetection = this;
                        }
            
                        function logError() {
                            console.error.apply(console, ['[AdBlockDetection]'].concat(Array.prototype.slice.call(arguments)));
                        }
            
                        function deepCopy(original) {
                            let copy;
                            switch (true) {
                                case typeof original === NUMBER && isNaN(original):
                                    return NaN;
                                case [STRING, NUMBER, BOOLEAN].includes(typeof original):
                                case [undefined, null].includes(original):
                                    return original;
                                case typeof original === FUNCTION:
                                    return original;
                                case original instanceof Date:
                                    return new Date(original);
                                case Array.isArray(original):
                                    copy = [];
                                    original.forEach((value, index) => {
                                        copy[index] = deepCopy(value);
                                    });
                                    return copy;
                                default:
                                    copy = {};
                                    for (const key in original) {
                                        if (!original.hasOwnProperty(key)) { continue; }
                                        copy[key] = deepCopy(original[key]);
                                    }
                                    return copy;
                            }
                        }
            
                        function mergeObjects(base, minor) {
                            for (const key in minor) {
                                if (!minor.hasOwnProperty(key)) { continue; }
                                if (base.hasOwnProperty(key)) {
                                    if (typeof minor[key] === OBJECT && minor[key] !== null) {
                                        base[key] = mergeObjects(base[key], minor[key]);
                                    }
                                    continue;
                                }
                                base[key] = minor[key];
                            }
                            return base;
                        }
            
                        function getCookieData() {
                            try {
                                return JSON.parse(atob(new RegExp('(?:^|; )' + COOKIE + '=([^;]*)').exec(doc.cookie)[1])) || {};
                            } catch { }
                            return {};
                        }
            
                        function getLocalData() {
                            if (!abdConfig.localData) { return {}; }
                            const result = getCookieData().a;
                            if (typeof result !== OBJECT) { return {}; }
                            for (const name in result) {
                                if (!result.hasOwnProperty(name)) { continue; }
                                if (result[name] === 0 || result[name] === 1) {
                                    result[name] = !!result[name];
                                } else {
                                    delete result[name];
                                }
                            }
                            return result;
                        }
            
                        function setLocalData(forcedSync) {
                            if (!forcedSync && !abdConfig.localData) { return; }
                            const cookieData = getCookieData();
                            if (typeof cookieData.a !== OBJECT) {
                                cookieData.a = {};
                            }
                            for (const detection of Object.values(detectionList.created)) {
                                if (!detection.conf.localMapping) { continue; }
                                if (typeof detection.status.result !== BOOLEAN) { continue; }
                                cookieData.a[detection.conf.localMapping] = +detection.status.result;
                            }
                            const domain = '.' + location.hostname.split('.').slice(-(location.hostname.endsWith('.co.uk') ? 3 : 2)).join('.');
                            doc.cookie = COOKIE + '=' + btoa(JSON.stringify(cookieData)) + '; domain='+ domain + '; path=/; SameSite=None; Secure;';
                        }
            
                        function informPage(isProvisional) {
                            function send() {
                                if (typeof abdConfig.eventname === STRING) {
                                    docElement.dispatchEvent(new CustomEvent(abdConfig.eventname, {detail: abdStatus}));
                                }
                                if (typeof abdConfig.resultFunction === FUNCTION) {
                                    try {
                                        abdConfig.resultFunction(abdStatus);
                                    } catch (e) {
                                        logError('Failed to execute custom function adBlockDetectionConfig.resultFunction', abdConfig.resultFunction, abdStatus, e);
                                    }
                                }
                                if (!abdStatus.pending && requestedSync) {
                                    requestedSync = false;
                                    setLocalData(true);
                                }
                            }
                            if (!abdStatus.pending && abdStatus.detected === undefined) {
                                abdStatus.detected = false;
                            }
                            if (abdConfig.informOn.includes('verbose')) {
                                send();
                                return;
                            }
                            if (isProvisional) { return; }
                            if (abdConfig.informOn.includes('everything')) {
                                send();
                                return;
                            }
                            if (abdConfig.informOn.includes('detected') && abdStatus.detected && !informPage.detectedSent) {
                                informPage.detectedSent = true;
                                send();
                                return;
                            }
                            if (abdConfig.informOn.includes('finished') && !abdStatus.pending && !isFinished) {
                                isFinished = true;
                                send();
                                return;
                            }
                        };
            
                        function distributeResult(name, result, isProvisional) {
                            if (self._isCentralInstance) {
                                for (const obj of abdList) {
                                    if (!obj.notifyOn[name]) { continue; }
                                    obj.abd.incomingResult(name, result, isProvisional);
                                }
                            } else {
                                docElement.AdBlockDetection.incomingResult(name, result, isProvisional);
                            }
                        }
            
                        this.incomingResult = function(name, result, isProvisional) {
                            if (this._isCentralInstance) {
                                distributeResult(name, result, isProvisional);
                            } else {
                                const detection = detectionList.known[name];
                                if (!detection) { return; }
                                detection.setResult(result, isProvisional, true);
                            }
                        };
            
                        this.forceSyncData = function() {
                            if (abdStatus.pending) {
                                requestedSync = true;
                            } else {
                                setLocalData(true);
                            }
                        }
            
                        this.getCurrentStatus = function() {
                            return deepCopy(abdStatus);
                        };
            
                        this.start = function() {
                            const detections = Object.values(detectionList.created);
                            abdStatus.pending = detections.length;
                            informPage();
                            for (const detection of detections) {
                                if (!detection) { continue; }
                                const usesLocalData = detection.conf.localMapping && localData.hasOwnProperty(detection.conf.localMapping);
                                if (!usesLocalData && !detectionList.results.hasOwnProperty(detection.conf.name)) { continue; }
                                detection.setResult(usesLocalData ? localData[detection.conf.localMapping] : detectionList.results[detection.conf.name], false, !usesLocalData);
                                detection.status.state = STATE.FINISHED;
                            }
                            for (const detection of detections) {
                                if (!(detection?.status.state === STATE.INITIALIZED)) { continue; }
                                if (!isBlocked(detection.conf.name, detection.userConf || {})) { continue; }
                                detection.status.state = STATE.QUEUED;
                            }
                            for (const detection of detections) {
                                if (!(detection?.status.state === STATE.INITIALIZED)) { continue; }
                                detection.status.state = STATE.RUNNING;
                                abdStatus.running++;
                                detection.run();
                            }
                            if (!abdStatus.running) {
                                abdStatus.pending = 0;
                                for (const detection of detections) {
                                    if (!(detection?.status.state === STATE.QUEUED)) { continue; }
                                    detection.status.pending = false;
                                    detection.status.state = STATE.OMITTED;
                                }
                            }
                            if (detections.some((detection) => ![STATE.FINISHED, STATE.OMITTED, STATE.TIMEOUT].includes(detection.status.state))) { return; }
                            setLocalData();
                            informPage();
                        };
            
                        this.registerABD = function(abd) {
                            const result = {
                                known: Object.keys(detectionList.known),
                                needed: [],
                                getsResult: [],
                                results: {},
                            };
                            const abdObject = {
                                abd: abd,
                                notifyOn: {},
                                runs: {},
                            };
                            for (const name in detectionList.known) {
                                const det = detectionList.known[name];
                                const needed = detectionMatch(det.conf.types, abd.conf.onlyTheseChecks, true) && !detectionMatch(det.conf.types, abd.conf.notTheseChecks, false);
                                if (!needed) { continue; }
                                const running = abdList.some((obj) => !!obj.runs[name]);
                                if (!running) {
                                    abdObject.runs[name] = true;
                                } else {
                                    abdObject.notifyOn[name] = true;
                                }
                            };
                            abdList.push(abdObject);
                            result.needed = Object.keys(abdObject.runs);
                            result.getsResult = Object.keys(abdObject.notifyOn);
                            for (const name of result.getsResult) {
                                if (detectionList.created[name]?.status.pending === false) {
                                    result.results[name] = detectionList.created[name].status.result;
                                }
                            }
                            return result;
                        };
            
                        function detectionMatch(types, checks, ifUndefined) {
                            if (checks === undefined) { return ifUndefined; }
                            return checks.some((checkSet) => {
                                if (checkSet.some((check) => !types.includes(check))) { return false; }
                                return true;
                            });
                        }
            
                        function determineIfNecessary(detection) {
                            if (self._isCentralInstance) {
                                detectionList.known[detection.conf.name] = detection;
                                return detectionMatch(detection.conf.types, abdConfig.onlyTheseChecks, true) && !detectionMatch(detection.conf.types, abdConfig.notTheseChecks, false);
                            }
                            return detectionList.created[detection.conf.name] === false
                                || (!detectionList.known[detection.conf.name] && detectionMatch(detection.conf.types, abdConfig.onlyTheseChecks, true) && !detectionMatch(detection.conf.types, abdConfig.notTheseChecks, false));
                        }
            
                        function isBlocked(name, {dependencies}) {
                            if (!Array.isArray(dependencies)) { return false; }
                            return !Object.values(dependencies).some((dependency) => {
                                return !Object.keys(dependency).some((key) => {
                                    if (key === name || !abdStatus.detection[key]) { return false; }
                                    if (dependency[key] === abdStatus.detection[key].result) { return false; }
                                    if (dependency[key] === abdStatus.detection[key].state) { return false; }
                                    return true;
                                });
                            });
                        }
            
                        const Detection = this.Detection = function(constructor) {
                            this.conf = constructor;
                            this.conf.abort = this.conf.abort || function() {
                                if (this.status.state === STATE.OMITTED) {
                                    this.status.pending = false;
                                } else if (this.status.state === STATE.QUEUED) {
                                    this.status.pending = false;
                                    this.status.state = STATE.OMITTED;
                                    this.setResult(undefined, true);
                                } else {
                                    abdStatus.timeout = true;
                                    this.status.state = STATE.TIMEOUT;
                                    this.setResult(false);
                                    this.conf.cleanup.call(this);
                                }
                            }.bind(this);
                            this.conf.results = this.conf.results || {};
                            this.conf.results._default = this.conf.results._default || {type: (a, b) => a || b};
                            this.conf.results._global = this.conf.results._global || ((a, b) => {
                                if (a || b) { return true; }
                                if (a === b) { return a; }
                                return b ?? a;
                            });
                            this.conf.localMapping = this.conf.localMapping ?? (this.conf.name.split('_').reduce((prev, val) => prev+(val[0] ?? ''), '') || this.conf.name);
                            this.userConf = mergeObjects(deepCopy(abdConfig.detectionAttributes[this.conf.name] || {}), this.conf.userConfDefaults || {});
                            this.data = {};
                            this.status = {
                                pending: true,
                                state: STATE.INITIALIZED,
                                result: undefined,
                                types: this.conf.types,
                            };
                            if (!determineIfNecessary.call(this, this)) {
                                if (detectionList.getsResult.includes(this.conf.name)) {
                                    this.executedExternally = true;
                                    detection[constructor.name] = this.status;
                                    detectionList.created[this.conf.name] = this;
                                    detectionList.known[this.conf.name] = this;
                                }
                                return;
                            }
                            abdList[0].runs[this.conf.name] = true;
                            this.timeout = win.setTimeout(this.abort.bind(this), abdConfig.timeout);
                            for (const type of this.conf.types) {
                                const temp = this.conf.results[type] || this.conf.results._default || {type: true};
                                if (!abdStatus.type.hasOwnProperty(type) && temp.type) { abdStatus.type[type] = undefined; }
                                if (!abdStatus.fyi.hasOwnProperty(type) && temp.fyi) { abdStatus.fyi[type] = undefined; }
                            }
                            detection[constructor.name] = this.status;
                            detectionList.created[this.conf.name] = this;
                        };
            
                        Detection.prototype.run = function() {
                            if (this.executedExternally) { return; }
                            if (this.conf.initialize) { this.conf.initialize.call(this); }
                            this.conf.runner.call(this);
                        };
            
                        Detection.prototype.setResult = function(result, isProvisional, isDistributed) {
                            if (this.status.pending) { this.status.pending = !!isProvisional; }
                            if (this.status.state === STATE.RUNNING && !isProvisional) {
                                this.status.state = STATE.FINISHED;
                                if (!isDistributed && this.conf.cleanup) {
                                    this.conf.cleanup.call(this);
                                }
                            }
                            this.status.result = result;
                            const detections = Object.values(abdStatus.detection);
                            abdStatus.pending = detections.reduce((counter, detection) => counter + detection.pending, 0);
                            abdStatus.running = detections.reduce((counter, detection) => counter + (detection.state === STATE.RUNNING), 0);
                            for (const type of this.conf.types) {
                                const combinator = this.conf.results[type] || this.conf.results._default;
                                if (typeof combinator.type === FUNCTION) { abdStatus.type[type] = combinator.type(abdStatus.type[type], result); }
                                if (typeof combinator.fyi === FUNCTION) { abdStatus.fyi[type] = combinator.fyi(abdStatus.fyi[type], result); }
                                abdStatus.detected = this.conf.results._global(abdStatus.detected, result);
                            }
                            if (!isDistributed) {
                                distributeResult(this.conf.name, result, isProvisional);
                            }
                            if (detections.some((detection) => detection.state === STATE.INITIALIZED)) { return; }
                            if (!isProvisional) {
                                if (!abdStatus.timeout) {
                                    for (const detection of Object.values(detectionList.created)) {
                                        if (!(detection?.status.state === STATE.QUEUED)) { continue; }
                                        if (isBlocked(detection.conf.name, detection.userConf || {})) { continue; }
                                        detection.status.state = STATE.RUNNING;
                                        abdStatus.running++;
                                        detection.run();
                                    }
                                }
                                if (!abdStatus.running && abdStatus.pending) {
                                    abdStatus.pending = 0;
                                    for (const detection of Object.values(detectionList.created)) {
                                        if (!(detection?.status.state === STATE.QUEUED)) { continue; }
                                        detection.conf.abort.call(detection);
                                    }
                                }
                            }
                            if (!abdStatus.pending) {
                                setLocalData();
                            }
                            informPage(isProvisional);
                        };
                        Detection.prototype.abort = function() {
                            if (typeof this.conf.abort === FUNCTION && this.status.pending) {
                                this.conf.abort.call(this);
                            }
                        };
            
                        (function() {
                            new Detection({
                                name: 'css_sync',
                                types: ['css', 'sync'],
                                userConfDefaults: {
                                    id: 'abd-container-sync',
                                },
                                initialize: function() {
                                    const el = this.data.element = doc.createElement('div');
                                    el.id = this.userConf.id;
                                    el.className = 'gemini-ad advertorial adv ad ad-container ad-contain promoad pub_300x250 plainAd photoad ad-body ad-content adContent ad300x250 adBox adCreative adModule adboxcontent sponsorad ezo_ad';
                                    el.setAttribute('lazy-ad', 'top_banner');
                                    el.style.width = '300px';
                                    el.style.height = '250px';
                                    el.style.position = 'absolute';
                                    el.style.top = '-1000px';
                                    el.style.left = '-1000px';
                                    doc.body.firstElementChild ? doc.body.insertBefore(el, doc.body.firstElementChild) : doc.body.appendChild(el);
                                },
                                runner: function() {
                                    if (doc.body.checkVisibility()) {
                                        const height = this.data.element.clientHeight || this.data.element.offsetHeight;
                                        const isBlocked = height > 0 ? false : true;
                                        this.setResult(isBlocked);
                                    } else {
                                        this.setResult(undefined);
                                    }
                                },
                                cleanup: function() {
                                    if (this.data.element && Array.from(doc.body.childNodes).includes(this.data.element)) {
                                        doc.body.removeChild(this.data.element);
                                        delete this.data.element;
                                    }
                                },
                            });
                        })();
                        (function() {
                            new Detection({
                                name: 'css_async',
                                types: ['css', 'async'],
                                userConfDefaults: {
                                    id: 'abd-container-async',
                                    maxTries: 5,
                                    timeout: 100,
                                },
                                initialize: function() {
                                    const el = this.data.element = doc.createElement('div');
                                    el.id = this.userConf.id;
                                    el.className = 'gemini-ad advertorial adv ad ad-container ad-contain promoad pub_300x250 plainAd photoad ad-body ad-content adContent ad300x250 adBox adCreative adModule adboxcontent sponsorad ezo_ad';
                                    el.setAttribute('lazy-ad', 'top_banner');
                                    el.style.width = '300px';
                                    el.style.height = '250px';
                                    el.style.position = 'absolute';
                                    el.style.top = '-1000px';
                                    el.style.left = '-1000px';
                                    doc.body.firstElementChild ? doc.body.insertBefore(el, doc.body.firstElementChild) : doc.body.appendChild(el);
                                },
                                runner: function() {
                                    if (this.status.state !== STATE.RUNNING) { return; }
                                    if (this.conf.custom.count++ < this.userConf.maxTries) {
                                        if (this.conf.custom.count === 1 || !this.conf.custom.check.call(this)) {
                                            this.conf.custom.timeout = win.setTimeout(function() {
                                                this.conf.runner.call(this);
                                            }.bind(this), this.userConf.timeout);
                                        }
                                    } else {
                                        const detected = doc.body.checkVisibility() ? false : undefined;
                                        this.setResult(detected);
                                    }
                                },
                                cleanup: function() {
                                    if (this.data.element && Array.from(doc.body.childNodes).includes(this.data.element)) {
                                        doc.body.removeChild(this.data.element);
                                        delete this.data.element;
                                    }
                                },
                                abort: function() {
                                    if (this.status.state === STATE.OMITTED) {
                                        this.status.pending = false;
                                    } else if (this.status.state === STATE.QUEUED) {
                                        this.status.pending = false;
                                        this.status.state = STATE.OMITTED;
                                        this.setResult(undefined, true);
                                    } else {
                                        abdStatus.timeout = true;
                                        this.status.state = STATE.TIMEOUT;
                                        this.setResult(false);
                                        win.clearTimeout(this.conf.custom.timeout);
                                    }
                                },
                                custom: {
                                    timeout: undefined,
                                    count: 0,
                                    check: function() {
                                        if (this.status.state !== STATE.RUNNING) { return true; }
                                        if (doc.body.checkVisibility()) {
                                            const height = this.data.element.clientHeight || this.data.element.offsetHeight;
                                            const isBlocked = height > 0 ? false : true;
                                            this.setResult(isBlocked, !isBlocked);
                                            return isBlocked;
                                        } else {
                                            this.setResult(undefined, true);
                                            return false;
                                        }
                                    },
                                },
                            });
                        })();
                        (function() {
                            new Detection({
                                name: 'indirect',
                                types: ['request', 'async'],
                                userConfDefaults: {
                                    dependencies: [
                                        {css_sync: false},
                                        {css_sync: undefined},
                                    ],
                                    url: 'https://imagesrv.adition.com/1x1.gif',
                                    css: {display: 'none'},
                                },
                                initialize: function() {
                                    this.data.pixel = doc.createElement('img');
                                    this.data.pixel.onload = function() {
                                        this.setResult(false);
                                    }.bind(this);
                                    this.data.pixel.onerror = function() {
                                        this.setResult(true);
                                    }.bind(this);
                                    this.data.pixel.src = this.userConf.url;
                                    for (const key of Object.keys(this.userConf.css)) {
                                        this.data.pixel.style[key] = this.userConf.css[key];
                                    }
                                    doc.body.firstElementChild ? doc.body.insertBefore(this.data.pixel, doc.body.firstElementChild) : doc.body.appendChild(this.data.pixel);
                                },
                                runner: function() {
                                },
                                cleanup: function() {
                                    if (this.data.pixel && Array.from(doc.body.childNodes).includes(this.data.pixel)) {
                                        doc.body.removeChild(this.data.pixel);
                                        delete this.data.pixel;
                                    }
                                },
                            });
                        })();
                        (function() {
                            new Detection({
                                name: 'acceptable',
                                types: ['acceptable', 'async', 'request'],
                                userConfDefaults: {
                                    dependencies: [
                                        {css_async: true},
                                        {css_sync: true},
                                    ],
                                },
                                results: {
                                    _global: function(a, b) { return a; },
                                    _default: {},
                                    acceptable: {fyi: function(a, b) { return a === undefined ? b : a && b; }},
                                },
                                initialize: function() {
                                    const abpTest = new function() {
                                        this.alreadyRunning = false;
                                        this.detect = function(url, callback) {
                                            if (typeof callback !== FUNCTION) { return; }
                                            if (abpTest.alreadyRunning) { return; }
                                            abpTest.alreadyRunning = true;
                                            const result = [undefined, undefined];
                                            const rnd = Math.floor((Math.random() * Math.pow(10, 9)) + 1);
                                            const acceptable = new Image;
                                            const blockable = new Image;
                                            url += '?ch=*&rn=' + rnd;
                                            acceptable.onload = function() { setResult(0, true); };
                                            acceptable.onerror = function() { setResult(0, false); };
                                            acceptable.src = url.replace('*', 1);
                                            blockable.onload = function() { setResult(1, true); };
                                            blockable.onerror = function() { setResult(1, false); };
                                            blockable.src = url.replace('*', 2);
                                            function setResult(index, requestSuccess) {
                                                result[index] = requestSuccess;
                                                if (result[0] === undefined || result[1] === undefined) { return; }
                                                callback(result[0] === true && result[1] === false);
                                            }
                                        };
                                    };
                                    abpTest.detect('//static.criteo.net/images/pixel.gif', function(doesAcceptAcceptables) {
                                        if (this.status.state !== STATE.TIMEOUT) {
                                            this.setResult(doesAcceptAcceptables);
                                        }
                                    }.bind(this));
                                },
                                runner: function() {
                                },
                                cleanup: function() {
                                },
                            });
                        })();
                        (function() {
                            new Detection({
                                name: 'united_info',
                                types: ['unitedBlocked', 'request', 'async'],
                                userConfDefaults: {
                                    dependencies: [
                                        {css_async: true},
                                        {css_sync: true},
                                    ],
                                    css: {display: 'none'},
                                },
                                results: {
                                    _global: function(a, b) { return a; },
                                    _default: {},
                                    unitedBlocked: {fyi: function(a, b) { return a === undefined ? b : a && b; }},
                                },
                                initialize: function() {
                                    this.data.pixel = doc.createElement('img');
                                    this.data.pixel.onload = function() {
                                        this.setResult(false);
                                    }.bind(this);
                                    this.data.pixel.onerror = function() {
                                        this.setResult(true);
                                    }.bind(this);
                                    this.data.pixel.src = 'https://united-infos.net/pixel.gif';
                                    for (const key of Object.keys(this.userConf.css)) {
                                        this.data.pixel.style[key] = this.userConf.css[key];
                                    }
                                    doc.body.firstElementChild ? doc.body.insertBefore(this.data.pixel, doc.body.firstElementChild) : doc.body.appendChild(this.data.pixel);
                                },
                                runner: function() {
                                },
                                cleanup: function() {
                                    if (this.data.pixel && Array.from(doc.body.childNodes).includes(this.data.pixel)) {
                                        doc.body.removeChild(this.data.pixel);
                                        delete this.data.pixel;
                                    }
                                },
                            });
                        })();
                        (function() {
                            new Detection({
                                name: 'ff_private',
                                types: ['private', 'sync'],
                                results: {
                                    _global: function(a, b) { return a; },
                                    _default: {},
                                    private: {fyi: function(a, b) { return a === undefined ? b : a && b; }},
                                },
                                initialize: function() {
                                    const isFirefox = typeof InstallTrigger !== UNDEFINED;
                                    const isPrivate = typeof ServiceWorkerContainer === UNDEFINED;
                                    this.setResult(isFirefox && isPrivate);
                                },
                                runner: function() {
                                },
                                cleanup: function() {
                                },
                            });
                        })();
                        if (abdConfig.autostart || abdCopy.autostart) {
                            this.start();
                        }
                    };
                }
            
                if (doc.body) { return createAbd(); }
                const observer = new MutationObserver(function() {
                    if (!doc.body) { return; }
                    createAbd();
                    observer.disconnect();
                });
                observer.observe(docElement, {childList: true, subtree: true});
            })();
            AdService.registerInfo({
                name: 'abd',
                func: () => result || {isBlocked: undefined, didCheck: false, initialized: false},
                persistent: false,
                event: 'body.begin',
                initial: result
            });
        })
        .extendBaseObject('setAdserverFallback', function(func) {
            AdService.on('adblock.detected', func);
            return AdService;
        })
        .extendBaseObject('abdDirectAdServer', function(func) {
            AdService.on('adblock.direct', func);
            return AdService;
        })
        .extendBaseObject('abdIndirectAdServer', function(func) {
            AdService.on('adblock.indirect', func);
            return AdService;
        });
} catch (e) {
    AdService.Module.logError(e);
}
const settings = {
    name: 'adtrgtng',
    initial: false,
    expire: false,
};

let cookieData = {
    d: {},
};

function sanitizeKey(key) {
    if (typeof key === BOOLEAN || typeof key === NUMBER) { return key; }
    if (!key || typeof key !== STRING) { return; }
    return key.toLowerCase().replace(/[^a-z0-9_-]/g, '');
}

function sanitizeValue(value) {
    if (typeof value === BOOLEAN || typeof value === NUMBER) { return value; }
    if (!value || typeof value !== STRING) { return; }
    return value.replace(/[^a-zA-Z0-9._,-]/g, '');
}

function evaluateData() {
    for (const key in cookieData.d) {
        if (!cookieData.d.hasOwnProperty(key)) { continue; }
        if (AdService.getDefaultAdServer().isSecured(key)) {
            delete cookieData.d[key];
            continue;
        }

        const asValue = AdService.getParam(key);
        const value = typeof asValue !== UNDEFINED ? asValue : cookieData.d[key];
        cookieData.d[key] = sanitizeValue(value);
    }
}

try {
    new AdService.Module('cookie', function() {
        AdService.on('initialize.begin', function() {
            cookieData = AdService.Module('cookie').read();
        });
        AdService.on('ad.begin', function() {
            if (!settings.initial) {
                settings.initial = true;
                AdService.Module('cookie').sync();
            }
        });
    })
    .set('write', function() {
        const temp = AdService.Module('cookie').read();
        const original = cookieData;
        cookieData = {
            ...temp,
            d: {...temp.d, ...original.d},
        };
        Object.keys(temp.d).forEach((key) => {
            if (!original.d.hasOwnProperty(key)) {
                delete cookieData.d[key];
            }
        });
        if (Object.keys(cookieData.d).length === 0) {
            delete cookieData.d;
        }

        const data = `${settings.name}=${btoa(JSON.stringify(cookieData))}`;
        const expires = settings.expire ? '; expires=' + new Date(Date.now() + settings.expire * 60 * 60 * 1000).toString() : '';
        const host = window.location.hostname;
        const domain = 'domain=.' + host.split('.').slice(-(host.endsWith('.co.uk') ? 3 : 2)).join('.');
        const path = 'path=/; SameSite=None; Secure';
        document.cookie = [data, expires, domain, path].join('; ');
    })
    .set('read', function() {
        const cookie = getCookieValue(settings.name);
        let data;

        try {
            data = JSON.parse(atob(cookie));
        } catch (e) { }

        if (typeof data?.d !== OBJECT) {
            return {
                ...data,
                d: {},
            };
        }
        return data;
    })
    .set('sync', function() {
        evaluateData();
        for (const key in cookieData.d) {
            AdService.setParam(key, cookieData.d[key]);
        }
        AdService.Module('cookie').write();
    })
    .extendBaseObject('setCookieParam', function(key, value) {
        if (AdService.getDefaultAdServer().isSecured(key)) { return; }
        key = sanitizeKey(key);
        value = sanitizeValue(value);
        if (typeof key === UNDEFINED || typeof value === UNDEFINED) { return; }

        if (!isNaN(value) && value !== null && value !== '') {
            value = Number(value);
        }

        if (!cookieData.d || typeof cookieData.d !== OBJECT) {
            cookieData.d = {};
        }

        cookieData.d[key] = value;
        AdService.setParam(key, value);

        AdService.Module('cookie').write();
    })
    .extendBaseObject('getCookieParam', function(key) {
        key = sanitizeKey(key);

        if (typeof cookieData?.d === OBJECT && cookieData.d.hasOwnProperty(key)) {
            return cookieData.d[key];
        }
    })
    .extendBaseObject('deleteCookieParam', function(key) {
        key = sanitizeKey(key);

        if (cookieData === null || !cookieData.d || typeof cookieData.d !== OBJECT) {
            return;
        }

        if (typeof cookieData?.d === OBJECT) {
            delete cookieData.d[key];
            AdService.setParam(key, undefined);

            AdService.Module('cookie').write();
        }
    })
    ;
} catch (e) {
    AdService.Module.logError(e);
}

var srqUrlParamMapper = {
    wi: 'w',
    ac: 'a',
    browser: 'b',
    os: 'o',
    screen_res: 'r',
    ref: 'e',
};
function handleParam(paramList, key, value, separator, translate) {
    if (!(!!value)) { return; }
    if (translate && typeof srqUrlParamMapper[key] !== UNDEFINED) {
        key = srqUrlParamMapper[key];
    }

    paramList.push(key + separator + value);
}

var previewSlot = {};

try {
    new AdService.Module('adition-asp-single-request', function() {  })
    .set('runSrqModule', function(srqConfig) {
        var singleRequestMessage = function(event) {
            if (!isAcceptedHost(event.origin)) { return; }
            if (!event.data) { return; }
            if (typeof event.data !== STRING) { return; }
            if (!event.data.includes('"singleRequestResponse"')) { return; }

            var singleRequestResponse = JSON.parse(event.data),
                slots = Object.keys(singleRequestObject.slots);

            for (var [position, previewCode] of Object.entries(previewSlot)) {
                singleRequestResponse.singleRequestResponse.b.splice(position, 0, {c: previewCode, w: 5, h: 5});
            }

            for (var i=0, len=slots.length; i<len; i++) {
                var slotData = singleRequestResponse.singleRequestResponse.b[i],
                    slot = slots[i];

                if (slotData.w === 0 && slotData.h === 0) {
                    if (AdService.getConfig('srqTracking', false)) {
                        (new Image()).src = 'https://t.uimserv.net/drp_p/?evtID=915&haID='+AdService.getParam('wi')+'&mediaID=&site='+AdService.getParam('portal')+'&sc=&att1=srqUrl' + encodeURIComponent(slotData.c);
                    }
                    slotData.w = 468;
                    slotData.h = 60;
                    slotData.c = '';
                }

                if (typeof singleRequestObject.slots[slot] === FUNCTION) {
                    var callback = singleRequestObject.slots[slot],
                        callbackData = {
                            'ad': slotData.c,
                            'width': slotData.w,
                            'height': slotData.h,
                            'adsize': slotData.w + 'x' + slotData.h,
                        };

                    callbackData.type = AdService.getDefaultAdServer().getAdFormByMetaData(callbackData);
                    singleRequestObject.slots[slot] = callback(callbackData);
                }

                if (singleRequestObject.slots[slot]) {
                    AdService.AdSlot(slot).queueAdHtml(slotData.c);
                    AdService.refresh(slot);
                }
            }
            window.removeEventListener('message', singleRequestMessage, false);
        };
        window.addEventListener('message', singleRequestMessage);

        var singleRequestObject = srqConfig;
        var slots = Object.keys(srqConfig.slots);

        var singleRequest = (slots.length === 1 && slots[0] === 'prioad') ? this.prioadSingleRequest(slots) : this.generateSingleRequestUrl(slots);

        if (typeof singleRequest === OBJECT) {
            AdService.AdSlot(srqConfig.target || 'srq').queueAdHtml('', 'srq');
            AdService.ad(srqConfig.target || 'srq');
            singleRequestMessage({data: JSON.stringify({singleRequestResponse: singleRequest}), origin: location.host.replace(/^(.*\.)?(web|gmx|1und1|mail)\./, '//dl.$2.')});
        }
        if (typeof singleRequest === STRING) {
            singleRequest = singleRequest.replace(/%20/g, '-');
            var code = `
              <script>
                  var adition = {
                      jp: {
                          h: function(x) {
                              parent.postMessage(JSON.stringify({singleRequestResponse:x}), \'*\');
                          }
                      }
                  }
              <\/script>
              <script src="${singleRequest}" onerror="adition.jp.h({'b':[{'c':'${singleRequest}','w':0,'h':0}]},'failure');"><\/script>`;
            AdService.AdSlot(srqConfig.target || 'srq').setIndependent();
            AdService.AdSlot(srqConfig.target || 'srq').queueAdHtml(code, 'srq');
            AdService.ad(srqConfig.target || 'srq');
        }
        AdService.getDefaultAdServer().increaseRequestCount();
    })
    .set('prioadSingleRequest', function(slot) {
        var requestUrl = AdService.AdSlot(slot[0]).getUrl(),
            slotParameterList = {},
            baseUrl = '//' + AdService.getDefaultAdServer().config.host + '/s?';

        requestUrl.split('?')[1].split('&').forEach(function(pair) {
            var values = pair.split('=');
            slotParameterList[values[0]] = values[1];
        });

        if (slotParameterList.ac === '-1' && slotParameterList['prf[btid_size]']) {
            var code = '<script src="'+requestUrl+'"></script>';
            return {b: [{c: code, w: slotParameterList['prf[btid_size]'].split('x')[0], h: slotParameterList['prf[btid_size]'].split('x')[1]}]};
        }
        if (slotParameterList.ac === '-1') { baseUrl = 'https://uim-adwebfe.server.lan/generic/s.php?'; }
        var learningTag = slotParameterList.lt.slice(0, -1);

        ['lt', 'wpt'].forEach(function(param) {
            delete slotParameterList[param];
        });

        var urlParams = ['t='+slot, 'v=1'],
        profileParams = [];

        Object.keys(slotParameterList).forEach(function(key) {
            if (key.includes('prf[tagid]')) { return; }
            if (!key.includes('prf[')) {
                handleParam(urlParams, key, slotParameterList[key], '=', true);
            } else {
                handleParam(profileParams, (/(prf\[)(\w+)(\])/g.exec(key))[2], slotParameterList[key], '(', false);
            }
        });

        var paramString = urlParams.join('&') + '&p=' + profileParams.join(')'),
            tagid = 'tagid('+slotParameterList['prf[tagid]']+'!tagid('+slotParameterList['prf[tagid]'];

        return baseUrl + paramString + '&lt=' + learningTag + '&s=' + tagid;
    })
    .set('generateSingleRequestUrl', function(slots) {
        var requestUrl = AdService.AdSlot('srq').getUrl(),
            previewSlotIndex = [],
            adserver = AdService.getDefaultAdServer(),
            params = adserver.params,
            baseUrl = '//' + adserver.config.host + '/s?';

        var learningTag = [
            'portal('+params.portal+')',
            'category('+params.category+')',
            'section('+params.section+')',
            'layoutclass('+params.layoutclass,
        ];

        var slotParams = {
            mutual: {},
            slots: {},
        };

        each(slots, function(index, slot) {
            AdService.AdSlot(slot).setParam('tagid', slot);
            var requestUrl = adserver.getUrl(slot),
                parameterList = {},
                tagid;

            if (requestUrl.includes('ac=-1')) {
                previewSlotIndex.push(index);
                previewSlot[index] = `<script src="${requestUrl}"><\/script>`;
            }

            requestUrl.split('?')[1].split('&').forEach(function(pair) {
                var values = pair.split('=');
                if (!['tagid', 'lt', 'wpt', 'ac'].includes(values[0])) {
                    parameterList[values[0]] = values[1];
                    if (slotParams.mutual[values[0]] === values[1]) {
                        delete parameterList[values[0]];
                        if (index === 1) {
                            delete slotParams.slots[Object.keys(slotParams.slots)][values[0]];
                        }
                    }
                } else if (values[0] === 'lt') {
                    tagid = (/tagid\((\w+)\)/g.exec(values[1]))[1];
                }
            });
            if (index === 0) { slotParams.mutual = JSON.parse(JSON.stringify(parameterList)); }
            slotParams.slots[slot] = parameterList;
        });

        if (previewSlotIndex.length > 0) {
            for (var i=previewSlotIndex.length-1; i >= 0; i--) { slots.splice(previewSlotIndex[i], 1); } 
            if (slots.length === 0) { return ''; } 
        }
        var urlParams = ['t='+slots.join('.'), 'v=1'],
            profileParams = [];

        Object.keys(slotParams.mutual).forEach(function(key) {
            if (!key.includes('prf[')) {
                handleParam(urlParams, key, slotParams.mutual[key], '=', true);
            } else {
                handleParam(profileParams, (/(prf\[)(\w+)(\])/g.exec(key))[2], slotParams.mutual[key], '(', false);
            }
        });

        var paramString = urlParams.join('&') + '&p=' + profileParams.join(')');
        var tagidSpecificParams = [];

        for (var i=0, len=slots.length; i<len; i++) {
            var tagid = slotParams.slots[slots[i]]['prf[tagid]'],
                slotParamsList = slotParams.slots[slots[i]],
                slotProfileParams = [];

            if (slots.length > 1) {
                Object.keys(slotParamsList).forEach(function(key) {
                    if (!key.includes('prf[')) {
                        handleParam(slotProfileParams, key, slotParamsList[key], '(', false);
                    } else {
                        handleParam(slotProfileParams, (/(prf\[)(\w+)(\])/g.exec(key))[2], slotParamsList[key], '(', true);
                    }
                });
            }

            tagidSpecificParams.push('tagid('+tagid+'!'+slotProfileParams.join(')')+'*');
        }
        return `${baseUrl}${paramString}&lt=${learningTag.join('')}&s=${tagidSpecificParams.join('').slice(0, -1)}`;
    })
    .extendBaseObject('singleRequestAdition', function(params) {
        if (typeof params === UNDEFINED) { return; }
        if (typeof params !== OBJECT || params === null) {
            var tmp = {
                'slots': {},
            };
            tmp.slots[arguments[0]] = (typeof arguments[1] !== UNDEFINED) ? arguments[1] : true;
            params = tmp;
        }
        AdService.Module('adition-asp-single-request').runSrqModule(params);
    });
} catch (e) {
    AdService.Module.logError(e);
}

(function(self) {
function getOs(ua) {
    if (/xbox\sseries/.test(ua)) { return 31; }
    if (/xbox\sone/.test(ua)) { return 30; }
    if (/playstation\s5/.test(ua)) { return 29; }
    if (/playstation\s4/.test(ua)) { return 28; }
    if (/windows\sphone\s10\.0/.test(ua)) { return 25; }
    if (/windows\sphone\s8\.1/.test(ua)) { return 24; }
    if (/windows\sphone\s8\.0/.test(ua)) { return 23; }
    if (/xbox/.test(ua)) { return 22; }
    if (/symbian/.test(ua)) { return 13; }
    if (/windows\snt\s6\.2;\sarm/.test(ua)) { return 18; }
    if (/cros/.test(ua)) { return 19; }
    if (/windows\snt\s6\.3/.test(ua)) { return 16; }
    if (/windows\snt\s6\.2/.test(ua)) { return 15; }
    if (/iphone|ipad|cfnetwork/.test(ua)) { return 14; }
    if (/android/.test(ua)) { return 12; }
    if (/windows\sce/.test(ua)) { return 11; }
    if (/windows\snt\s10\.0/.test(ua)) { return 17; }
    if (/playstation/.test(ua)) { return 20; }
    if (/nintendo/.test(ua)) { return 21; }
    if (/blackberry/.test(ua)) { return 26; }
    if (/nokia/.test(ua)) { return 27; }
    if (/windows\snt\s6\.1/.test(ua)) { return 10; }
    if (/unix/.test(ua)) { return 7; }
    if (/linux/.test(ua)) { return 6; }
    if (/mac\sos\sx|macintosh|mac\x5fpowerpc/.test(ua)) { return 5; }
    if (/windows\snt\s6\.0/.test(ua)) { return 9; }
    if (/windows\snt\s5\.2/.test(ua)) { return 8; }
    if (/windows\snt\s5\.1/.test(ua)) { return 3; }
    if (/windows\snt\s5\.0/.test(ua)) { return 2; }
    if (/windows\snt\s4\.0/.test(ua)) { return 4; }
    if (/windows\s95|windows\sme|win98|windows\s98/.test(ua)) { return 1; }
    return -1;
};
function getBrowser(ua) {
    if (/spotify/.test(ua)) { return 31; }
    if (/fban|fb_iab/.test(ua)) { return 30; }
    if (/ucbrowser|ucweb/.test(ua)) { return 16; }
    if (/silk/.test(ua)) { return 28; }
    if (/chromium/.test(ua)) { return 17; }
    if (/yabrowser/.test(ua)) { return 20; }
    if (/edge|edg/.test(ua)) { return 15; }
    if (/vivaldi/.test(ua)) { return 23; }
    if (/qqbrowser/.test(ua)) { return 26; }
    if (/maxthon|myie/.test(ua)) { return 24; }
    if (/360se|360ee/.test(ua)) { return 25; }
    if (/iron/.test(ua)) { return 21; }
    if (/iceweasel/.test(ua)) { return 19; }
    if (/metasr/.test(ua)) { return 27; }
    if (/palemoon/.test(ua)) { return 22; }
    if (/seamonkey/.test(ua)) { return 18; }
    if (/trident\/(7\.0)/.test(ua)) { return 14; }
    if (/nokia/.test(ua)) { return 29; }
    if (/msie\s10/.test(ua)) { return 13; }
    if (/msie\s9/.test(ua)) { return 12; }
    if (/chrome\//.test(ua)) { return 11; }
    if (/msie\s8/.test(ua)) { return 10; }
    if (/gecko\//.test(ua)) { return 6; }
    if (/opera|OPR/.test(ua)) { return 7; }
    if (/konqueror|safari/.test(ua)) { return 8; }
    if (/msie\s7/.test(ua)) { return 9; }
    if (/msie\s6/.test(ua)) { return 3; }
    if (/msie\s5/.test(ua)) { return 2; }
    if (/netscape4/.test(ua)) { return 4; }
    if (/netscape6|netscape\/(7\.\d*)/.test(ua)) { return 5; }
    if (/msie\s4/.test(ua)) { return 1; }
    return -1;
};

function getResolution() {
    const resList = {
        '360': {
            '740': 464,
        },
        '640': {
            '480': 1,
        },
        '720': {
            '400': 153,
        },
        '768': {
            '1024': 199,
        },
        '800': {
            '240': 465,
            '352': 466,
            '480': 154,
            '600': 2,
        },
        '832': {
            '624': 159,
        },
        '848': {
            '480': 155,
        },
        '852': {
            '480': 156,
        },
        '854': {
            '480': 467,
        },
        '858': {
            '484': 158,
        },
        '864': {
            '480': 157,
        },
        '960': {
            '540': 160,
            '544': 468,
            '640': 162,
            '720': 163,
        },
        '964': {
            '544': 161,
        },
        '1024': {
            '576': 164,
            '600': 165,
            '640': 469,
            '768': 3,
            '800': 472,
            '1024': 476,
        },
        '1072': {
            '600': 166,
        },
        '1080': {
            '1200': 477,
        },
        '1120': {
            '832': 474,
        },
        '1136': {
            '640': 470,
        },
        '1138': {
            '640': 471,
        },
        '1152': {
            '720': 473,
            '768': 167,
            '864': 4,
            '870': 168,
            '900': 169,
        },
        '1200': {
            '800': 170,
            '900': 171,
        },
        '1280': {
            '720': 172,
            '768': 9,
            '800': 10,
            '854': 173,
            '960': 7,
            '1024': 5,
        },
        '1334': {
            '750': 475,
        },
        '1360': {
            '768': 174,
            '1024': 179,
        },
        '1366': {
            '768': 175,
            '1024': 180,
        },
        '1376': {
            '768': 176,
        },
        '1400': {
            '900': 177,
            '1050': 8,
        },
        '1440': {
            '900': 11,
            '960': 178,
            '1024': 478,
            '1080': 479,
            '1440': 482,
        },
        '1600': {
            '768': 181,
            '900': 182,
            '1024': 183,
            '1200': 6,
            '1280': 481,
        },
        '1680': {
            '1050': 12,
        },
        '1776': {
            '1000': 480,
        },
        '1792': {
            '1344': 483,
        },
        '1800': {
            '1440': 487,
        },
        '1856': {
            '1392': 486,
        },
        '1920': {
            '1080': 184,
            '1200': 13,
            '1280': 484,
            '1400': 185,
            '1440': 186,
        },
        '2048': {
            '1152': 187,
            '1280': 490,
            '1536': 188,
        },
        '2160': {
            '1200': 489,
            '1440': 494,
        },
        '2256': {
            '1504': 496,
        },
        '2280': {
            '1080': 485,
        },
        '2304': {
            '1440': 495,
            '1728': 497,
        },
        '2436': {
            '1125': 491,
        },
        '2538': {
            '1080': 492,
        },
        '2560': {
            '1080': 493,
            '1440': 190,
            '1600': 191,
            '1700': 500,
            '1800': 501,
            '1920': 503,
            '2048': 192,
        },
        '2732': {
            '2048': 507,
        },
        '2736': {
            '1824': 505,
        },
        '2800': {
            '2100': 193,
        },
        '2880': {
            '900': 488,
            '1440': 498,
            '1620': 502,
            '1800': 506,
        },
        '2960': {
            '1440': 499,
        },
        '3000': {
            '2000': 509,
        },
        '3200': {
            '1800': 508,
            '2048': 511,
            '2400': 513,
        },
        '3240': {
            '2160': 512,
        },
        '3440': {
            '1440': 504,
        },
        '3840': {
            '1600': 510,
            '2160': 514,
            '2400': 515,
        },
        '4096': {
            '2304': 516,
            '3072': 519,
        },
        '4480': {
            '2520': 518,
        },
        '4500': {
            '3000': 520,
        },
        '5120': {
            '2160': 517,
            '2880': 521,
            '3200': 522,
            '4096': 523,
        },
        '6016': {
            '3384': 524,
        },
        '6400': {
            '4096': 525,
            '4800': 526,
        },
        '6480': {
            '3240': 527,
        },
        '7680': {
            '4320': 528,
            '4800': 529,
        },
        '8192': {
            '4320': 530,
            '4608': 531,
            '8192': 533,
        },
        '10240': {
            '4320': 532,
        },
        '15360': {
            '8640': 534,
        },
    };
    if (resList[screen.width]?.[screen.height]) { return resList[screen.width][screen.height]; }
    for (const width in resList) {
        if (width < screen.width) { continue; }
        for (const height in resList[width]) {
            if (height < screen.height) { continue; }
            return resList[width][height];
        }
    }
    return -1;
};
    var sizetable = self.info.adformat().sizetable,
        useAutoSetIframeSize = self.info.adformat().useAutoSetIframeSize,
        pageId = {}, 
        uaString = navigator.userAgent.toLowerCase(),
        nextTagId = {},
        tempNextTagId = {},
        wiTimeoutDefault = 30,
    _;
    function getDeviceClass() {
        return uaParams.deviceclass;
    };
    function forceNewPageId(wi) {
        wi = typeof wi === NUMBER || typeof wi === STRING ? wi : generatePageId();
        pageId = {[(new Date).getTime()]: wi};
        return wi;
    };
    function getToolbarInfo() {
        const html = document.documentElement;
        const info = {};
        info.variant = html.getAttribute('united-toolbar-variant');
        info.brand = html.getAttribute('united-toolbar-brand');
        info.version = html.getAttribute('united-toolbar-version');
        return info;
    };
    function getAutUserId(optout) {
        if (optout) {
            document.cookie = '_autuserid2=; SameSite=None; Secure; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            return false;
        }
        const value = /(?:^|; )_autuserid2=([^;]*)/.exec(document.cookie)?.[1] || '';
        if (!value) {
            var ajax = new XMLHttpRequest();
            ajax.onreadystatechange = function() {
                if (this.readyState !== 4 || this.status !== 200) { return; }
                if (ajax.responseText === '0') { return; }
                aditionUserId = ajax.responseText;
                setAutUserIdCookie(ajax.responseText);
            };
            ajax.withCredentials = true;
            self.on('adblock.detected', () => {
                ajax.open('GET', 'https://united-infos.net/i?raw=1&ts='+(new Date().getTime()), true);
                ajax.send();
            });
            self.on('adblock.notdetected', () => {
                ajax.open('GET', 'https://ad11.adfarm1.adition.com/i?raw=1&ts='+(new Date().getTime()), true);
                ajax.send();
            });
        } else {
            setAutUserIdCookie(value);
        }
        return value;
    };
    function setAutUserIdCookie(value) {
        var domain = location.host.split('.').splice(-2).join('.');
        var expires = new Date();
        expires.setTime(expires.getTime() + 2*365*24*60*60*1000);
        document.cookie = '_autuserid2=' + value
                        + '; domain=.' + domain
                        + '; path=/'
                        + '; SameSite=None'
                        + '; Secure'
                        + '; expires=' + expires.toGMTString()
        ;
    };
    var nguserid = getCookieValue('NGUserID'),
        tpid = getCookieValue('tp_id'),
        idcc = getCookieValue('idcc'),
        aditionUserId;
    self.on('server.setparam', function({data}) {
        if (data.params.hasOwnProperty('userid') && typeof data.params.userid !== FUNCTION) {
            aditionUserId = data.params.userid;
        }
    });

    function getPreviewCookieData(tagid) {
        const cookieDefault = {
            ids: {},
            expires: new Date().getTime() + (5 * 60 * 1000)
        };
        try {
            const cookieValue = extend(cookieDefault, JSON.parse(atob(getCookieValue('bannerPrev'))));
            if (typeof cookieValue.ids !== OBJECT) {
                cookieValue.ids = {};
            }
            if (typeof tagid !== STRING) { return cookieValue; }
            const placement = `${self.getParam('category')}:${tagid}`;
            const bidCounter = cookieValue.ids[placement];
            if (!Array.isArray(bidCounter)) { return undefined; }
            if (typeof bidCounter[0] !== NUMBER || typeof bidCounter[1] !== NUMBER) { return undefined; }
            setPreviewCookieData({[placement]: bidCounter[0]});
            return bidCounter[0];
        } catch { }
        return typeof tagid === STRING ? undefined : cookieDefault;
    }

    function setPreviewCookieData(placementBids, forced) {
        const cookieValue = getPreviewCookieData();
        const now = new Date().getTime();
        const soon = now + (5 * 60 * 1000);
        if (forced || typeof cookieValue.expires !== NUMBER || cookieValue.expires < now || cookieValue.expires > soon) {
            cookieValue.expires = soon;
        }
        for (const placement of Object.keys(placementBids)) {
            let counter = cookieValue.ids[placement]?.[1];
            counter = !forced && typeof counter === NUMBER ? counter-1 : 5;
            if (counter > 0 && counter <= 5) {
                cookieValue.ids[placement] = [placementBids[placement], counter];
            } else {
                delete cookieValue.ids[placement];
            }
        }
        const expires = Object.values(cookieValue.ids).length ? (new Date(cookieValue.expires).toUTCString()) : 'Thu, 01 Jan 1970 00:00:00 GMT';
        const domain = '.' + location.hostname.split('.').slice(-(location.hostname.endsWith('.co.uk') ? 3 : 2)).join('.');
        document.cookie = `bannerPrev=${btoa(JSON.stringify(cookieValue))}; domain=${domain}; expires=${expires}; path=/; SameSite=None; Secure`;
    }
    ((pt) => {
        if (!pt) { return; }
        const ptBidPlacements = {};
        decodeURIComponent(pt).replace(/(?:^|;)(\d+),(\w+:\w*\w(?=(?:;|$)))/g, (_, bid, placement) => ptBidPlacements[placement] = +bid);
        if (!Object.values(ptBidPlacements).length) { return; }
        setPreviewCookieData(ptBidPlacements, true);
    })(Const.QUERYPARAMS['_ad.pt'])
    function setSecuredParam(name, value) {
        self.getDefaultAdServer()[securedSymbol].setSecuredParam(name, value);
    }

    var uaParams = self.info.ua(),
        createAdServer = function(id, config, params) {
            var tagidListForPrf = {};
            var posCount = {};
            var ac = 1;
            var server = new AdService.AdServer(id, extend({
                probableDefault: true,
                host: 'ad11.adfarm1.adition.com',
                path: 'lt',
                paramOrder: {
                    begin: [
                        'wpt', 'nw', 'lt', 'ac', 'portal', 'category', 'section', 'tagid', 'layoutclass',
                        'posIndex', 'posCount', 'wi', 'ref',
                        'os', 'browser', 'screen_res', 'iframe', 'external_uid', 'uid_stable',
                        'optout', 'userid', 'gdpr', 'gdpr_consent', 'shb', 'userid[netid.de]', 'userid[aaid]', 'userid[idfa]',
                        'prf[slot]', 'prf[portal]', 'prf[category]', 'prf[section]', 'prf[tagid]', 'prf[layoutclass]',
                        'prf[deviceclass]', 'prf[deviceclient]',
                        'prf[sectionlong]', 'prf[categorytype]',
                        'prf[os]', 'prf[browser]', 'prf[lang]', 'prf[screen_res]', 'prf[iframe]',
                        'prf[sys]', 'prf[sysv]', 'prf[cl]', 'prf[clv]', 'prf[vpw]', 'prf[vph]', 'prf[net]',
                        'prf[external_uid]', 'prf[hid]', 'prf[nguserid]', 'prf[tpid]', 'prf[uids]', 'prf[optout]',
                        'prf[shb]', 'prf[pp]', 'prf[pa]', 'prf[pg]'
                    ],
                    end:    ['prf[pageview]', 'debug'],
                    ignore: ['posName','prf[sizes]']
                },
                evenIfEmpty: ['ref', 'external_uid', 'uid_stable', 'prf[nguserid]', 'prf[uids]', 'prf[hid]', 'prf[net]'],
                paramTypes: {
                    direct: ['lt', 'ac', 'nw', 'uid_stable', 'userid', 'wpt', 'sid', 'bid', 'kid', 'ref', 'gdpr', 'gdpr_consent', 'debug', 'userid[netid.de]', 'userid[aaid]', 'userid[idfa]'],
                    both:   ['wi', 'os', 'browser', 'iframe', 'screen_res', 'optout', 'external_uid', 'ipt', 'consentLevel', 'shb'],
                    ignore: ['pt']
                },
                nonContentElements: ['uim:ad'],
                init: function() {
                    var info = getToolbarInfo();
                    this.setParam({
                        toolbar: !!info.version ? '1' : '0',
                        brandedbrowser: info.variant === 'bundle' ? '1' : '0',
                    });
                    this.setParam({
                        uid_stable: tpid ? '1' : '0',
                        uids: tpid ? '1' : '0',
                        external_uid: tpid,
                        nguserid: nguserid,
                        optout: nguserid==='TGP-OPT-OUT' ? '1' : '0',
                        google_ac: '0',
                    });
                    this.setParam({ tpid: tpid });
                    if (idcc === '2') { this.setParam({ 'userid[netid.de]' : tpid }); }
                    var dnt = window.doNotTrack == '1' || navigator.doNotTrack == 'yes' || navigator.doNotTrack == '1' || navigator.msDoNotTrack == '1';
                    this.setParam({
                        dnt: dnt ? '1' : '0'
                    });
                    this.setParam({
                        os: getOs(uaString),
                        browser: getBrowser(uaString)
                    });
                }
            }, config), extend({
                ac: () => {
                    return ac;
                },
                nw: 42,
                deviceclient: 'browser',
                wpt: 'j',
                deviceclass:  uaParams.deviceclass,
                external_uid: '',
                uid_stable:   0,
                wi:           forceNewPageId(),
                ref:          location.origin + location.pathname || '',
                iframe:       1,
                screen_res:   getResolution(),
                cl:           uaParams.browser.name ? uaParams.browser.name.slice(0, 3) : undefined,
                clv:          uaParams.browser.version,
                sys:          uaParams.os.name ? uaParams.os.name.slice(0, 3) : undefined,
                sysv:         uaParams.os.version,
                vpw:          document.documentElement.offsetWidth,
                vph:          window.innerHeight,
                r:            self.info.device().retina ? '1' : undefined,
                net:          uaParams.net,
                lang:         navigator.language,
                tagid: (slot) => {
                    if (slot instanceof AdService.AdSlot) {
                        return slot.id;
                    }
                },
                slot: (slot) => {
                    if (slot instanceof AdService.AdSlot) {
                        return slot.id;
                    }
                },
                userid: () => {
                    return aditionUserId;
                }
            }, params)
        );

        server.isAditionServer = true;

        server.isTagIdSame = function(slot) {
            params = server.getParamsForSlot(slot);
            if (params.tagid in tagidListForPrf && (!params.tagid.includes('_x'))) { params.tagid += '_x'; }
            if (nextTagId[slot.id]) { params.tagid = nextTagId[slot.id]; }
            return params.tagid === slot.id;
        };

        server._specialParams = {
            _order: ['tagid_x', 'lt', 'maxwidth', 'bannerid', 'btid', 'asyncParams'],
            tagid_x: function(params, slot) {
                var rawTagid = (''+(nextTagId[slot.id]||params.tagid)).replace(/_x/g, '');
                if (params.tagid in tagidListForPrf && !params.tagid.includes('_x') && !slot.config.disableTagid_x) {
                    params.tagid += '_x';
                }
                if (nextTagId[slot.id]) {
                    params.tagid = nextTagId[slot.id];
                    tempNextTagId[slot.id] = nextTagId[slot.id];
                    delete nextTagId[slot.id];
                }
                posCount[rawTagid] = (posCount[rawTagid] || 0) + 1;
                params.pos = posCount[rawTagid];
                tagidListForPrf[params.tagid] = true;
            },
            lt: function(params, slot) {
                this.config.sepNameValue = '(';
                this.config.sepParams = ')';
                var lt = this._getParamString({
                    portal: params.portal,
                    category: params.category,
                    section: params.section,
                    tagid: params.tagid,
                    layoutclass: params.layoutclass
                }, slot);
                this.config.sepNameValue = '=';
                this.config.sepParams = '&';
                params.lt = lt + ')';
            },
            maxwidth: function(params, slot) {
                if (!params.hasOwnProperty('maxwidth')) {
                    params.maxwidth = slot.getContainerWidth();
                }
            },
            bannerid: function(params, slot) {
                if (params.bannerid) {
                    const bid = new RegExp('(\\d+),'+slot.id+'($|;)').exec(params.bannerid)?.[1];
                    if (bid) {
                        params.bid = bid;
                        params.ac = -1;
                    }
                    delete params.bannerid;
                }
            },
            btid: function(params, slot) {
                if (tempNextTagId[slot.id]) { return; }
                const bid = (params.btid ? new RegExp('(\\d+),'+params.tagid+'($|;)').exec(params.btid)?.[1] : undefined) ?? getPreviewCookieData(slot.id);
                if (typeof bid !== UNDEFINED) {
                    params.bid = bid;
                    params.ac = -1;
                }
                delete tempNextTagId[slot.id];
                delete params.btid;
            },
            asyncParams: function(params) {
                params.cdl = cookieLabel;
                params.applicationarea = window.utag_data?.applicationArea;
                params.pageviewid = window.utag_data?.pageViewId;
            }
        };

        self.on('adblock.detectionfinished', () => { 
            const abdResult = self.info.abd();
            if (abdResult.directBlocked) {
                server.params.acceptableads = abdResult.details.fyi.acceptable ? 1 : 0;
            }
        });

        server.prepareParamsForUrl = function(params, slot) {
            var server = this;
            params = params || {};
            slot = self._getAdSlot(slot);
            each(server._specialParams._order, (i, name) => {
                server._specialParams[name].call(server, params, slot);
            });
            each(params, (key, value) => {
                if (!server.config.paramTypes.direct.includes(key) && !server.config.paramTypes.ignore.includes(key)) {
                    if (key.startsWith('prf[')) {
                        params[key] = value;
                    } else if (!params.hasOwnProperty('prf['+key+']')) {
                        params['prf['+key+']'] = value;
                    }
                }
            });
            each(params, function(key, value) {
                if (!key.includes('prf[') && !server.config.paramTypes.direct.includes(key) && !server.config.paramTypes.both.includes(key)) {
                    delete params[key];
                }
                if (server.config.paramTypes.ignore.includes(key)) {
                    delete params[key];
                }
            });
            var directParams = extend({}, server.directParams, slot.directParams);
            params = extend(params, directParams);
            for (const [key, value] of Object.entries(this[securedSymbol].getSecuredParam())) {
                if(directParams[key]) {
                    params = extend(params, {[key]:value});
                }
            }
            return params;
        };

        server.getUrl = function(slot, requestMethod) {
            slot = self._getAdSlot(slot);
            const params = this.getUrlParams(slot);
            let url = this.config.protocol + this.config.host + '/' + this.config.path + this.config.sepQuery;
            url += this._getParamString(params, slot);
            return url;
        };

        server.renderMeta = function(html, meta) {
            var metaHtml = '<uim:ad xmlns:uim="urn:publicid:-:UIM:adinfo" ';
            each(meta, (name, value) => {
                if (/^[\w\.\:\-, ]+$/.test(name+value)) {
                    metaHtml += name+'="'+value+'" ';
                }
            });
            metaHtml += '></uim:ad>\n';
            return metaHtml + html;
        };
        server.exportMetaData = function() {
            const metaData = {};
            const adTagAttributes = /^<uim:ad\s([^>]+)><\/uim:ad>$/.exec(document.getElementsByTagName('uim:ad')[0]?.outerHTML)?.[1];
            if (adTagAttributes) {
                adTagAttributes.split(' ').forEach((keyVal) => {
                    keyVal = keyVal.split('=');
                    if (keyVal.length === 2) {
                        metaData[keyVal[0]] = keyVal[1].slice(1, -1);
                    }
                });
            }
            self.sendMessage(parent, 'importMetaData', metaData);
            return this;
        };
        server.importMetaData = function(data) {
            var uimAdTags = data.iframe.parentNode.getElementsByTagName('uim:ad');
            if (uimAdTags) {
                for (var i=uimAdTags.length-1; i>=0; i--) {
                    data.iframe.parentNode.removeChild(uimAdTags[i]);
                }
            }
            data.iframe.insertAdjacentHTML('beforebegin', this.renderMeta('', data.message.data));
        };

        server.newPageContext = function(wi) {
            this.setParam('wi', forceNewPageId(wi));
            return this;
        };
        server.renewPageId = function() {
            var newTimestamp = (new Date).getTime();
            var lastTimestamp = Object.keys(pageId)[0];
            var diff = newTimestamp - lastTimestamp;
            var wi = diff > self.getConfig('wiTimeout', wiTimeoutDefault)*1000 ? generatePageId() : pageId[lastTimestamp];
            this.setParam('wi', forceNewPageId(wi));
            return wi;
        };
        server.getAcAndIncrement = function() {
            var tmpAc = ac
            server.increaseRequestCount();
            return tmpAc;
        };
        server.getAdSize = function(domNode) {
            try {
                var adSize = domNode.getElementsByTagName('uim\:ad')[0].getAttribute('adsize');
                var dimensions = adSize.split('x');
                return {
                    width: dimensions[0],
                    height: dimensions[1]
                }
            } catch (e) { }
            return {};
        };
        server.getAdFormByMetaData = function(meta, fallbackAllowed) {
            return meta ? (sizetable(meta.adsize).format || (fallbackAllowed ? meta.adsize : undefined)) : undefined;
        };
        server.getEventsByMetaData = function(meta) {
            return {
                form: server.getAdFormByMetaData(meta),
                size: meta.adsize,
            _:0};
        };

        server.splitMultiResponse = (domNode) => {
            const parts = [... domNode.childNodes].filter((node) => node.nodeName.toLowerCase() === 'div');
            return parts;
        };

        server.increaseRequestCount = function(newAc) {
            typeof newAc === NUMBER ? ac = newAc : ac++;
        };

        server.checkUrlValidity = function(url) {
            url = parseUrl(url);
            return (server.config.validHosts || [server.config.host]).includes(url.hostname);
        };

        server.resetSlot = function(slot) {
            if (!slot) { return self; }
            const tagid = server.params.tagid(self._getAdSlot(slot));
            delete tagidListForPrf[tagid];
        }

        server._global.initiateNativeTracking = function(data, domNode) {
            self.sendNativeTrackingInfo(JSON.stringify(data));
            data.action = 'click';
            domNode.addEventListener('click', function() {
                if (arguments[0].button !== 0) { return; }
                self.sendNativeTrackingInfo(JSON.stringify(data));
            });
            domNode.addEventListener('auxclick', function() {
                if (arguments[0].button !== 1) { return; }
                self.sendNativeTrackingInfo(JSON.stringify(data));
            });
        }

        self.on('ad.urlReady', (evt) => {
            if (evt.slot && !evt.slot.hasQueuedAdHtml() && evt.slot.getAdServer().isAditionServer && evt.slot.getAdServer().id === server.id) { 
                server.increaseRequestCount();
            }
        });

        return server;
    };

    self.on('server.setparam', (evt) => {
        if (evt.data.params.tpid) {
            evt.data.params.external_uid = evt.data.params.tpid;
            evt.data.params.uid_stable = 1;
            evt.data.params.uids = 1;
        }
    });

    self.on('ad.property.nextTagId', (evt) => {
        nextTagId[evt.slot.id] = evt.data.nextTagId;
    });

    self.getDeviceClass = self.getDeviceClass || function() {
        return self.getParam('deviceclass');
    };

    self.disableTagid_x = function(slots) {
        if (typeof slots === STRING) { slots = [slots]; }
        if (!(slots instanceof Array)) { return self; }

        slots.forEach((slot) => {
            self.AdSlot(slot).setConfig('disableTagid_x',true);
        })        
        return self;
    }

    var here = location.host.split('.').slice(-2).join('.'),
        host = 'united-infos.net';
    switch (here) {
        case 'gmx.at':
        case 'gmx.ch':
        case 'gmx.net':
            host = 'info.gmx.net';
            break;
        case '1und1.de':
        case 'web.de':
            host = 'info.'+here;
            break;
    }
    var serverDomain = {
        'src_domain': 'uimserv.net',
        'prefix_img': 'adimg.',
        'prefix_vid': 'advideo.'
    };
    if (AdService.info._ua.browser.name === 'firefox' && typeof ServiceWorkerContainer === UNDEFINED) {
        serverDomain = {
            'src_domain': `i0.${here}`,
            'suffix_img': '/uimimg',
            'suffix_vid': '/uimvideo'
        }
    }
    var AditionAspAdServer = createAdServer('adition-asp', {
        firstUse: function() {
            this.params = extend({}, AditionAspAdServer.params, this.params, serverDomain);
        }
    });
    var AditionAspInfoServer = createAdServer('adition-asp-info', {
        host: host,
        validHosts: ['united-infos.net', 'info.1und1.de', 'info.gmx.net', 'info.web.de'],
        firstUse: function() {
            this.config = extend({}, AditionAspAdServer.config, this.config);
            this.params = extend({}, AditionAspAdServer.params, this.params, serverDomain);
        }
    }, {
        tagid: (slot) => {
            return (slot instanceof AdService.AdSlot && slot.getConfig('fallbackTagid')) || (slot.id + '_info');
        }
    });
    var AditionAspInfoServer = createAdServer('adition-asp-united-infos', {
        host: 'united-infos.net',
        firstUse: function() {
            this.config = extend({}, AditionAspAdServer.config, this.config);
            this.params = extend({}, AditionAspAdServer.params, this.params, serverDomain);
        }
    }, {
        tagid: (slot) => {
            return (slot instanceof AdService.AdSlot && slot.getConfig('fallbackTagid')) || (slot.id + '_united');
        }
    });
    var uimservHost = AdService.info._ua.browser.name === 'firefox' ? host.replace('info.','united.') : 'united.uimserv.net';
    var AditionAspInfoServer = createAdServer('adition-asp-united-uimserv', {
        host: uimservHost,
        firstUse: function() {
            if (AdService.info._ua.browser.name === 'firefox') {
                serverDomain = {
                    'src_domain': `i0.${here}`,
                    'suffix_img': '/uimimg',
                    'suffix_vid': '/uimvideo'
                }
            }
            this.config = extend({}, AditionAspAdServer.config, this.config);
            this.params = extend({}, AditionAspAdServer.params, this.params, serverDomain);
        }
    }, {
        tagid: (slot) => {
            return (slot instanceof AdService.AdSlot && slot.getConfig('fallbackTagid')) || (slot.id + '');
        }
    });
    var AditionAspInfoServer = createAdServer('adition-asp-consent-uimserv', {
        host: uimservHost,
        firstUse: function() {
            this.config = extend({}, AditionAspAdServer.config, this.config);
            this.params = extend({}, AditionAspAdServer.params, this.params, serverDomain);
        }
    });
    var AditionAspMailInt = createAdServer('adition-asp-consent-mail-int', {
        host: 'united.uimserv.net',
        firstUse: function() {
            this.config = extend({}, AditionAspAdServer.config, this.config);
            this.params = extend({}, AditionAspAdServer.params, this.params, serverDomain);
        }
    });

    if (self._isConnector) {
        self.adMeta = function(meta) {
            self._adMeta = meta;
            self.sendMessage(parent, 'adMeta', arguments, (evt) => {
                self.trigger('answer.adMeta', evt);
            });
            return self;
        };
    } else {
        self.adMeta = function(meta) {
            self._getCurrentAdSlot().addMetaData(meta);
            return self;
        };
    }

    if (self._isConnector) {
        self.on('ad.finished', function(evt) {
            if (!self._adMeta) {
                var uimAd = document.getElementsByTagName('uim\:ad')[0],
                    attr = uimAd ? uimAd.getAttribute : () => undefined,
                    meta = {
                        contentunitid: attr.call(uimAd, 'contentunitid'),
                        campaignid: attr.call(uimAd, 'campaignid'),
                        bannerid: attr.call(uimAd, 'bannerid'),
                        portal: attr.call(uimAd, 'portal'),
                        category: attr.call(uimAd, 'category'),
                        section: attr.call(uimAd, 'section'),
                        tagid: attr.call(uimAd, 'tagid'),
                        layoutclass: attr.call(uimAd, 'layoutclass'),
                        width: attr.call(uimAd, 'width'),
                        height: attr.call(uimAd, 'height'),
                        adsize: attr.call(uimAd, 'adsize'),
                        ovkid: attr.call(uimAd, 'ovkid')
                    },
                _;
                self.adMeta(meta);
            }
        });
    }

self.on('initialize.end', function() {
    const uiConsent = self.info.consent();

    function hasConsent(userConsent, consentToCheck, doFullCheck = false) {
        if (uiConsent.fullConsent && !doFullCheck) { return true; }
        return uiConsent.hasConsent(userConsent, consentToCheck);
    }
    function hasAcConsent(acString) {
        const [version, value] = acString.split('~');
        return !(!value || value.startsWith('dv.'));
    }

    if (uiConsent.tcString) {
        setSecuredParam('gdpr', 1);
        setSecuredParam('gdpr_consent', uiConsent.tcString);
        setSecuredParam('prf[optout]', self.getParam('optout'));
        setSecuredParam('optout', undefined);
    } else {
        setSecuredParam('prf[optout]', 1);
        setSecuredParam('optout', 1);
    }
    setSecuredParam('tcf_ven', ','+uiConsent.vendorList.join(',')+',');
    setSecuredParam('google_ac', hasAcConsent(uiConsent.acString) ? '1' : '0');

    if (!hasConsent(uiConsent.publisher, [1, 2]) && self.getParam('deviceclient')==='browser' && !(!!self.info.abd?.().isBlocked)) {
        self.setDefaultAdServer('adition-asp-consent-uimserv');
    }

    if (hasConsent(uiConsent.publisher, [1])) {
        aditionUserId = getAutUserId(nguserid==='TGP-OPT-OUT') || undefined;
        if (idcc && /^([a-zA-Z0-9])+$/.test(idcc)) {
            setSecuredParam('idcc', idcc);
        }
    }

    if (!hasConsent(uiConsent.publisher, [2])) {
        [
            'deviceclass', 'cl', 'clv', 'sys', 'sysv', 'os', 'browser', 'screen_res',
            'vpw', 'vph', 'toolbar', 'brandedbrowser', 'weather_temp', 'weather_condition', 'clktype',
            'view', 'camera', 'hw', 'hwversion', 'appvers', 'd', 'retina', 'orient',
            'net', 'apilevel', 'interstitial', 'screensizewidth', 'screensizeheight', 'maxsize',
        ].forEach(function(item) {
            setSecuredParam(item, undefined);
        });
    }

    if (!hasConsent(uiConsent.publisher, [1, 3])) {
        ['prf[hid]', 'prf[nguserid]', 'prf[external_uid]', 'prf[idfa]', 'prf[device-idfa]', 'prf[tpid]'].forEach((item) => {
            setSecuredParam(item, undefined);
        });
    }

    if (!hasConsent(uiConsent.publisher, [1, 2, 4])) {
        setSecuredParam('prf[lang]', undefined);
    }

    if (!hasConsent(uiConsent.publisher, [1, 3, 4])) {
        setSecuredParam('prf[cdl]', undefined);
        setSecuredParam('external_uid', undefined);
    }

    if (!hasConsent(uiConsent.publisher, [1, 2, 3, 4, 7])) {
        ['userid', 'userid[aaid]', 'userid[idfa]', 'userid[adserver.org]', 'userid[criteo.com]', 'userid[pubcid.org]', 'shb'].forEach((item) => {
            setSecuredParam(item, undefined);
        });
    }

    if (!hasConsent(uiConsent.publisher, [1, 2, 3, 4, 7]) || !hasConsent(uiConsent.customPurpose.adition, [10], true)) {
        setSecuredParam('userid[netid.de]', undefined);
    }

    if (!hasConsent(uiConsent.publisher, [3, 4])) {
        ['pa', 'pp', 'pg', 'userlevel'].forEach((item) => {
            setSecuredParam(item, undefined);
        });
    }

    setSecuredParam('tcf_pub', ','+uiConsent.publisher.join()+',');
    setSecuredParam('tcf_pcp', ','+uiConsent.customPurpose.adition.join()+',');
    setSecuredParam('tcf_pur', ','+uiConsent.purpose.join()+',');
    setSecuredParam('tcf_spe', ','+uiConsent.specialFeature.join()+',');
    setSecuredParam('tcf_vv', uiConsent.vendorListVersion);
    setSecuredParam('tcf_pv', uiConsent.tcfPolicyVersion);
});
}).apply(self, [AdService]);
if (/(\/\/|\.)(mail\.com|gmx\.(co\.uk|com|es|fr))|localhost/.test(location.host)) {
    (function(self) {
        let singleRequestEnabled = false;
        const sizetable = self.info.adformat().sizetable;
        const googleSlots = {};
        const requestedGoogleSlots = {};

        const globalParamsBlacklist = ['tcf_pub', 'tcf_pur', 'tcf_spe', 'tcf_ven', 'tcf_version', 'tcf_pcp', 'google_ac', 'gdpr', 'gdpr_consent', 'prf[optout]', 'optout', 'hasConsent', 'skv', 'wpt'];
        const slotParamsBlacklist = ['sizes', 'isRefresh'];


        function getAdTargeting() {
            try {
                const cookieParams = window.atob(getCookieValue('adtrgtng'));
                return JSON.parse(cookieParams).d;
            } catch (e) { }
            return {};
        }
        function getConsent() {
            if (/(?:^|; )gdna=[^;]*/i.test(document.cookie) && !/(?:^|; )euconsent-v2=[^;]*/i.test(document.cookie)) { return true; }
            setSecuredParam('tcf_pcp', ','+self.info.consent().customPurpose.google.join()+',');
            return self.info.consent().fullConsent ? true : self.info.consent().google;
        }

        function setSecuredParam(name, value) {
            self.getDefaultAdServer()[securedSymbol].setSecuredParam(name, value);
        }

        function setParamsfromString(values) {
            const keyValueSlot = values.split(';');
            keyValueSlot.forEach((value) => {
                const kvs = value.split(',');
                self.AdSlot(kvs[2]).setParam(kvs[0], kvs[1]);
            });
        }

        function createGoogleScript() {
            if (self._isConnector) { return; }
            const script = document.createElement('script');

            script.onload = () => {
                self.triggerOnce('gpt.loaded');
            };
            script.onerror = () => {
                self.triggerOnce('gpt.notloaded');
            };

            script.src = 'https://securepubads.g.doubleclick.net/tag/js/gpt.js';
            script.async = true;

            window.googletag = {cmd: []};

            document.head.appendChild(script);
        }

        function setGoogleDebug() {
            let googleDebug = Const.QUERYPARAMS['google-debug'];
            if (googleDebug === 'true' || googleDebug === 'false') {
                googleDebug = googleDebug === 'true';
                googletag.cmd.push(((googleDebug) => {
                    googleDebug ? googletag.openConsole() : googletag.disablePublisherConsole();
                })(googleDebug));
            }
        }

        self.on('server.setconfig', function(evt) {
            const secure = AdService.getConfig('secure');
            if (typeof googletag === UNDEFINED) { return; }
            if (secure) {
                googletag.cmd.push(() => {
                    var forcedSafe = googletag.pubads().setForceSafeFrame(secure);
                    log('[Google] - server.setconfig secure: ', forcedSafe);
                    if (typeof evt.data.config.safeFrameConfig !== UNDEFINED) {
                        googletag.pubads().setSafeFrameConfig(evt.data.config.safeFrameConfig);
                    }
                });
            }
            if (evt.data.config.singleRequest) {
                googletag.cmd.push(() => {
                    var singleRequest = googletag.pubads().enableSingleRequest();
                    log('[Google] - server.setconfig singlerequest: ', singleRequest);
                    singleRequestEnabled = singleRequest;
                });
            }
            if (!!evt.data.config.page_url) {
                googletag.cmd.push(() => {
                    googletag.pubads().set('page_url', evt.data.config.page_url);
                });
            }
            if (evt.data.config.page_url === undefined) {
                googletag.cmd.push(() => {
                    googletag.pubads().set('page_url', location.origin);
                });
            }
        });

        self.on('adblock.detected', function() {
            server.disable();
        });
        self.on('gpt.notloaded', function(evt) {
            log('[Google] - gpt script not loaded', evt);
            server.disable();
        });

        var server = new AdService.AdServer('google', {
            probableDefault: true,
            init: function() {
                var hasConsent = getConsent();
                this.setParam('hasConsent', hasConsent);
                log('[Google] - getConsent: ', hasConsent);
                if (!hasConsent) {
                    this.setConfig('enabled', false);
                    self.trigger('server.disabled', self._getCurrentAdSlot(), {server: this});
                    return self;
                }
                createGoogleScript();

                var cookieParams = getAdTargeting(),
                    tpid = getCookieValue('tp_id').replace(/_|-/g, '').slice(-55, -13);
                this.setParam(cookieParams);
                each(cookieParams, function(key, value) {
                    googletag.cmd.push(() => {
                        if (typeof value !== UNDEFINED) {
                            googletag.pubads().setTargeting(key, ''+value);
                        }
                    });
                });

                googletag.cmd.push(() => {
                    googletag.pubads().setTargeting('connector', self.getConfig('adservice-iframe-script-url'));
                    googletag.pubads().setTargeting('server', 'google');
                });
                if (tpid) {
                    googletag.cmd.push(() => {
                        googletag.pubads().setPublisherProvidedId(tpid);
                    });
                }
                if (this.getParams().kvs) {
                    log('[Google] - set kvs: ', this.getParams().kvs);
                    setParamsfromString(this.getParams().kvs);
                }
            },
        }, {

        });

        server.newPageContext = function() {
            googletag.cmd.push(() => {
                googletag.pubads().updateCorrelator();
            });
            return this;
        };

        server.getAdSize = function(domNode) {
            try {
                var child = domNode.querySelector('div');
                if (!child.style.width && !child.style.height && child.querySelector('iframe')) {
                    return {
                        width: child.querySelector('iframe').width,
                        height: child.querySelector('iframe').height,
                    };
                } else {
                    return {
                        width: child.style.width.replace('px', ''),
                        height: child.style.height.replace('px', ''),
                    };
                }
            } catch (e) {
                log.error('[Google] - getAdSize', 'domNode', domNode, 'first div element', domNode.querySelector('div'), 'first iframe element', domNode.querySelector('iframe'));
            }
            return {};
        };

        function adsizeToString(adsize) {
            return adsize.width && adsize.height ? adsize.width + 'x' + adsize.height : adsize.width === 0 && adsize.height === 0 ? 'fluid' : undefined;
        }

        server.splitMultiResponse = function(domNode) { return []; };
        server.getAdFormByMetaData = function(meta, fallbackAllowed) {
            return meta ? (sizetable(meta.adsize).format || (fallbackAllowed ? meta.adsize : undefined)) : undefined;
        };
        server.getEventsByMetaData = function(meta) {
            return {
                form: server.getAdFormByMetaData(meta),
                size: meta.adsize,
            };
        };

        server.getMetaData = function(slot, googleResponse) {
            var googleSlot = this.getGoogleSlot(slot.id),
                responseInformation = googleSlot.getResponseInformation(),
                adsize;

            if (Array.isArray(googleResponse.size) && googleResponse.size.length === 2) {
                adsize = {
                    width: googleResponse.size[0],
                    height: googleResponse.size[1],
                };
            }

            var adMeta = {
                adsize: adsizeToString(adsize || server.getAdSize(slot.getDomNode())),
                tagid: slot.id,
                portal: this.params.portal,
                category: this.params.category,
                section: this.params.section,
                network: this.params.network,
                sectionlong: this.params.sectionlong,
            };
            var metaData = extend({}, adMeta, responseInformation, {isEmpty: googleResponse.isEmpty});

            slot.clearMetaData();
            slot.addMetaData(metaData);

            return metaData;
        };

        server.renderMeta = function(domNode, googleResponse) {
            var slot = self._getAdSlot(domNode);
            var metaHtml = '<uim:ad xmlns:uim="urn:publicid:-:UIM:adinfo" ';
            each(this.getMetaData(slot, googleResponse), (name, value) => {
                metaHtml += name+'="' + value + '" ';
            });
            metaHtml += '></uim:ad>\n';
            slot.getTarget().insertAdjacentHTML('afterbegin', metaHtml);
        };

        server.setSizes = function(sizeTable) {
            each(sizeTable, (slot, sizes) => {
                self.AdSlot(slot).setParam('sizes', sizes);
            });
        };

        server.setSecureSignalProviders = (providers) => {
            if (typeof providers !== OBJECT) { return; }
            googletag.secureSignalProviders = googletag.secureSignalProviders || [];
            for (const id in providers) {
                if (typeof providers[id] !== STRING) { continue; }
                googletag.secureSignalProviders.push({
                    id,
                    collectorFunction: () => Promise.resolve(providers[id]),
                });
            }
        };

        server.isAlive = () => true;
        server.isSingleRequestEnabled = () => singleRequestEnabled;

        server.getGoogleSlots = function(slots) {
            if (Array.isArray(slots)) { return slots.map((slot) => server.getGoogleSlot(slot)).filter(Boolean); }
            return googleSlots;
        };
        server.getGoogleSlot = function(slot) {
            return googleSlots[slot];
        };

        server.getAdUnitPath = function(slot) {
            return ['', server.params.network, server.params.portal, server.params.category, server.params.section, slot.params.tagid || slot.id].join('/');
        };

        server.isTagIdSame = function(slot) {
            if (server.getConfig('enable-refresh-tagid', true)) { return false; }
            return true;
        };

        server._global.slotDomNodeInit = function(slot) {
            if (!slot.getAdServer().isEnabled()) {
                const data = {
                    slot,
                    cancelReason: 'server.disabled',
                };
                self.trigger('ad.cancel', slot, data);
                return self;
            }

            var containerWidth = slot.getParam('maxwidth') || slot.getContainerWidth();
            log('[Google] slot getContainerWidth ', slot, containerWidth);

            var sizes = slot.getParam('sizes', []).sizes || [];

            var filteredSizes;
            if (slot.getConfig('enable-filter-sizes', server.getConfig('enable-filter-sizes', true))) {
                filteredSizes = sizes.filter((size) => {
                    if (containerWidth >= size[0] || size[0] === 'fluid') { return size; }
                });
            } else {
                filteredSizes = sizes;
            }
            log('[Google] filtered sizes ', slot, filteredSizes);

            if (filteredSizes.length === 0) {
                delete googleSlots[slot];
                slot.disable();
    log('[Google] slot.disabled', slot);
                var data = {
                    'slot': slot,
                    'cancelReason': 'slot.disabled',
                };
                self.trigger('ad.cancel', slot, data);
                return;
            }

            googletag.cmd.push(function() {
                var slotSizes = slot.getParam('sizes');
    log('[Google] - slotDomNodeInit', slot.domId, server.getAdUnitPath(slot), 'slotSizes', slotSizes, 'filteredSizes', filteredSizes, slot);
                var response = googletag.defineSlot(server.getAdUnitPath(slot), filteredSizes || [], slot.domId).addService(googletag.pubads()); 

                if (slotSizes.mappingSizes) {
                    var mapping = googletag.sizeMapping();
                    slotSizes.mappingSizes.forEach((size) => {
                        mapping = mapping.addSize(size[0], size[1]);
                    });
                    mapping = mapping.build();
    log('[Google] mapping', mapping);
                    response = response.defineSizeMapping(mapping);
                }

                googleSlots[slot] = response;
    log('[Google] - slotDomNodeInit response', slot, response);
            });
        };

        server._global.ad = function(slot, isRefresh) {
            slot = self._getAdSlot(slot);
            if (requestedGoogleSlots[slot] > +new Date()) { return self; }
            var data = {
                slot: slot,
                target: slot.getTarget(),
                deferred: false,
                isRefresh: !!isRefresh || !!slot.getParam('isRefresh'),
            };

            if (!slot.isEnabled()) {
                data.cancelReason = 'slot.disabled';
                self.trigger('ad.cancel', slot, data);
                return self;
            }
            if (!slot.getAdServer().isEnabled()) {
                data.cancelReason = 'server.disabled';
                self.trigger('ad.cancel', slot, data);
                return self;
            }
            var checkEnabled = slot.getConfig('checkEnabled', slot.getAdServer().getConfig('checkEnabled', self.getConfig('checkEnabled', true)));
            if (typeof checkEnabled === FUNCTION) {
                try {
                    checkEnabled = checkEnabled(slot, data);
                } catch (e) {
                    log.error('Error in function supplied for AdService.AdSlot('+slot.id+').setConfig(\'checkEnabled\', func)', e, arguments, data, this);
                }
            }
            if (!checkEnabled) {
                data.cancelReason = 'checked.disabled';
                self.trigger('ad.cancel', slot, data);
                return self;
            }

            self.trigger('ad.begin', slot, data);

            if ((data.deferred || slot.isDeferred()) && !slot.isIndependent) {
                self.trigger('ad.deferred', slot, data);
                return self;
            }

            if (typeof data.target !== UNDEFINED) {
                googletag.cmd.push(() => {
    log('[Google] - ad', slot, slot.getDomNode().id);

                    prepareSlot(slot, isRefresh);

                    if (data.isRefresh) {
                        var slotsToRefresh = [slot],
                            setData = function(sets) {
                                for (timer in sets) {
                                    if (sets[timer].includes(slot.id)) { return {set: sets[timer], timer: timer}; }
                                }
                                return {set: [], timer: 0};
                            }(self.getConfig('intervalSets', {})),
                            intervalSlotSet = setData.set,
                            timer = Math.max(setData.timer-3, 1);
                        intervalSlotSet.forEach((intervalSlot) => {
                            intervalSlot = self.AdSlot(intervalSlot);
                            if (!slotsToRefresh.includes(intervalSlot) && intervalSlot.isEnabled() && intervalSlot.visibilityCheck() && !(requestedGoogleSlots[intervalSlot] > +new Date())) {
                                slotsToRefresh.push(intervalSlot);
                                prepareSlot(intervalSlot, true);
                            }
                        });
                        googletag.enableServices();
                        log('[Google] ad refresh', slotsToRefresh.length === 1 ? slot : slotsToRefresh, data.isRefresh);
                        googletag.pubads().refresh(server.getGoogleSlots(slotsToRefresh));
                        slotsToRefresh.forEach((slot) => {
                            cleanUpSlot(slot);
                            requestedGoogleSlots[slot] = +new Date() + timer*1000;
                        });
                        return;
                    }
                    requestedGoogleSlots[slot] = +new Date() + 1000; 
                    googletag.enableServices();

                    if ((!singleRequestEnabled && !data.isRefresh) || self.getExclusionRuleSet()._prio === slot.id) {
                        log('[Google] normal display', slot);
                        googletag.display(slot.getDomNode().id);
                        cleanUpSlot(slot);
                    }

                    var singleRequestReady = (singleRequestEnabled) ? isSingleRequestReady(server.getConfig('singleRequestSlots'), Object.keys(requestedGoogleSlots)) : false;

                    if (singleRequestEnabled && singleRequestReady && !data.isRefresh) {
                        log('[Google] singleRequest display', Object.keys(googleSlots));
                        each(googleSlots, (slot) => {
                            if (!self._getAdSlot(slot).isEnabled) { return; }
                            googletag.display(self._getAdSlot(slot).getDomNode().id);
                            cleanUpSlot(slot);
                        });
                    }
                });
            } else {
                log.remind('[Google] - ad', 'no target, ' + (window.document.readyState==='loading' ? 'not yet ' : '') + 'document.ready', slot, data);
            }
        };

        function isSingleRequestReady(singleRequestSlots, googleSlots) {
            each(googleSlots, (i, googleSlot) => {
                const index = singleRequestSlots.indexOf(googleSlot);
                if (index > -1) {
                    singleRequestSlots.splice(index, 1);
                }
            });
            return singleRequestSlots.length === 0;
        }

        function cleanUpSlot(slot) {
            if (!slot) { return; }
            slot = self._getAdSlot(slot);
            slot.clearOnetimeParams();
            googleSlots[slot].clearTargeting();
            self.removeEmptyAuctionResult(slot);
        }

        function prepareSlot(slot, isRefresh) {
            if (!slot) { return; }
            slot = self._getAdSlot(slot);
            if (isRefresh && server.getConfig('enable-refresh-tagid', true)) {
                slot.setParam('tagid', slot.id + '_x');
            }
            if (isRefresh && server.getConfig('enable-refresh-tagid', false)) {
                slot.empty();
                googletag.destroySlots([googleSlots[slot]]);
                self._runOutsourceable('slotDomNodeInit', self, [slot], self.getDefaultAdServer());
            }

            const secure = self.AdSlot(slot).getConfig('secure');
            if (typeof secure !== UNDEFINED) {
    log('[Google] - ad setForceSafeFrame', slot, secure);
                googleSlots[slot].setForceSafeFrame(secure);
            }
            const safeFrameConfig = self.AdSlot(slot).getConfig('safeFrameConfig');
            if (typeof safeFrameConfig !== UNDEFINED) {
    log('[Google] - ad setSafeFrameConfig', slot, safeFrameConfig);
                googleSlots[slot].setSafeFrameConfig(safeFrameConfig);
            }

            serverParams = server.getParams();
            each(serverParams, (key, value) => {
                if (globalParamsBlacklist.includes(key)) { return; }
                if (typeof value === UNDEFINED) { return; }
                googletag.pubads().setTargeting(key, ''+value);
            });

            slotParams = server.getParamsForSlot(slot);
            for (const key in serverParams) {
                delete slotParams[key];
            }

            each(slotParams, (key, value) => {
                if (slotParamsBlacklist.includes(key)) { return; }
                if (typeof value === UNDEFINED) { return; }
                googleSlots[slot].setTargeting(key, ''+value);
            });
            googleSlots[slot].setTargeting('tagid', slot.getParam('tagid') || slot.id);
        }

        server._global.refresh = function() {
            const slots = arguments.length ? (arguments[0] instanceof Array ? arguments[0] : [arguments[0]]) : Object.keys(googleSlots);

            each(slots, function(i, slot) {
                slot = self.AdSlot(slot);
                if (!slot.visibilityCheck()) {
                    log('[Google] - refresh', 'slot', slot, 'notVisible');
                    return;
                }
                slot.setOnetimeParam('isRefresh', true);
                self.ad(slot, true);
            });
        };

        server._global.auctionResultHtml = function(data) {
            log('[Google] - auctionResultHtml');
            var iframeSrc = 'about:blank',
                width = data.size.width,
                height = data.size.height,
                iframeAttrs = {width: width, height: height, frameborder: 0, border: 0, scrolling: 'no', marginwidth: 0, marginheight: 0, sandbox: 'allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts'};

            var iframe = self.createElement('iframe', iframeAttrs, {src: iframeSrc});
            document.body.appendChild(iframe);

            var iframeWindow = iframe.contentWindow,
            iframeDocument = iframeWindow.document;
            iframeDocument.open();

            iframeDocument.write(data.html);

            self.trigger('ad.finished', self.AdSlot(self._getCurrentAdSlot()), data);
            return true;
        };

        if (typeof googletag !== UNDEFINED && typeof googletag.cmd !== UNDEFINED) {
            googletag.cmd.push(() => {
                googletag.pubads().addEventListener('slotRenderEnded', function(e) {
                    log('[Google] - slotRenderEnded', e);
                    setGoogleDebug();
                    var data = {
                        'origin': {
                            'server': server.id,
                            'action': 'slotRenderEnded',
                        },
                    };
                    try {
                        server.renderMeta(document.getElementById(e.slot.getSlotElementId()), e); 
                        if (!e.isEmpty && (e.size[0] === 0 && e.size[1] === 0)) {
                            var size = server.getAdSize(document.getElementById(e.slot.getSlotElementId()));
                            if (size.width) {
                                document.getElementById(e.slot.getSlotElementId()).style.width = size.width;
                            }
                        }
                    } catch (err) {
                        log.error('Error in slotRenderEnded', err, 'arguments', e, data);
                    }
                    self._ad_finished(self._getAdSlot(document.getElementById(e.slot.getSlotElementId())), data);
                });
            });
        }

        self.on('ad.cancel', function(data) {
            if (self.getDefaultAdServer().id !== 'google' || !server.getConfig('singleRequest')) { return; }
            var slots = server.getConfig('singleRequestSlots');
            if (slots.indexOf(data.slot.id) < 0) { return; }
            slots.splice(slots.indexOf(data.slot.id), 1);
        });

        server.activate();

        window.addEventListener('message', function(evt) {
            if (!/^\{"message":"Prebid Native","adId":"[0-9a-f]{10,20}"(,"action":"\w+")?\}$/.test(evt.data)) { return; }
            self.sendNativeTrackingInfo(evt.data);
        });
        server._global.initiateNativeTracking = function() {
            return;
        };
    }).apply(self, [AdService]);
}

    AdService._initialize();

})();
