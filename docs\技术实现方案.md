# Go-Mail 临时邮箱服务平台 - 技术实现方案

## 🏗️ 整体架构设计

### 现有架构分析
```
go-mail-backend/
├── internal/
│   ├── types/          # ✅ 数据类型定义（完善）
│   ├── interfaces/     # ✅ 接口定义（完善）
│   ├── errors/         # ✅ 错误处理体系（完善）
│   ├── client/         # ✅ Mail.com客户端（完善）
│   ├── pool/           # ✅ 资源池管理（完善）
│   ├── manager/        # ✅ 主管理器（完善）
│   └── database/       # ✅ 数据库操作（完善）
├── cmd/                # ✅ 测试程序（完善）
└── scripts/            # ✅ 构建脚本（完善）
```

### 新增架构设计
```
go-mail-backend/
├── internal/
│   ├── api/            # 🆕 HTTP API层
│   │   ├── handlers/   # API处理器
│   │   ├── middleware/ # 中间件（认证、加密、CORS）
│   │   └── routes/     # 路由定义
│   ├── auth/           # 🆕 认证授权
│   │   ├── jwt/        # JWT令牌管理
│   │   └── crypto/     # AES加密解密
│   ├── services/       # 🆕 业务服务层
│   │   ├── activation/ # 激活码服务
│   │   ├── mailbox/    # 临时邮箱服务
│   │   └── monitor/    # 监控服务
│   └── scheduler/      # 🆕 定时任务
├── cmd/
│   └── server/         # 🆕 HTTP服务器启动程序
└── web/                # 🆕 静态文件目录
```

## 🔧 后端技术实现

### 1. HTTP服务器集成

#### Gin框架集成
```go
// cmd/server/main.go
package main

import (
    "go-mail/internal/api"
    "go-mail/internal/manager"
    "go-mail/internal/types"
)

func main() {
    // 创建现有的邮件管理器
    config := types.DefaultManagerConfig()
    mailManager := manager.NewMailManager(config)
    
    // 创建HTTP服务器
    server := api.NewServer(mailManager)
    server.Start(":8080")
}
```

#### 路由设计
```
API路由结构：
├── /api/v1/
│   ├── /auth/                   # 管理后台认证
│   │   ├── POST /login          # 管理员登录
│   │   └── POST /refresh        # 刷新令牌
│   ├── /activation/             # 激活码管理（管理后台）
│   │   ├── POST /generate       # 批量生成激活码
│   │   ├── POST /verify         # 验证激活码
│   │   └── GET  /list           # 激活码列表
│   ├── /client/                 # 客户端接口
│   │   ├── /activation/
│   │   │   └── POST /verify     # 客户端激活码验证
│   │   └── /mailbox/
│   │       ├── POST /allocate   # 分配临时邮箱
│   │       ├── POST /query      # 查询邮件
│   │       ├── POST /mail/detail # 获取邮件详情
│   │       ├── POST /release    # 释放邮箱
│   │       └── POST /status     # 邮箱状态
│   └── /monitor/                # 系统监控（管理后台）
│       ├── GET  /accounts       # 账户状态
│       ├── GET  /sessions       # 会话状态
│       └── GET  /statistics     # 系统统计
```

### 2. 数据库扩展设计

#### 新增数据表
```sql
-- 激活码表
CREATE TABLE activation_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,              -- 激活码
    device_fingerprint TEXT,                -- 设备指纹
    status TEXT DEFAULT 'unused',           -- unused/used/expired
    expires_at DATETIME,                    -- 过期时间
    used_at DATETIME,                       -- 使用时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 临时邮箱分配表
CREATE TABLE temp_mailboxes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mailbox_address TEXT NOT NULL,          -- 临时邮箱地址
    account_email TEXT NOT NULL,            -- 关联的Mail.com账户
    device_fingerprint TEXT,                -- 使用设备
    status TEXT DEFAULT 'active',           -- active/released/expired
    allocated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_check_at DATETIME,                 -- 最后检查时间
    expires_at DATETIME,                    -- 过期时间
    FOREIGN KEY (account_email) REFERENCES accounts(email)
);

-- 邮件记录表
CREATE TABLE mail_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mailbox_id INTEGER NOT NULL,
    mail_id TEXT NOT NULL,                  -- 邮件ID
    subject TEXT,                           -- 邮件主题
    sender TEXT,                            -- 发件人
    received_at DATETIME,                   -- 接收时间
    content_fetched BOOLEAN DEFAULT FALSE,  -- 是否已获取内容
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (mailbox_id) REFERENCES temp_mailboxes(id)
);
```

### 3. 安全机制实现

#### AES加密中间件
```go
// internal/auth/crypto/aes.go
type AESCrypto struct {
    key []byte
}

func (a *AESCrypto) Encrypt(data []byte) ([]byte, error) {
    // AES-256-GCM加密实现
}

func (a *AESCrypto) Decrypt(data []byte) ([]byte, error) {
    // AES-256-GCM解密实现
}

// internal/api/middleware/crypto.go
func CryptoMiddleware(crypto *AESCrypto) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 请求解密和响应加密逻辑
    }
}
```

#### 客户端认证中间件
```go
// internal/api/middleware/client_auth.go
func ClientAuthMiddleware(crypto *AESCrypto, db *database.Database) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 验证激活码
        authHeader := c.GetHeader("Authorization")
        if !strings.HasPrefix(authHeader, "Activation ") {
            c.JSON(401, gin.H{"code": 401, "message": "无效的认证方式"})
            c.Abort()
            return
        }

        activationCode := strings.TrimPrefix(authHeader, "Activation ")

        // 2. 解密设备指纹
        encryptedFingerprint := c.GetHeader("X-Device-Fingerprint")
        encryptedMac := c.GetHeader("X-Mac-Address")

        deviceInfo, err := crypto.Decrypt([]byte(encryptedFingerprint))
        if err != nil {
            c.JSON(400, gin.H{"code": 4001, "message": "设备信息解密失败"})
            c.Abort()
            return
        }

        // 3. 验证激活码和设备绑定
        if err := validateActivationCode(db, activationCode, deviceInfo, encryptedMac); err != nil {
            c.JSON(401, gin.H{"code": 1001, "message": "激活码验证失败"})
            c.Abort()
            return
        }

        // 4. 将验证信息存储到上下文
        c.Set("activation_code", activationCode)
        c.Set("device_info", deviceInfo)
        c.Next()
    }
}
```

#### 设备指纹生成
```go
// internal/auth/device.go
type DeviceFingerprint struct {
    SystemUUID   string `json:"system_uuid"`
    Username     string `json:"username"`
    ComputerName string `json:"computer_name"`
    Platform     string `json:"platform"`
    MacAddress   string `json:"mac_address"`
}

func GenerateFingerprint(data DeviceFingerprint) string {
    // 生成设备指纹哈希
    hash := sha256.Sum256([]byte(fmt.Sprintf("%s:%s:%s:%s:%s",
        data.SystemUUID, data.Username, data.ComputerName,
        data.Platform, data.MacAddress)))
    return hex.EncodeToString(hash[:])
}

func ValidateDeviceBinding(db *database.Database, activationCode string, deviceInfo DeviceFingerprint) error {
    // 验证激活码是否绑定到该设备
    // 1. 查询激活码记录
    // 2. 验证设备指纹匹配
    // 3. 验证MAC地址匹配
    // 4. 检查激活码是否过期
    return nil
}
```

### 4. 业务服务层设计

#### 激活码服务
```go
// internal/services/activation/service.go
type Service struct {
    db *database.Database
}

func (s *Service) GenerateCodes(count int, expiryDays int) ([]string, error) {
    // 批量生成激活码
}

func (s *Service) VerifyCode(code string, deviceFingerprint string) error {
    // 验证激活码并绑定设备
}
```

#### 临时邮箱服务
```go
// internal/services/mailbox/service.go
type Service struct {
    mailManager *manager.MailManager
    db          *database.Database
}

func (s *Service) AllocateMailbox(deviceFingerprint string) (*TempMailbox, error) {
    // 分配临时邮箱逻辑
    // 1. 选择可用的Mail.com账户
    // 2. 生成随机别名
    // 3. 创建别名邮箱
    // 4. 记录分配信息
}

func (s *Service) CheckMails(mailboxID int) ([]Mail, error) {
    // 检查新邮件
}

func (s *Service) ReleaseMailbox(mailboxID int) error {
    // 释放邮箱（删除别名）
}
```

### 5. 定时任务系统

#### 任务调度器
```go
// internal/scheduler/scheduler.go
type Scheduler struct {
    mailboxService *mailbox.Service
    ticker         *time.Ticker
}

func (s *Scheduler) Start() {
    // 启动定时任务
    go s.cleanupExpiredMailboxes()
    go s.healthCheckAccounts()
}

func (s *Scheduler) cleanupExpiredMailboxes() {
    // 每分钟检查并清理过期邮箱
    for range s.ticker.C {
        s.mailboxService.CleanupExpired()
    }
}
```

## 🖥️ 前端技术实现

### 1. 项目结构设计
```
mail-frontend/
├── src/
│   ├── components/     # 通用组件
│   │   ├── Layout/     # 布局组件
│   │   ├── Forms/      # 表单组件
│   │   └── Charts/     # 图表组件
│   ├── views/          # 页面组件
│   │   ├── Login/      # 登录页面
│   │   ├── Dashboard/  # 仪表板
│   │   ├── Activation/ # 激活码管理
│   │   └── Monitor/    # 系统监控
│   ├── stores/         # Pinia状态管理
│   │   ├── auth.ts     # 认证状态
│   │   ├── activation.ts # 激活码状态
│   │   └── monitor.ts  # 监控状态
│   ├── api/            # API接口
│   │   ├── auth.ts     # 认证API
│   │   ├── activation.ts # 激活码API
│   │   └── monitor.ts  # 监控API
│   ├── utils/          # 工具函数
│   │   ├── crypto.ts   # 加密解密
│   │   ├── request.ts  # HTTP请求
│   │   └── storage.ts  # 本地存储
│   └── types/          # TypeScript类型定义
├── public/             # 静态资源
└── dist/               # 构建输出
```

### 2. 核心功能实现

#### 认证系统
```typescript
// stores/auth.ts
export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: '',
    user: null,
    isAuthenticated: false
  }),
  
  actions: {
    async login(username: string, password: string) {
      const response = await authAPI.login(username, password)
      this.token = response.token
      this.isAuthenticated = true
      localStorage.setItem('token', this.token)
    },
    
    logout() {
      this.token = ''
      this.isAuthenticated = false
      localStorage.removeItem('token')
    }
  }
})
```

#### 激活码管理
```typescript
// views/Activation/ActivationManage.vue
<template>
  <n-card title="激活码管理">
    <n-space vertical>
      <n-form @submit="generateCodes">
        <n-form-item label="生成数量">
          <n-input-number v-model:value="generateForm.count" />
        </n-form-item>
        <n-form-item label="有效期（天）">
          <n-input-number v-model:value="generateForm.expiryDays" />
        </n-form-item>
        <n-button type="primary" @click="generateCodes">
          批量生成
        </n-button>
      </n-form>
      
      <n-data-table :columns="columns" :data="activationCodes" />
    </n-space>
  </n-card>
</template>
```

## 🚀 部署方案

### 1. 后端部署
```bash
# 编译后端服务
go build -o bin/go-mail-server ./cmd/server

# 配置文件
cat > config.yaml << EOF
server:
  port: 8080
  host: "0.0.0.0"
database:
  path: "./data/go-mail.db"
auth:
  jwt_secret: "your-secret-key"
  aes_key: "your-aes-key-32-bytes-long"
mail:
  accounts_file: "./data/accounts.json"
EOF

# 启动服务
./bin/go-mail-server -config=config.yaml
```

### 2. 前端部署
```bash
# 构建前端
cd mail-frontend
npm run build

# 部署到后端静态目录
cp -r dist/* ../go-mail-backend/web/
```

### 3. 一体化部署
- 后端服务器同时提供API和静态文件服务
- 单一可执行文件部署
- SQLite数据库文件存储
- 配置文件外部化

## 📊 开发时间估算

### 第一阶段：管理后台框架（2周）
- Day 1-3：Vue3项目搭建和基础配置
- Day 4-6：JWT认证系统和登录页面
- Day 7-10：主要页面框架和路由配置
- Day 11-14：UI组件开发和样式调整

### 第二阶段：后端API服务（2周）
- Day 1-3：Gin框架集成和基础路由
- Day 4-6：数据库扩展和模型定义
- Day 7-10：激活码和邮箱服务开发
- Day 11-14：安全中间件和API测试

### 第三阶段：功能完善（2周）
- Day 1-5：定时任务系统开发
- Day 6-10：前后端联调和功能测试
- Day 11-14：部署优化和文档完善

## 🔧 关键技术决策

### 1. Web框架选择：Gin
- **理由**：生态成熟、性能优秀、学习成本低
- **优势**：快速开发、中间件丰富、社区活跃

### 2. 加密方案：AES + HMAC
- **理由**：性能优秀、适合高频访问
- **实现**：AES-256-GCM模式、HMAC-SHA256签名

### 3. 设备绑定：系统信息组合
- **方案**：系统UUID + 用户名 + 计算机名
- **优势**：跨平台兼容、相对稳定、安全性好

### 4. 前端框架：Vue3 + Naive UI
- **理由**：现代化、TypeScript支持、组件丰富
- **优势**：开发效率高、用户体验好

## 📝 下一步行动

1. **确认技术方案**：审查本文档并确认技术选型
2. **环境准备**：安装开发工具和依赖
3. **开始开发**：按照产品驱动策略启动开发
4. **持续集成**：建立代码管理和部署流程

---

**文档版本**：v1.0  
**创建时间**：2025-01-20  
**技术负责人**：开发团队  
**审核状态**：待确认
