package auth

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Auth 认证服务
type Auth struct {
	jwtSecret []byte
	aesKey    []byte
	gcm       cipher.AEAD
}

// JWTClaims JWT声明结构
type JWTClaims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// NewAuth 创建认证服务
func NewAuth(jwtSecret, aesKey string) *Auth {
	// 确保AES密钥长度为32字节
	key := make([]byte, 32)
	copy(key, []byte(aesKey))

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(fmt.Sprintf("创建AES cipher失败: %v", err))
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		panic(fmt.Sprintf("创建GCM模式失败: %v", err))
	}

	return &Auth{
		jwtSecret: []byte(jwtSecret),
		aesKey:    key,
		gcm:       gcm,
	}
}

// GenerateJWT 生成JWT令牌
func (a *Auth) GenerateJWT(userID int, username, role string) (string, error) {
	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		Role:     role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "go-mail",
			Subject:   username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(a.jwtSecret)
}

// ValidateJWT 验证JWT令牌
func (a *Auth) ValidateJWT(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return a.jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("无效的令牌")
}

// GenerateRefreshToken 生成刷新令牌
func (a *Auth) GenerateRefreshToken(userID int, username string) (string, error) {
	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		Role:     "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(7 * 24 * time.Hour)), // 7天
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "go-mail",
			Subject:   username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(a.jwtSecret)
}

// EncryptAES AES加密
func (a *Auth) EncryptAES(data []byte) (string, error) {
	// 生成随机nonce
	nonce := make([]byte, a.gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", err
	}

	// 加密数据
	ciphertext := a.gcm.Seal(nonce, nonce, data, nil)

	// Base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptAES AES解密
func (a *Auth) DecryptAES(encryptedData string) ([]byte, error) {
	// Base64解码
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}

	// 检查数据长度
	nonceSize := a.gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("密文太短")
	}

	// 分离nonce和密文
	nonce, ciphertext := data[:nonceSize], data[nonceSize:]

	// 解密
	plaintext, err := a.gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// HashPassword 密码哈希
func (a *Auth) HashPassword(password string) string {
	hash := sha256.Sum256([]byte(password))
	return hex.EncodeToString(hash[:])
}

// VerifyPassword 验证密码
func (a *Auth) VerifyPassword(password, hashedPassword string) bool {
	return a.HashPassword(password) == hashedPassword
}

// GenerateDeviceFingerprint 生成设备指纹
func (a *Auth) GenerateDeviceFingerprint(systemUUID, username, computerName, platform, macAddress string) string {
	data := fmt.Sprintf("%s:%s:%s:%s:%s", systemUUID, username, computerName, platform, macAddress)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// GenerateActivationCode 生成激活码
func (a *Auth) GenerateActivationCode(prefix string) string {
	// 生成随机字节
	randomBytes := make([]byte, 8)
	rand.Read(randomBytes)

	// 转换为十六进制
	randomHex := hex.EncodeToString(randomBytes)

	// 格式化为激活码格式
	if prefix == "" {
		prefix = "GOMAIL"
	}

	// 格式：PREFIX-XXXX-XXXX-XXXX
	return fmt.Sprintf("%s-%s-%s-%s",
		prefix,
		randomHex[0:4],
		randomHex[4:8],
		randomHex[8:12])
}

// ValidateActivationCodeFormat 验证激活码格式
func (a *Auth) ValidateActivationCodeFormat(code string) bool {
	// 简单的格式验证
	// 应该是类似 GOMAIL-ABCD-1234-EFGH 的格式
	parts := len(code)
	return parts >= 16 && parts <= 32
}

// TokenInfo 令牌信息结构
type TokenInfo struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
}

// GenerateTokenPair 生成令牌对
func (a *Auth) GenerateTokenPair(userID int, username, role string) (*TokenInfo, error) {
	// 生成访问令牌
	accessToken, err := a.GenerateJWT(userID, username, role)
	if err != nil {
		return nil, err
	}

	// 生成刷新令牌
	refreshToken, err := a.GenerateRefreshToken(userID, username)
	if err != nil {
		return nil, err
	}

	return &TokenInfo{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    24 * 60 * 60, // 24小时，单位秒
	}, nil
}

// RefreshAccessToken 刷新访问令牌
func (a *Auth) RefreshAccessToken(refreshToken string) (*TokenInfo, error) {
	// 验证刷新令牌
	claims, err := a.ValidateJWT(refreshToken)
	if err != nil {
		return nil, err
	}

	// 检查是否是刷新令牌
	if claims.Role != "refresh" {
		return nil, fmt.Errorf("无效的刷新令牌")
	}

	// 生成新的令牌对（这里需要从数据库获取用户角色）
	// 暂时使用默认角色
	return a.GenerateTokenPair(claims.UserID, claims.Username, "admin")
}

// EncryptPassword 加密邮箱密码
func (a *Auth) EncryptPassword(password string) (string, error) {
	if password == "" {
		return "", fmt.Errorf("密码不能为空")
	}

	// 添加时间戳和随机盐以增强安全性
	timestamp := time.Now().Unix()
	salt := make([]byte, 8)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("生成盐值失败: %w", err)
	}

	// 组合数据：时间戳|盐值|密码
	data := fmt.Sprintf("%d|%s|%s", timestamp, hex.EncodeToString(salt), password)

	return a.EncryptAES([]byte(data))
}

// DecryptPassword 解密邮箱密码
func (a *Auth) DecryptPassword(encryptedPassword string) (string, error) {
	if encryptedPassword == "" {
		return "", fmt.Errorf("加密密码不能为空")
	}

	// 解密数据
	data, err := a.DecryptAES(encryptedPassword)
	if err != nil {
		return "", fmt.Errorf("解密失败: %w", err)
	}

	// 解析数据：时间戳|盐值|密码
	parts := string(data)
	segments := make([]string, 0, 3)
	current := ""
	pipeCount := 0

	for _, char := range parts {
		if char == '|' {
			segments = append(segments, current)
			current = ""
			pipeCount++
			if pipeCount >= 2 {
				// 找到两个分隔符后，剩余部分都是密码
				break
			}
		} else {
			current += string(char)
		}
	}

	// 添加剩余部分（密码）
	if current != "" {
		segments = append(segments, current)
	}

	if len(segments) != 3 {
		return "", fmt.Errorf("密码格式错误")
	}

	// 返回原始密码
	return segments[2], nil
}

// EncryptDeviceInfo 加密设备信息
func (a *Auth) EncryptDeviceInfo(systemUUID, username, computerName, platform, macAddress string) (string, error) {
	// 序列化为JSON
	data := fmt.Sprintf(`{"system_uuid":"%s","username":"%s","computer_name":"%s","platform":"%s","mac_address":"%s"}`,
		systemUUID, username, computerName, platform, macAddress)

	// 加密
	return a.EncryptAES([]byte(data))
}
