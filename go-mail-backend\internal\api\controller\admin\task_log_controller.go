package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// TaskLogController 任务日志管理控制器
type TaskLogController struct {
	adminAPI *AdminAPI
}

// NewTaskLogController 创建任务日志管理控制器
func NewTaskLogController(adminAPI *AdminAPI) *TaskLogController {
	return &TaskLogController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册任务日志管理路由
func (c *TaskLogController) RegisterRoutes(v1 *gin.RouterGroup) {
	taskLogGroup := v1.Group("/task-logs")
	taskLogGroup.Use(middleware.JWTAuth(c.adminAPI.auth))
	taskLogGroup.Use(middleware.SecurityHeaders())
	{
		taskLogHandler := handlers.NewTaskLogHandler(c.adminAPI.services.TaskLog)
		c.adminAPI.logger.Info("注册任务日志管理路由", "prefix", "/api/v1/task-logs")

		// 任务日志查询
		taskLogGroup.GET("", taskLogHandler.GetTaskLogs)                  // 获取任务日志列表
		taskLogGroup.GET("/:id/details", taskLogHandler.GetTaskLogDetail) // 获取任务日志详情

		// 任务日志管理
		taskLogGroup.POST("", taskLogHandler.CreateTaskLog)         // 创建任务日志
		taskLogGroup.PUT("/:task_id", taskLogHandler.UpdateTaskLog) // 更新任务日志

		c.adminAPI.logger.Info("任务日志管理路由注册完成", "routes", []string{
			"GET /api/v1/task-logs",
			"GET /api/v1/task-logs/:id/details",
			"POST /api/v1/task-logs",
			"PUT /api/v1/task-logs/:task_id",
		})
	}
}
