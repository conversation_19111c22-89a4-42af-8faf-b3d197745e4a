# Go-Mail 临时邮箱服务平台 - API接口设计

## 🌐 API 总体设计

### 基础信息
- **Base URL**: `http://localhost:8080/api/v1`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer <PERSON>
- **加密方式**: AES-256-GCM + HMAC-SHA256

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-01-20T10:30:00Z",
  "request_id": "uuid-string"
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "详细错误信息",
  "timestamp": "2025-01-20T10:30:00Z",
  "request_id": "uuid-string"
}
```

## 🔐 认证授权接口

### 1. 管理员登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_string",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "last_login": "2025-01-20T10:30:00Z"
    }
  }
}
```

### 2. 刷新令牌
```http
POST /api/v1/auth/refresh
Authorization: Bearer {refresh_token}
```

### 3. 登出
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```

## 🎫 激活码管理接口

### 1. 批量生成激活码
```http
POST /api/v1/activation/generate
Authorization: Bearer {token}
Content-Type: application/json

{
  "count": 100,
  "expiry_days": 30,
  "prefix": "GOMAIL",
  "description": "测试批次"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "激活码生成成功",
  "data": {
    "batch_id": "batch_20250120_001",
    "codes": [
      "GOMAIL-ABCD-1234-EFGH",
      "GOMAIL-WXYZ-5678-IJKL"
    ],
    "count": 100,
    "expires_at": "2025-02-20T10:30:00Z"
  }
}
```

### 2. 验证激活码（管理后台使用）
```http
POST /api/v1/activation/verify
Authorization: Bearer {token}
Content-Type: application/json

{
  "code": "GOMAIL-ABCD-1234-EFGH",
  "device_fingerprint": {
    "system_uuid": "uuid-string",
    "username": "user123",
    "computer_name": "PC-001",
    "platform": "windows",
    "mac_address": "00:11:22:33:44:55"
  }
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "激活码验证成功",
  "data": {
    "valid": true,
    "expires_at": "2025-02-20T10:30:00Z",
    "permissions": ["basic_mailbox"],
    "device_bound": true
  }
}
```

### 3. 客户端激活码验证（内部接口）
```http
POST /api/v1/client/activation/verify
Authorization: Activation GOMAIL-ABCD-1234-EFGH
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json

{}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "激活码有效",
  "data": {
    "valid": true,
    "expires_at": "2025-02-20T10:30:00Z",
    "permissions": ["basic_mailbox"],
    "device_matched": true
  }
}
```

### 4. 激活码列表查询
```http
GET /api/v1/activation/list?page=1&size=20&status=unused
Authorization: Bearer {token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 1000,
    "page": 1,
    "size": 20,
    "items": [
      {
        "id": 1,
        "code": "GOMAIL-ABCD-1234-EFGH",
        "status": "unused",
        "created_at": "2025-01-20T10:30:00Z",
        "expires_at": "2025-02-20T10:30:00Z",
        "used_at": null,
        "device_fingerprint": null,
        "mac_address": null
      }
    ]
  }
}
```

## 📧 客户端临时邮箱接口

### 客户端认证方式
所有客户端接口使用激活码认证，请求头格式：
```http
Authorization: Activation {activation_code}
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json
```

**设备信息加密内容：**
```json
{
  "system_uuid": "uuid-string",
  "username": "user123",
  "computer_name": "PC-001",
  "platform": "windows",
  "mac_address": "00:11:22:33:44:55"
}
```

### 1. 分配临时邮箱
```http
POST /api/v1/client/mailbox/allocate
Authorization: Activation GOMAIL-ABCD-1234-EFGH
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json

{
  "preferences": {
    "domain_suffix": "random",
    "auto_release_minutes": 3
  }
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "邮箱分配成功",
  "data": {
    "mailbox_id": 12345,
    "address": "<EMAIL>",
    "status": "active",
    "allocated_at": "2025-01-20T10:30:00Z",
    "expires_at": "2025-01-20T10:33:00Z",
    "auto_release": true
  }
}
```

### 2. 查询邮件
```http
POST /api/v1/client/mailbox/query
Authorization: Activation GOMAIL-ABCD-1234-EFGH
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json

{
  "mailbox_id": 12345,
  "since": "2025-01-20T10:30:00Z"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "mailbox_id": 12345,
    "address": "<EMAIL>",
    "mails": [
      {
        "id": "mail_001",
        "subject": "验证您的邮箱地址",
        "sender": "<EMAIL>",
        "received_at": "2025-01-20T10:31:00Z",
        "preview": "请点击以下链接验证您的邮箱...",
        "has_content": true
      }
    ],
    "total_count": 1,
    "last_check": "2025-01-20T10:32:00Z"
  }
}
```

### 3. 获取邮件详情
```http
POST /api/v1/client/mailbox/mail/detail
Authorization: Activation GOMAIL-ABCD-1234-EFGH
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json

{
  "mailbox_id": 12345,
  "mail_id": "mail_001"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "mail_001",
    "subject": "验证您的邮箱地址",
    "sender": "<EMAIL>",
    "received_at": "2025-01-20T10:31:00Z",
    "content": {
      "text": "纯文本内容",
      "html": "<html>HTML内容</html>"
    },
    "attachments": []
  }
}
```

### 4. 释放邮箱
```http
POST /api/v1/client/mailbox/release
Authorization: Activation GOMAIL-ABCD-1234-EFGH
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json

{
  "mailbox_id": 12345,
  "reason": "manual_release"
}
```

### 5. 邮箱状态查询
```http
POST /api/v1/client/mailbox/status
Authorization: Activation GOMAIL-ABCD-1234-EFGH
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json

{
  "mailbox_id": 12345
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "mailbox_id": 12345,
    "address": "<EMAIL>",
    "status": "active",
    "allocated_at": "2025-01-20T10:30:00Z",
    "expires_at": "2025-01-20T10:33:00Z",
    "last_check": "2025-01-20T10:32:00Z",
    "mail_count": 1
  }
}
```

## 📊 系统监控接口

### 1. 系统统计信息
```http
GET /api/v1/monitor/statistics
Authorization: Bearer {token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "accounts": {
      "total": 50,
      "active": 45,
      "inactive": 5
    },
    "mailboxes": {
      "total_allocated": 1000,
      "currently_active": 25,
      "today_allocated": 100
    },
    "activation_codes": {
      "total_generated": 5000,
      "used": 1200,
      "unused": 3800
    },
    "system": {
      "uptime": "72h30m",
      "memory_usage": "256MB",
      "cpu_usage": "15%"
    }
  }
}
```

### 2. Mail.com账户状态
```http
GET /api/v1/monitor/accounts
Authorization: Bearer {token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "accounts": [
      {
        "email": "<EMAIL>",
        "status": "active",
        "last_login": "2025-01-20T10:30:00Z",
        "session_valid": true,
        "aliases_count": 5,
        "max_aliases": 10
      }
    ],
    "summary": {
      "total": 50,
      "active": 45,
      "error": 2,
      "maintenance": 3
    }
  }
}
```

### 3. 会话状态监控
```http
GET /api/v1/monitor/sessions
Authorization: Bearer {token}
```

### 4. 系统日志查询
```http
GET /api/v1/monitor/logs?level=error&start_time=2025-01-20T00:00:00Z&end_time=2025-01-20T23:59:59Z
Authorization: Bearer {token}
```

## 🔧 系统配置接口

### 1. 获取系统配置
```http
GET /api/v1/config
Authorization: Bearer {token}
```

### 2. 更新系统配置
```http
PUT /api/v1/config
Authorization: Bearer {token}
Content-Type: application/json

{
  "mailbox_auto_release_minutes": 3,
  "max_concurrent_allocations": 100,
  "activation_code_expiry_days": 30
}
```

## 🔒 安全机制

### 请求加密
所有敏感数据请求都需要进行 AES 加密：

```http
POST /api/v1/mailbox/allocate
Authorization: Bearer {device_token}
Content-Type: application/json
X-Encrypted: true

{
  "encrypted_data": "base64_encoded_encrypted_json",
  "signature": "hmac_sha256_signature"
}
```

### 响应加密
敏感数据响应也会进行加密：

```json
{
  "code": 200,
  "message": "success",
  "encrypted_data": "base64_encoded_encrypted_json",
  "signature": "hmac_sha256_signature"
}
```

### 频率限制
- **管理员登录**: 5次/分钟
- **激活码验证**: 10次/分钟
- **客户端邮箱分配**: 20次/小时
- **客户端邮件查询**: 200次/小时
- **客户端激活码验证**: 100次/小时

## 📝 错误码定义

### 通用错误码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `429`: 请求过于频繁
- `500`: 服务器内部错误

### 业务错误码
- `1001`: 激活码无效
- `1002`: 激活码已使用
- `1003`: 激活码已过期
- `1004`: 设备指纹不匹配
- `1005`: MAC地址不匹配
- `1006`: 设备已绑定其他激活码
- `2001`: 邮箱分配失败
- `2002`: 邮箱不存在
- `2003`: 邮箱已释放
- `2004`: 邮件不存在
- `3001`: Mail.com账户不可用
- `3002`: 会话已过期
- `4001`: 设备信息解密失败
- `4002`: 请求头缺失必要信息

## 🧪 接口测试

### 测试环境
- **开发环境**: `http://localhost:8080`
- **测试环境**: `http://test.gomail.com`
- **生产环境**: `https://api.gomail.com`

### 测试工具
- **Postman**: 提供完整的接口测试集合
- **curl**: 命令行测试脚本
- **自动化测试**: Go 单元测试和集成测试

### 示例测试脚本
```bash
#!/bin/bash
# 登录获取token
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}' \
  | jq -r '.data.token')

# 生成激活码
curl -X POST http://localhost:8080/api/v1/activation/generate \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"count":10,"expiry_days":30}'
```

---

**文档版本**：v1.0  
**创建时间**：2025-01-20  
**API版本**：v1  
**维护人员**：后端开发团队
