package handlers

import (
	"go-mail/internal/database"
	"go-mail/internal/services/mailbox"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// ClientMailboxHandler 客户端邮箱处理器
type ClientMailboxHandler struct {
	mailboxService *mailbox.Service
}

// NewClientMailboxHandler 创建客户端邮箱处理器
func NewClientMailboxHandler(mailboxService *mailbox.Service) *ClientMailboxHandler {
	return &ClientMailboxHandler{
		mailboxService: mailboxService,
	}
}

// AllocateMailbox 分配临时邮箱
func (h *ClientMailboxHandler) AllocateMailbox(c *gin.Context) {
	// 从中间件获取认证信息
	activationCode := c.GetString("activation_code")
	deviceInfo, exists := c.Get("device_info")
	if !exists {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      4002,
			Message:   "设备信息缺失",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 类型断言
	deviceInfoStruct, ok := deviceInfo.(database.DeviceInfo)
	if !ok {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      4001,
			Message:   "设备信息格式错误",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 生成设备指纹
	deviceFingerprint := generateDeviceFingerprint(deviceInfoStruct)

	// 解析请求参数
	var req database.MailboxAllocationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 设置默认值
	if req.Preferences.AutoReleaseMinutes <= 0 {
		req.Preferences.AutoReleaseMinutes = 3
	}
	if req.Preferences.DomainSuffix == "" {
		req.Preferences.DomainSuffix = "random"
	}

	// 分配邮箱
	response, err := h.mailboxService.AllocateMailbox(c.Request.Context(), activationCode, deviceFingerprint, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      2001,
			Message:   "邮箱分配失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "邮箱分配成功",
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// QueryMails 查询邮件
func (h *ClientMailboxHandler) QueryMails(c *gin.Context) {
	var req database.MailQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证邮箱ID
	if req.MailboxID <= 0 {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的邮箱ID",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 查询邮件
	response, err := h.mailboxService.QueryMails(c.Request.Context(), req.MailboxID, req.Since)
	if err != nil {
		var code int
		var message string
		
		switch err.Error() {
		case "邮箱不存在或已释放":
			code = 2002
			message = "邮箱不存在或已释放"
		case "邮箱已过期":
			code = 2003
			message = "邮箱已过期"
		default:
			code = 500
			message = "查询邮件失败"
		}

		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      code,
			Message:   message,
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "查询成功",
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetMailDetail 获取邮件详情
func (h *ClientMailboxHandler) GetMailDetail(c *gin.Context) {
	var req database.MailDetailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证参数
	if req.MailboxID <= 0 || req.MailID == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "邮箱ID和邮件ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 获取邮件详情
	response, err := h.mailboxService.GetMailDetail(c.Request.Context(), req.MailboxID, req.MailID)
	if err != nil {
		var code int
		var message string
		
		switch err.Error() {
		case "邮箱不存在或已释放":
			code = 2002
			message = "邮箱不存在或已释放"
		case "获取邮件内容失败":
			code = 2004
			message = "邮件不存在"
		default:
			code = 500
			message = "获取邮件详情失败"
		}

		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      code,
			Message:   message,
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ReleaseMailbox 释放邮箱
func (h *ClientMailboxHandler) ReleaseMailbox(c *gin.Context) {
	var req database.MailboxReleaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证邮箱ID
	if req.MailboxID <= 0 {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的邮箱ID",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 设置默认释放原因
	if req.Reason == "" {
		req.Reason = database.ReleaseReasonManual
	}

	// 释放邮箱
	err := h.mailboxService.ReleaseMailbox(c.Request.Context(), req.MailboxID, req.Reason)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "释放邮箱失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "邮箱释放成功",
		Data: gin.H{
			"mailbox_id": req.MailboxID,
			"reason":     req.Reason,
			"released_at": time.Now().Format(time.RFC3339),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetMailboxStatus 获取邮箱状态
func (h *ClientMailboxHandler) GetMailboxStatus(c *gin.Context) {
	var req database.MailboxStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证邮箱ID
	if req.MailboxID <= 0 {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的邮箱ID",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 获取邮箱状态
	response, err := h.mailboxService.GetMailboxStatus(c.Request.Context(), req.MailboxID)
	if err != nil {
		c.JSON(http.StatusNotFound, database.APIResponse{
			Code:      2002,
			Message:   "邮箱不存在",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "查询成功",
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// 辅助函数

// generateDeviceFingerprint 生成设备指纹
func generateDeviceFingerprint(deviceInfo database.DeviceInfo) string {
	// 这里应该使用与auth包中相同的算法
	// 暂时返回简单的组合字符串
	return deviceInfo.SystemUUID + ":" + deviceInfo.Username + ":" + deviceInfo.ComputerName + ":" + deviceInfo.Platform + ":" + deviceInfo.MacAddress
}
