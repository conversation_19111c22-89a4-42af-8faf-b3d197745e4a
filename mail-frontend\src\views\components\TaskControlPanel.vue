<template>
  <n-drawer v-model:show="visible" :width="800" placement="right">
    <n-drawer-content title="定时任务控制面板">
      <div class="task-control-panel">
        <!-- 任务状态概览 -->
        <n-card title="任务状态概览" class="mb-4">
          <n-grid :cols="4" :x-gap="12">
            <n-grid-item>
              <n-statistic label="运行中" :value="taskSummary.running" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="已停止" :value="taskSummary.stopped" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="已暂停" :value="taskSummary.paused" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="错误" :value="taskSummary.error" />
            </n-grid-item>
          </n-grid>
        </n-card>

        <!-- 任务列表 -->
        <n-card title="任务列表">
          <template #header-extra>
            <n-space>
              <n-button @click="refreshTasks" :loading="loading">
                <template #icon>
                  <n-icon><RefreshOutline /></n-icon>
                </template>
                刷新
              </n-button>
              <n-button type="primary" @click="startAllTasks">
                <template #icon>
                  <n-icon><PlayOutline /></n-icon>
                </template>
                全部启动
              </n-button>
              <n-button @click="stopAllTasks">
                <template #icon>
                  <n-icon><StopOutline /></n-icon>
                </template>
                全部停止
              </n-button>
            </n-space>
          </template>

          <n-data-table
            :columns="columns"
            :data="tasks"
            :loading="loading"
            :pagination="false"
            :row-key="(row: TaskSchedulerStatus) => row.id"
          />
        </n-card>

        <!-- 批量操作历史 -->
        <n-card title="批量操作历史" class="mt-4">
          <template #header-extra>
            <n-button @click="refreshOperations" :loading="loadingOperations">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              刷新
            </n-button>
          </template>

          <n-data-table
            :columns="operationColumns"
            :data="operations"
            :loading="loadingOperations"
            :pagination="operationPagination"
            :row-key="(row: BatchOperation) => row.id"
          />
        </n-card>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import {
  NDrawer, NDrawerContent, NCard, NGrid, NGridItem, NStatistic,
  NSpace, NButton, NIcon, NDataTable, NTag, NProgress, useMessage, useDialog
} from 'naive-ui'
import { RefreshOutline, PlayOutline, StopOutline, PauseOutline } from '@vicons/ionicons5'
import { mailboxApi, type TaskSchedulerStatus, type BatchOperation } from '@/api/mailbox'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const loadingOperations = ref(false)
const tasks = ref<TaskSchedulerStatus[]>([])
const operations = ref<BatchOperation[]>([])

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const taskSummary = computed(() => {
  const summary = { running: 0, stopped: 0, paused: 0, error: 0 }
  tasks.value.forEach(task => {
    switch (task.status) {
      case 'running':
        summary.running++
        break
      case 'stopped':
        summary.stopped++
        break
      case 'paused':
        summary.paused++
        break
      case 'error':
        summary.error++
        break
    }
  })
  return summary
})

// 分页配置
const operationPagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 任务表格列定义
const columns = computed(() => [
  {
    title: '任务名称',
    key: 'task_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '任务类型',
    key: 'task_type',
    width: 120,
    render: (row: TaskSchedulerStatus) => {
      const typeMap: Record<string, string> = {
        batch_verification: '批量验证',
        health_check: '健康检查',
        cleanup: '清理任务'
      }
      return typeMap[row.task_type] || row.task_type
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: TaskSchedulerStatus) => {
      const statusMap: Record<string, { text: string; type: 'success' | 'error' | 'warning' | 'info' }> = {
        running: { text: '运行中', type: 'success' },
        stopped: { text: '已停止', type: 'info' },
        paused: { text: '已暂停', type: 'warning' },
        error: { text: '错误', type: 'error' }
      }
      const status = statusMap[row.status] || { text: row.status, type: 'info' }
      return h(NTag, { type: status.type }, { default: () => status.text })
    }
  },
  {
    title: '运行次数',
    key: 'run_count',
    width: 100
  },
  {
    title: '错误次数',
    key: 'error_count',
    width: 100,
    render: (row: TaskSchedulerStatus) => {
      return row.error_count > 0 
        ? h(NTag, { type: 'error' }, { default: () => row.error_count })
        : row.error_count
    }
  },
  {
    title: '最后运行',
    key: 'last_run_at',
    width: 150,
    render: (row: TaskSchedulerStatus) => {
      return row.last_run_at 
        ? new Date(row.last_run_at).toLocaleString()
        : '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row: TaskSchedulerStatus) => {
      return h(NSpace, [
        h(NButton, {
          size: 'small',
          type: row.status === 'running' ? 'warning' : 'primary',
          onClick: () => toggleTask(row)
        }, { 
          default: () => row.status === 'running' ? '暂停' : '启动',
          icon: () => h(NIcon, null, { 
            default: () => row.status === 'running' ? h(PauseOutline) : h(PlayOutline) 
          })
        }),
        h(NButton, {
          size: 'small',
          type: 'error',
          onClick: () => stopTask(row),
          disabled: row.status === 'stopped'
        }, { 
          default: () => '停止',
          icon: () => h(NIcon, null, { default: () => h(StopOutline) })
        }),
        h(NButton, {
          size: 'small',
          onClick: () => resetTask(row)
        }, { default: () => '重置' })
      ])
    }
  }
])

// 批量操作表格列定义
const operationColumns = computed(() => [
  {
    title: '操作ID',
    key: 'operation_id',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '操作类型',
    key: 'operation_type',
    width: 100,
    render: (row: BatchOperation) => {
      const typeMap: Record<string, string> = {
        import: '批量导入',
        verify: '批量验证',
        disable: '批量禁用',
        enable: '批量启用'
      }
      return typeMap[row.operation_type] || row.operation_type
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: BatchOperation) => {
      const statusMap: Record<string, { text: string; type: 'success' | 'error' | 'warning' | 'info' }> = {
        pending: { text: '等待中', type: 'info' },
        running: { text: '运行中', type: 'warning' },
        completed: { text: '已完成', type: 'success' },
        failed: { text: '失败', type: 'error' },
        cancelled: { text: '已取消', type: 'info' }
      }
      const status = statusMap[row.status] || { text: row.status, type: 'info' }
      return h(NTag, { type: status.type }, { default: () => status.text })
    }
  },
  {
    title: '进度',
    key: 'progress',
    width: 150,
    render: (row: BatchOperation) => {
      const percentage = row.total_count > 0 
        ? Math.round((row.processed_count / row.total_count) * 100)
        : 0
      
      return h('div', [
        h(NProgress, {
          type: 'line',
          percentage,
          showIndicator: false,
          height: 8
        }),
        h('div', { class: 'text-xs text-gray-500 mt-1' }, 
          `${row.processed_count}/${row.total_count}`
        )
      ])
    }
  },
  {
    title: '成功/失败',
    key: 'result',
    width: 100,
    render: (row: BatchOperation) => {
      return h('div', { class: 'text-xs' }, [
        h('div', { class: 'text-green-600' }, `成功: ${row.success_count}`),
        h('div', { class: 'text-red-600' }, `失败: ${row.failed_count}`)
      ])
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    render: (row: BatchOperation) => {
      return new Date(row.created_at).toLocaleString()
    }
  }
])

// 方法
const refreshTasks = async () => {
  loading.value = true
  try {
    const response = await mailboxApi.getTaskSchedulerStatus()
    if (response.success && response.data) {
      tasks.value = response.data
    }
  } catch (error) {
    message.error('加载任务状态失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const refreshOperations = async () => {
  loadingOperations.value = true
  try {
    // TODO: 实现批量操作历史查询API
    // const response = await mailboxApi.getBatchOperations()
    // if (response.success && response.data) {
    //   operations.value = response.data.items
    //   operationPagination.itemCount = response.data.total
    // }
  } catch (error) {
    message.error('加载操作历史失败')
    console.error(error)
  } finally {
    loadingOperations.value = false
  }
}

const toggleTask = async (task: TaskSchedulerStatus) => {
  try {
    const action = task.status === 'running' ? 'pause' : 'start'
    await mailboxApi.controlTask({
      action,
      task_id: task.task_name
    })
    
    message.success(`任务${action === 'start' ? '启动' : '暂停'}成功`)
    refreshTasks()
    emit('refresh')
  } catch (error: any) {
    message.error(error.message || '操作失败')
  }
}

const stopTask = async (task: TaskSchedulerStatus) => {
  try {
    await mailboxApi.controlTask({
      action: 'stop',
      task_id: task.task_name
    })
    
    message.success('任务停止成功')
    refreshTasks()
    emit('refresh')
  } catch (error: any) {
    message.error(error.message || '停止任务失败')
  }
}

const resetTask = async (task: TaskSchedulerStatus) => {
  dialog.warning({
    title: '确认重置',
    content: `确定要重置任务 "${task.task_name}" 吗？这将清除运行计数和错误记录。`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await mailboxApi.controlTask({
          action: 'reset',
          task_id: task.task_name
        })
        
        message.success('任务重置成功')
        refreshTasks()
        emit('refresh')
      } catch (error: any) {
        message.error(error.message || '重置任务失败')
      }
    }
  })
}

const startAllTasks = async () => {
  dialog.info({
    title: '确认启动',
    content: '确定要启动所有已停止的任务吗？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      const stoppedTasks = tasks.value.filter(task => task.status === 'stopped')
      
      for (const task of stoppedTasks) {
        try {
          await mailboxApi.controlTask({
            action: 'start',
            task_id: task.task_name
          })
        } catch (error) {
          console.error(`启动任务 ${task.task_name} 失败:`, error)
        }
      }
      
      message.success('批量启动完成')
      refreshTasks()
      emit('refresh')
    }
  })
}

const stopAllTasks = async () => {
  dialog.warning({
    title: '确认停止',
    content: '确定要停止所有运行中的任务吗？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      const runningTasks = tasks.value.filter(task => task.status === 'running')
      
      for (const task of runningTasks) {
        try {
          await mailboxApi.controlTask({
            action: 'stop',
            task_id: task.task_name
          })
        } catch (error) {
          console.error(`停止任务 ${task.task_name} 失败:`, error)
        }
      }
      
      message.success('批量停止完成')
      refreshTasks()
      emit('refresh')
    }
  })
}

// 生命周期
onMounted(() => {
  refreshTasks()
  refreshOperations()
})
</script>

<style scoped>
.task-control-panel {
  padding: 16px;
}
</style>
