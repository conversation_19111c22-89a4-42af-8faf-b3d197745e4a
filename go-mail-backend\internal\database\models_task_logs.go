package database

import (
	"time"
)

// TaskLog 任务日志数据库记录结构
type TaskLog struct {
	ID            int        `db:"id" json:"id"`
	TaskID        string     `db:"task_id" json:"task_id"`                   // 任务唯一标识
	OperationType string     `db:"operation_type" json:"operation_type"`     // 操作类型：import/verify/login
	Email         string     `db:"email" json:"email"`                       // 邮箱地址
	Status        string     `db:"status" json:"status"`                     // 状态：success/failed/running/pending
	StartTime     time.Time  `db:"start_time" json:"start_time"`             // 开始时间
	EndTime       *time.Time `db:"end_time" json:"end_time"`                 // 结束时间
	DurationMs    *int       `db:"duration_ms" json:"duration_ms"`           // 耗时（毫秒）
	ErrorMessage  *string    `db:"error_message" json:"error_message"`       // 错误信息摘要
	BatchID       *string    `db:"batch_id" json:"batch_id"`                 // 批次ID
	DetailLogPath *string    `db:"detail_log_path" json:"detail_log_path"`   // 详细日志文件路径
	CreatedAt     time.Time  `db:"created_at" json:"created_at"`             // 创建时间
	UpdatedAt     time.Time  `db:"updated_at" json:"updated_at"`             // 更新时间
}

// TaskLogDetail 详细日志结构（用于JSON文件存储）
type TaskLogDetail struct {
	TaskID      string                 `json:"task_id"`
	Email       string                 `json:"email"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time"`
	Steps       []TaskLogStep          `json:"steps"`
	FinalStatus string                 `json:"final_status"`
	ErrorDetails *string               `json:"error_details"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// TaskLogStep 任务执行步骤
type TaskLogStep struct {
	Step         string                 `json:"step"`          // 步骤名称
	Timestamp    time.Time              `json:"timestamp"`     // 时间戳
	Status       string                 `json:"status"`        // 步骤状态：success/failed/running
	Details      string                 `json:"details"`       // 步骤详情
	Duration     *int                   `json:"duration"`      // 步骤耗时（毫秒）
	ErrorMessage *string                `json:"error_message"` // 错误信息
	ResponseData map[string]interface{} `json:"response_data"` // 响应数据
}

// TaskLogFilter 任务日志查询过滤器
type TaskLogFilter struct {
	StartTime     *time.Time `json:"start_time"`
	EndTime       *time.Time `json:"end_time"`
	OperationType *string    `json:"operation_type"`
	Status        *string    `json:"status"`
	Email         *string    `json:"email"`
	BatchID       *string    `json:"batch_id"`
	Page          int        `json:"page"`
	PageSize      int        `json:"page_size"`
}

// TaskLogResponse 任务日志响应结构
type TaskLogResponse struct {
	Total int       `json:"total"`
	Page  int       `json:"page"`
	Size  int       `json:"size"`
	Items []TaskLog `json:"items"`
}

// TaskLogDetailResponse 详细日志响应结构
type TaskLogDetailResponse struct {
	TaskLog TaskLog       `json:"task_log"`
	Detail  TaskLogDetail `json:"detail"`
}

// CreateTaskLogRequest 创建任务日志请求
type CreateTaskLogRequest struct {
	TaskID        string    `json:"task_id" binding:"required"`
	OperationType string    `json:"operation_type" binding:"required"`
	Email         string    `json:"email" binding:"required"`
	Status        string    `json:"status" binding:"required"`
	StartTime     time.Time `json:"start_time" binding:"required"`
	BatchID       *string   `json:"batch_id"`
}

// UpdateTaskLogRequest 更新任务日志请求
type UpdateTaskLogRequest struct {
	Status        *string    `json:"status"`
	EndTime       *time.Time `json:"end_time"`
	DurationMs    *int       `json:"duration_ms"`
	ErrorMessage  *string    `json:"error_message"`
	DetailLogPath *string    `json:"detail_log_path"`
}
