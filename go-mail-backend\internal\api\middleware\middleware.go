package middleware

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/logger"
	"net/http"
	"runtime/debug"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Device-Fingerprint, X-Mac-Address, X-Request-ID")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

// JWTAuth JWT认证中间件（管理后台使用）
func JWTAuth(authService *auth.Auth) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":       401,
				"message":    "缺少认证头",
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":       401,
				"message":    "无效的认证格式",
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 提取token
		token := strings.TrimPrefix(authHeader, "Bearer ")

		// 验证token
		claims, err := authService.ValidateJWT(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":       401,
				"message":    "无效的认证令牌",
				"error":      err.Error(),
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Next()
	}
}

// ClientAuth 客户端认证中间件（激活码认证）
func ClientAuth(authService *auth.Auth, db *database.Database) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 验证激活码
		authHeader := c.GetHeader("Authorization")
		if !strings.HasPrefix(authHeader, "Activation ") {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":       401,
				"message":    "无效的认证方式",
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		activationCode := strings.TrimPrefix(authHeader, "Activation ")
		if activationCode == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":       1001,
				"message":    "激活码不能为空",
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 2. 获取加密的设备信息
		encryptedFingerprint := c.GetHeader("X-Device-Fingerprint")
		encryptedMac := c.GetHeader("X-Mac-Address")

		if encryptedFingerprint == "" || encryptedMac == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":       4002,
				"message":    "缺少设备信息",
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 3. 解密设备指纹
		deviceInfoBytes, err := authService.DecryptAES(encryptedFingerprint)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":       4001,
				"message":    "设备信息解密失败",
				"error":      err.Error(),
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 4. 解析设备信息
		var deviceInfo database.DeviceInfo
		if err := json.Unmarshal(deviceInfoBytes, &deviceInfo); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":       4001,
				"message":    "设备信息格式错误",
				"error":      err.Error(),
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 5. 解密MAC地址
		macInfoBytes, err := authService.DecryptAES(encryptedMac)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":       4001,
				"message":    "MAC地址解密失败",
				"error":      err.Error(),
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		var macInfo database.DeviceInfo
		if err := json.Unmarshal(macInfoBytes, &macInfo); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":       4001,
				"message":    "MAC地址格式错误",
				"error":      err.Error(),
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 6. 验证激活码和设备绑定
		if err := validateActivationCode(db, activationCode, deviceInfo, macInfo.MacAddress); err != nil {
			var code int
			var message string

			switch err.Error() {
			case "activation_code_not_found":
				code = 1001
				message = "激活码不存在"
			case "activation_code_expired":
				code = 1003
				message = "激活码已过期"
			case "device_fingerprint_mismatch":
				code = 1004
				message = "设备指纹不匹配"
			case "mac_address_mismatch":
				code = 1005
				message = "MAC地址不匹配"
			case "device_binding_failed":
				code = 1006
				message = "设备绑定失败"
			default:
				code = 1001
				message = "激活码验证失败"
			}

			c.JSON(http.StatusUnauthorized, gin.H{
				"code":       code,
				"message":    message,
				"error":      err.Error(),
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
			c.Abort()
			return
		}

		// 7. 将验证信息存储到上下文
		c.Set("activation_code", activationCode)
		c.Set("device_info", deviceInfo)
		c.Set("mac_address", macInfo.MacAddress)
		c.Next()
	}
}

// validateActivationCode 验证激活码和设备绑定
func validateActivationCode(db *database.Database, activationCode string, deviceInfo database.DeviceInfo, macAddress string) error {
	// 1. 查询激活码记录
	var activationCodeRecord database.ActivationCode
	err := db.GetDB().QueryRow(`
		SELECT id, code, device_fingerprint, mac_address, status, expires_at
		FROM activation_codes WHERE code = ?`,
		activationCode).Scan(&activationCodeRecord.ID, &activationCodeRecord.Code,
		&activationCodeRecord.DeviceFingerprint, &activationCodeRecord.MacAddress,
		&activationCodeRecord.Status, &activationCodeRecord.ExpiresAt)

	if err != nil {
		return fmt.Errorf("activation_code_not_found")
	}

	// 2. 验证激活码是否过期
	if time.Now().After(activationCodeRecord.ExpiresAt) {
		// 更新状态为过期
		db.GetDB().Exec("UPDATE activation_codes SET status = ? WHERE id = ?",
			database.ActivationCodeStatusExpired, activationCodeRecord.ID)
		return fmt.Errorf("activation_code_expired")
	}

	// 3. 生成设备指纹进行比较
	expectedFingerprint := generateDeviceFingerprint(deviceInfo)

	// 4. 如果激活码未绑定设备，则绑定
	if stringPtrIsEmpty(activationCodeRecord.DeviceFingerprint) {
		_, err = db.GetDB().Exec(`
			UPDATE activation_codes
			SET device_fingerprint = ?, mac_address = ?, status = ?, used_at = CURRENT_TIMESTAMP
			WHERE id = ?`,
			expectedFingerprint, macAddress, database.ActivationCodeStatusUsed, activationCodeRecord.ID)
		if err != nil {
			return fmt.Errorf("device_binding_failed")
		}
	} else {
		// 5. 验证设备指纹是否匹配
		if !stringPtrEquals(activationCodeRecord.DeviceFingerprint, expectedFingerprint) {
			return fmt.Errorf("device_fingerprint_mismatch")
		}
		if !stringPtrEquals(activationCodeRecord.MacAddress, macAddress) {
			return fmt.Errorf("mac_address_mismatch")
		}
	}

	return nil
}

// generateDeviceFingerprint 生成设备指纹
func generateDeviceFingerprint(deviceInfo database.DeviceInfo) string {
	// 使用与auth包中相同的算法
	data := fmt.Sprintf("%s:%s:%s:%s:%s",
		deviceInfo.SystemUUID, deviceInfo.Username, deviceInfo.ComputerName,
		deviceInfo.Platform, deviceInfo.MacAddress)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// stringPtrIsEmpty 检查字符串指针是否为空或指向空字符串
func stringPtrIsEmpty(ptr *string) bool {
	return ptr == nil || *ptr == ""
}

// stringPtrEquals 比较字符串指针与字符串是否相等
func stringPtrEquals(ptr *string, str string) bool {
	if ptr == nil {
		return str == ""
	}
	return *ptr == str
}

// RateLimit 频率限制中间件
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现频率限制逻辑
		c.Next()
	}
}

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 处理错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":       500,
				"message":    "服务器内部错误",
				"error":      err.Error(),
				"timestamp":  time.Now().Format(time.RFC3339),
				"request_id": c.GetString("request_id"),
			})
		}
	}
}

// PanicRecovery Panic恢复中间件
func PanicRecovery() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 获取堆栈跟踪
				stack := debug.Stack()

				// 获取日志记录器
				log := logger.GetDefaultLogger().WithContext(c.Request.Context())

				// 记录详细的panic信息
				log.WithFields(map[string]any{
					"panic":      err,
					"stack":      string(stack),
					"method":     c.Request.Method,
					"path":       c.Request.URL.Path,
					"query":      c.Request.URL.RawQuery,
					"user_agent": c.GetHeader("User-Agent"),
					"client_ip":  c.ClientIP(),
					"request_id": c.GetString("request_id"),
				}).Error("服务器发生panic")

				// 返回500错误响应
				c.JSON(http.StatusInternalServerError, database.APIResponse{
					Code:      500,
					Message:   "服务器内部错误",
					Error:     "系统发生异常，请稍后重试",
					Timestamp: time.Now().Format(time.RFC3339),
					RequestID: c.GetString("request_id"),
				})

				c.Abort()
			}
		}()

		c.Next()
	}
}

// EnhancedErrorHandler 增强的错误处理中间件
func EnhancedErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始
		start := time.Now()
		log := logger.GetDefaultLogger().WithContext(c.Request.Context())

		// 记录请求信息
		log.WithFields(map[string]any{
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"query":      c.Request.URL.RawQuery,
			"user_agent": c.GetHeader("User-Agent"),
			"client_ip":  c.ClientIP(),
			"request_id": c.GetString("request_id"),
		}).Info("请求开始")

		c.Next()

		// 计算请求耗时
		duration := time.Since(start)

		// 处理错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// 记录详细错误信息
			log.WithFields(map[string]any{
				"error":      err.Error(),
				"type":       err.Type,
				"meta":       err.Meta,
				"duration":   duration.String(),
				"status":     c.Writer.Status(),
				"request_id": c.GetString("request_id"),
			}).Error("请求处理失败")

			// 如果还没有响应，返回错误响应
			if !c.Writer.Written() {
				c.JSON(http.StatusInternalServerError, database.APIResponse{
					Code:      500,
					Message:   "服务器内部错误",
					Error:     err.Error(),
					Timestamp: time.Now().Format(time.RFC3339),
					RequestID: c.GetString("request_id"),
				})
			}
		} else {
			// 记录成功请求
			log.WithFields(map[string]any{
				"duration":   duration.String(),
				"status":     c.Writer.Status(),
				"request_id": c.GetString("request_id"),
			}).Info("请求完成")
		}
	}
}
