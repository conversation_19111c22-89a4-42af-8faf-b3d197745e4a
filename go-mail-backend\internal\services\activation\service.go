package activation

import (
	"fmt"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/logger"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Service 激活码服务
type Service struct {
	db     *database.Database
	auth   *auth.Auth
	logger *logger.Logger
}

// NewService 创建激活码服务
func NewService(db *database.Database, authService *auth.Auth) *Service {
	return &Service{
		db:     db,
		auth:   authService,
		logger: logger.GetDefaultLogger(),
	}
}

// GenerateCodes 批量生成激活码
func (s *Service) GenerateCodes(count int, expiryDays int, prefix, description string) (*database.ActivationCodeBatchResponse, error) {
	if count <= 0 || count > 1000 {
		return nil, fmt.Errorf("生成数量必须在1-1000之间")
	}

	if expiryDays <= 0 || expiryDays > 365 {
		return nil, fmt.E<PERSON><PERSON>("有效期必须在1-365天之间")
	}

	// 生成批次ID
	batchID := fmt.Sprintf("batch_%s", uuid.New().String()[:8])
	expiresAt := time.Now().AddDate(0, 0, expiryDays)

	var codes []string

	// 开始事务
	tx, err := s.db.GetDB().Begin()
	if err != nil {
		return nil, fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 批量生成激活码
	for i := 0; i < count; i++ {
		code := s.auth.GenerateActivationCode(prefix)
		codes = append(codes, code)

		// 插入数据库
		_, err = tx.Exec(`
			INSERT INTO activation_codes (code, status, expires_at, description, batch_id)
			VALUES (?, ?, ?, ?, ?)`,
			code, database.ActivationCodeStatusUnused, expiresAt, description, batchID)
		if err != nil {
			return nil, fmt.Errorf("插入激活码失败: %w", err)
		}
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	return &database.ActivationCodeBatchResponse{
		BatchID:   batchID,
		Codes:     codes,
		Count:     count,
		ExpiresAt: expiresAt,
	}, nil
}

// VerifyCode 验证激活码（管理后台使用）
func (s *Service) VerifyCode(code string, deviceInfo database.DeviceInfo) error {
	// 查询激活码
	var activationCode database.ActivationCode
	err := s.db.GetDB().QueryRow(`
		SELECT id, code, device_fingerprint, mac_address, status, expires_at, used_at
		FROM activation_codes WHERE code = ?`,
		code).Scan(&activationCode.ID, &activationCode.Code, &activationCode.DeviceFingerprint,
		&activationCode.MacAddress, &activationCode.Status, &activationCode.ExpiresAt, &activationCode.UsedAt)

	if err != nil {
		return fmt.Errorf("激活码不存在")
	}

	// 检查激活码状态
	if activationCode.Status == database.ActivationCodeStatusExpired {
		return fmt.Errorf("激活码已过期")
	}

	if activationCode.Status == database.ActivationCodeStatusUsed {
		return fmt.Errorf("激活码已使用")
	}

	// 检查是否过期
	if time.Now().After(activationCode.ExpiresAt) {
		// 更新状态为过期
		s.db.GetDB().Exec("UPDATE activation_codes SET status = ? WHERE id = ?",
			database.ActivationCodeStatusExpired, activationCode.ID)
		return fmt.Errorf("激活码已过期")
	}

	// 生成设备指纹
	deviceFingerprint := s.auth.GenerateDeviceFingerprint(
		deviceInfo.SystemUUID, deviceInfo.Username, deviceInfo.ComputerName,
		deviceInfo.Platform, deviceInfo.MacAddress)

	// 如果激活码未绑定设备，则绑定
	if stringPtrIsEmpty(activationCode.DeviceFingerprint) {
		_, err = s.db.GetDB().Exec(`
			UPDATE activation_codes
			SET device_fingerprint = ?, mac_address = ?, status = ?, used_at = CURRENT_TIMESTAMP
			WHERE id = ?`,
			deviceFingerprint, deviceInfo.MacAddress, database.ActivationCodeStatusUsed, activationCode.ID)
		if err != nil {
			return fmt.Errorf("绑定设备失败: %w", err)
		}
	} else {
		// 验证设备指纹是否匹配
		if !stringPtrEquals(activationCode.DeviceFingerprint, deviceFingerprint) {
			return fmt.Errorf("设备指纹不匹配")
		}
		if !stringPtrEquals(activationCode.MacAddress, deviceInfo.MacAddress) {
			return fmt.Errorf("MAC地址不匹配")
		}
	}

	return nil
}

// ValidateActivationCode 验证激活码（客户端使用）
func (s *Service) ValidateActivationCode(code string, deviceInfo database.DeviceInfo, macAddress string) error {
	// 查询激活码
	var activationCode database.ActivationCode
	err := s.db.GetDB().QueryRow(`
		SELECT id, code, device_fingerprint, mac_address, status, expires_at
		FROM activation_codes WHERE code = ?`,
		code).Scan(&activationCode.ID, &activationCode.Code, &activationCode.DeviceFingerprint,
		&activationCode.MacAddress, &activationCode.Status, &activationCode.ExpiresAt)

	if err != nil {
		return fmt.Errorf("activation_code_not_found")
	}

	// 检查是否过期
	if time.Now().After(activationCode.ExpiresAt) {
		// 更新状态为过期
		s.db.GetDB().Exec("UPDATE activation_codes SET status = ? WHERE id = ?",
			database.ActivationCodeStatusExpired, activationCode.ID)
		return fmt.Errorf("activation_code_expired")
	}

	// 生成设备指纹
	deviceFingerprint := s.auth.GenerateDeviceFingerprint(
		deviceInfo.SystemUUID, deviceInfo.Username, deviceInfo.ComputerName,
		deviceInfo.Platform, deviceInfo.MacAddress)

	// 如果激活码未绑定设备，则绑定
	if stringPtrIsEmpty(activationCode.DeviceFingerprint) {
		_, err = s.db.GetDB().Exec(`
			UPDATE activation_codes
			SET device_fingerprint = ?, mac_address = ?, status = ?, used_at = CURRENT_TIMESTAMP
			WHERE id = ?`,
			deviceFingerprint, macAddress, database.ActivationCodeStatusUsed, activationCode.ID)
		if err != nil {
			return fmt.Errorf("device_binding_failed")
		}
	} else {
		// 验证设备指纹是否匹配
		if !stringPtrEquals(activationCode.DeviceFingerprint, deviceFingerprint) {
			return fmt.Errorf("device_fingerprint_mismatch")
		}
		if !stringPtrEquals(activationCode.MacAddress, macAddress) {
			return fmt.Errorf("mac_address_mismatch")
		}
	}

	return nil
}

// ListCodes 获取激活码列表
func (s *Service) ListCodes(page, size int, status string) ([]database.ActivationCode, int, error) {
	// 获取日志记录器
	log := s.logger.WithFields(map[string]any{
		"service":   "activation",
		"operation": "ListCodes",
		"page":      page,
		"size":      size,
		"status":    status,
	})

	log.Info("开始查询激活码列表")

	if page <= 0 {
		log.Warn("页码参数无效，使用默认值", "original_page", page)
		page = 1
	}
	if size <= 0 || size > 100 {
		log.Warn("页面大小参数无效，使用默认值", "original_size", size)
		size = 20
	}

	offset := (page - 1) * size
	log.Debug("计算分页参数", "offset", offset)

	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}

	if status != "" {
		whereClause += " AND status = ?"
		args = append(args, status)
		log.Debug("添加状态筛选条件", "status", status)
	}

	// 查询总数
	var total int
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM activation_codes %s", whereClause)
	log.Debug("执行计数查询", "sql", countSQL, "args", args)

	countStart := time.Now()
	err := s.db.GetDB().QueryRow(countSQL, args...).Scan(&total)
	countDuration := time.Since(countStart)

	if err != nil {
		log.WithError(err).WithFields(map[string]any{
			"sql":      countSQL,
			"args":     args,
			"duration": countDuration.String(),
		}).Error("计数查询失败")
		return nil, 0, fmt.Errorf("查询激活码总数失败: %w", err)
	}

	log.Info("计数查询成功", "total", total, "duration", countDuration.String())

	// 查询数据
	listSQL := fmt.Sprintf(`
		SELECT id, code, device_fingerprint, mac_address, status, expires_at, used_at, created_at, description, batch_id
		FROM activation_codes %s
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?`, whereClause)

	args = append(args, size, offset)
	log.Debug("执行列表查询", "sql", listSQL, "args", args)

	listStart := time.Now()
	rows, err := s.db.GetDB().Query(listSQL, args...)
	if err != nil {
		listDuration := time.Since(listStart)
		log.WithError(err).WithFields(map[string]any{
			"sql":      listSQL,
			"args":     args,
			"duration": listDuration.String(),
		}).Error("列表查询失败")
		return nil, 0, fmt.Errorf("查询激活码列表失败: %w", err)
	}
	defer rows.Close()

	var codes []database.ActivationCode
	scanStart := time.Now()
	for rows.Next() {
		var code database.ActivationCode
		err := rows.Scan(&code.ID, &code.Code, &code.DeviceFingerprint, &code.MacAddress,
			&code.Status, &code.ExpiresAt, &code.UsedAt, &code.CreatedAt, &code.Description, &code.BatchID)
		if err != nil {
			scanDuration := time.Since(scanStart)
			log.WithError(err).WithFields(map[string]any{
				"scan_duration": scanDuration.String(),
				"codes_count":   len(codes),
			}).Error("数据扫描失败")
			return nil, 0, fmt.Errorf("扫描激活码数据失败: %w", err)
		}
		codes = append(codes, code)
	}

	// 检查行扫描错误
	if err = rows.Err(); err != nil {
		scanDuration := time.Since(scanStart)
		log.WithError(err).WithFields(map[string]any{
			"scan_duration": scanDuration.String(),
			"codes_count":   len(codes),
		}).Error("行扫描过程中发生错误")
		return nil, 0, fmt.Errorf("行扫描错误: %w", err)
	}

	totalDuration := time.Since(listStart)
	log.Info("激活码列表查询完成",
		"result_count", len(codes),
		"total_count", total,
		"query_duration", totalDuration.String(),
		"scan_duration", time.Since(scanStart).String())

	return codes, total, nil
}

// stringPtrIsEmpty 检查字符串指针是否为空或指向空字符串
func stringPtrIsEmpty(ptr *string) bool {
	return ptr == nil || *ptr == ""
}

// stringPtrEquals 比较字符串指针与字符串是否相等
func stringPtrEquals(ptr *string, str string) bool {
	if ptr == nil {
		return str == ""
	}
	return *ptr == str
}

// GetCodeInfo 获取激活码信息
func (s *Service) GetCodeInfo(code string) (*database.ActivationCode, error) {
	var activationCode database.ActivationCode
	err := s.db.GetDB().QueryRow(`
		SELECT id, code, device_fingerprint, mac_address, status, expires_at, used_at, created_at, description, batch_id
		FROM activation_codes WHERE code = ?`,
		code).Scan(&activationCode.ID, &activationCode.Code, &activationCode.DeviceFingerprint,
		&activationCode.MacAddress, &activationCode.Status, &activationCode.ExpiresAt,
		&activationCode.UsedAt, &activationCode.CreatedAt, &activationCode.Description, &activationCode.BatchID)

	if err != nil {
		return nil, fmt.Errorf("激活码不存在")
	}

	return &activationCode, nil
}

// DeleteCode 删除激活码
func (s *Service) DeleteCode(codeID int) error {
	result, err := s.db.GetDB().Exec("DELETE FROM activation_codes WHERE id = ?", codeID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("激活码不存在")
	}

	return nil
}

// BatchDeleteCodes 批量删除激活码
func (s *Service) BatchDeleteCodes(codeIDs []int) error {
	if len(codeIDs) == 0 {
		return fmt.Errorf("删除列表不能为空")
	}

	if len(codeIDs) > 100 {
		return fmt.Errorf("单次删除数量不能超过100个")
	}

	// 获取日志记录器
	log := s.logger.WithFields(map[string]any{
		"service":   "activation",
		"operation": "BatchDeleteCodes",
		"count":     len(codeIDs),
		"ids":       codeIDs,
	})

	log.Info("开始批量删除激活码")

	// 开始事务
	tx, err := s.db.GetDB().Begin()
	if err != nil {
		log.WithError(err).Error("开始事务失败")
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback() // 确保在出错时回滚

	// 构建批量删除SQL
	placeholders := make([]string, len(codeIDs))
	args := make([]interface{}, len(codeIDs))
	for i, id := range codeIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	deleteSQL := fmt.Sprintf("DELETE FROM activation_codes WHERE id IN (%s)",
		strings.Join(placeholders, ","))

	log.Debug("执行批量删除", "sql", deleteSQL, "args", args)

	start := time.Now()
	result, err := tx.Exec(deleteSQL, args...)
	duration := time.Since(start)

	if err != nil {
		log.WithError(err).WithFields(map[string]any{
			"sql":      deleteSQL,
			"args":     args,
			"duration": duration.String(),
		}).Error("批量删除执行失败")
		return fmt.Errorf("批量删除执行失败: %w", err)
	}

	// 检查影响的行数
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.WithError(err).Error("获取影响行数失败")
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		log.WithError(err).Error("提交事务失败")
		return fmt.Errorf("提交事务失败: %w", err)
	}

	log.Info("批量删除激活码完成",
		"requested_count", len(codeIDs),
		"deleted_count", rowsAffected,
		"duration", duration.String())

	return nil
}

// ExpireCode 使激活码过期
func (s *Service) ExpireCode(codeID int) error {
	result, err := s.db.GetDB().Exec(`
		UPDATE activation_codes 
		SET status = ? 
		WHERE id = ? AND status != ?`,
		database.ActivationCodeStatusExpired, codeID, database.ActivationCodeStatusExpired)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("激活码不存在或已过期")
	}

	return nil
}

// GetStats 获取激活码统计信息
func (s *Service) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取总数
	var total int
	err := s.db.GetDB().QueryRow("SELECT COUNT(*) FROM activation_codes").Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("获取总数失败: %w", err)
	}
	stats["total"] = total

	// 获取各状态的数量
	var active, used, expired int

	err = s.db.GetDB().QueryRow("SELECT COUNT(*) FROM activation_codes WHERE status = ?",
		database.ActivationCodeStatusUnused).Scan(&active)
	if err != nil {
		return nil, fmt.Errorf("获取活跃数量失败: %w", err)
	}
	stats["active"] = active

	err = s.db.GetDB().QueryRow("SELECT COUNT(*) FROM activation_codes WHERE status = ?",
		database.ActivationCodeStatusUsed).Scan(&used)
	if err != nil {
		return nil, fmt.Errorf("获取已使用数量失败: %w", err)
	}
	stats["used"] = used

	err = s.db.GetDB().QueryRow("SELECT COUNT(*) FROM activation_codes WHERE status = ?",
		database.ActivationCodeStatusExpired).Scan(&expired)
	if err != nil {
		return nil, fmt.Errorf("获取过期数量失败: %w", err)
	}
	stats["expired"] = expired

	return stats, nil
}
