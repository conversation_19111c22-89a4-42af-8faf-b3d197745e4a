# 常用模式和最佳实践

- API路由重构模式：将单一的admin_api.go文件按功能模块拆分为独立的控制器文件（auth_controller.go、user_controller.go、activation_controller.go、mailbox_controller.go、task_log_controller.go、monitor_controller.go、config_controller.go、system_controller.go），每个控制器包含相关的路由注册逻辑，主文件admin_api.go只保留AdminAPI结构体、构造函数和主RegisterRoutes函数来调用各个独立控制器
- 邮箱管理服务优化模式：1. 在数据库查询中移除敏感字段（如cookie_data）以保护隐私；2. 在结构体JSON标签中使用`json:"-"`屏蔽敏感字段；3. 使用自定义类型的Scan方法进行标准JSON解析，替代简单字符串分割；4. 添加JSON解析错误处理，确保单个字段解析失败不影响整体数据加载；5. 保持向后兼容性，对空值和无效JSON进行适当处理
