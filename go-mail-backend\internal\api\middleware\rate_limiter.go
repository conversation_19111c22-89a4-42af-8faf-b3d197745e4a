package middleware

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 频率限制器
type RateLimiter struct {
	clients  map[string]*ClientLimiter
	mutex    sync.RWMutex
	stopChan chan struct{}
}

// ClientLimiter 客户端限制器
type ClientLimiter struct {
	tokens     int
	maxTokens  int
	refillRate int
	lastRefill time.Time
	mutex      sync.Mutex
}

// NewRateLimiter 创建频率限制器
func NewRateLimiter() *RateLimiter {
	rl := &RateLimiter{
		clients:  make(map[string]*ClientLimiter),
		stopChan: make(chan struct{}),
	}

	// 启动清理协程
	go rl.cleanupRoutine()

	return rl
}

// RateLimit 频率限制中间件
func (rl *RateLimiter) RateLimit(maxRequests int, window time.Duration) gin.HandlerFunc {
	refillRate := int(window.Seconds()) / maxRequests
	if refillRate < 1 {
		refillRate = 1
	}

	return func(c *gin.Context) {
		clientID := rl.getClientID(c)

		if !rl.allowRequest(clientID, maxRequests, refillRate) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"message": "请求过于频繁，请稍后再试",
				"error":   "rate_limit_exceeded",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// BatchOperationRateLimit 批量操作专用频率限制
func (rl *RateLimiter) BatchOperationRateLimit() gin.HandlerFunc {
	return rl.RateLimit(30, time.Minute*10) // 每10分钟最多30次批量操作
}

// VerificationRateLimit 验证操作频率限制
func (rl *RateLimiter) VerificationRateLimit() gin.HandlerFunc {
	return rl.RateLimit(30, time.Minute*10) // 每10分钟最多30次验证操作
}

// ImportRateLimit 导入操作频率限制
func (rl *RateLimiter) ImportRateLimit() gin.HandlerFunc {
	return rl.RateLimit(30, time.Minute*10) // 每10分钟最多30次导入操作
}

// getClientID 获取客户端ID
func (rl *RateLimiter) getClientID(c *gin.Context) string {
	// 优先使用用户ID
	if userID, exists := c.Get("user_id"); exists {
		// user_id 可能是 int 或 string 类型
		switch v := userID.(type) {
		case int:
			return fmt.Sprintf("user_%d", v)
		case string:
			return fmt.Sprintf("user_%s", v)
		default:
			return fmt.Sprintf("user_%v", v)
		}
	}

	// 回退到IP地址
	return fmt.Sprintf("ip_%s", c.ClientIP())
}

// allowRequest 检查是否允许请求
func (rl *RateLimiter) allowRequest(clientID string, maxTokens, refillRate int) bool {
	rl.mutex.Lock()
	client, exists := rl.clients[clientID]
	if !exists {
		client = &ClientLimiter{
			tokens:     maxTokens,
			maxTokens:  maxTokens,
			refillRate: refillRate,
			lastRefill: time.Now(),
		}
		rl.clients[clientID] = client
	}
	rl.mutex.Unlock()

	client.mutex.Lock()
	defer client.mutex.Unlock()

	// 补充令牌
	now := time.Now()
	elapsed := now.Sub(client.lastRefill).Seconds()
	tokensToAdd := int(elapsed) / client.refillRate

	if tokensToAdd > 0 {
		client.tokens += tokensToAdd
		if client.tokens > client.maxTokens {
			client.tokens = client.maxTokens
		}
		client.lastRefill = now
	}

	// 检查是否有可用令牌
	if client.tokens > 0 {
		client.tokens--
		return true
	}

	return false
}

// cleanupRoutine 清理过期的客户端记录
func (rl *RateLimiter) cleanupRoutine() {
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rl.cleanupExpired()
		case <-rl.stopChan:
			return
		}
	}
}

// cleanupExpired 清理过期记录
func (rl *RateLimiter) cleanupExpired() {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	for clientID, client := range rl.clients {
		client.mutex.Lock()
		// 如果客户端超过2小时没有活动，删除记录
		if now.Sub(client.lastRefill) > time.Hour*2 {
			delete(rl.clients, clientID)
		}
		client.mutex.Unlock()
	}
}

// Stop 停止频率限制器
func (rl *RateLimiter) Stop() {
	close(rl.stopChan)
}

// ConcurrencyLimiter 并发限制器
type ConcurrencyLimiter struct {
	semaphore chan struct{}
	timeout   time.Duration
}

// NewConcurrencyLimiter 创建并发限制器
func NewConcurrencyLimiter(maxConcurrent int, timeout time.Duration) *ConcurrencyLimiter {
	return &ConcurrencyLimiter{
		semaphore: make(chan struct{}, maxConcurrent),
		timeout:   timeout,
	}
}

// ConcurrencyLimit 并发限制中间件
func (cl *ConcurrencyLimiter) ConcurrencyLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试获取信号量
		select {
		case cl.semaphore <- struct{}{}:
			// 获取成功，继续处理
			defer func() { <-cl.semaphore }()
			c.Next()
		case <-time.After(cl.timeout):
			// 超时，返回错误
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"success": false,
				"message": "服务器繁忙，请稍后再试",
				"error":   "concurrency_limit_exceeded",
			})
			c.Abort()
		}
	}
}

// BatchOperationConcurrencyLimit 批量操作并发限制
func BatchOperationConcurrencyLimit() gin.HandlerFunc {
	limiter := NewConcurrencyLimiter(5, time.Second*10) // 最多5个并发批量操作，超时10秒
	return limiter.ConcurrencyLimit()
}

// VerificationConcurrencyLimit 验证操作并发限制
func VerificationConcurrencyLimit() gin.HandlerFunc {
	limiter := NewConcurrencyLimiter(5, time.Second*10) // 最多5个并发验证操作，超时10秒
	return limiter.ConcurrencyLimit()
}

// RequestSizeLimit 请求大小限制中间件
func RequestSizeLimit(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"success": false,
				"message": fmt.Sprintf("请求体过大，最大允许 %d 字节", maxSize),
				"error":   "request_too_large",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// BatchImportSizeLimit 批量导入大小限制
func BatchImportSizeLimit() gin.HandlerFunc {
	return RequestSizeLimit(10 * 1024 * 1024) // 10MB
}

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止点击劫持
		c.Header("X-Frame-Options", "DENY")

		// 防止MIME类型嗅探
		c.Header("X-Content-Type-Options", "nosniff")

		// XSS保护
		c.Header("X-XSS-Protection", "1; mode=block")

		// 强制HTTPS（在生产环境中）
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

		// 内容安全策略
		c.Header("Content-Security-Policy", "default-src 'self'")

		// 引用策略
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		c.Next()
	}
}

// IPWhitelist IP白名单中间件
func IPWhitelist(allowedIPs []string) gin.HandlerFunc {
	allowedMap := make(map[string]bool)
	for _, ip := range allowedIPs {
		allowedMap[ip] = true
	}

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		if len(allowedMap) > 0 && !allowedMap[clientIP] {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"message": "访问被拒绝",
				"error":   "ip_not_allowed",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequestLogger 请求日志中间件（增强版）
func RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.ClientIP,
			param.Method,
			param.StatusCode,
			param.Latency,
			param.Path,
			param.ErrorMessage,
		)
	})
}

// OperationAudit 操作审计中间件
func OperationAudit() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// 记录请求信息
		userID := ""
		if uid, exists := c.Get("user_id"); exists {
			// user_id 可能是 int 或 string 类型
			switch v := uid.(type) {
			case int:
				userID = fmt.Sprintf("%d", v)
			case string:
				userID = v
			default:
				userID = fmt.Sprintf("%v", v)
			}
		}

		// 处理请求
		c.Next()

		// 记录操作日志（对于重要操作）
		if shouldAudit(c.Request.URL.Path, c.Request.Method) {
			duration := time.Since(start)

			// TODO: 将审计日志写入数据库或日志文件
			fmt.Printf("[AUDIT] User: %s, Method: %s, Path: %s, Status: %d, Duration: %v, IP: %s\n",
				userID, c.Request.Method, c.Request.URL.Path, c.Writer.Status(), duration, c.ClientIP())
		}
	}
}

// shouldAudit 判断是否需要审计
func shouldAudit(path, method string) bool {
	auditPaths := []string{
		"/api/v1/mailbox/batch-import",
		"/api/v1/mailbox/verify",
		"/api/v1/mailbox/task-control",
	}

	for _, auditPath := range auditPaths {
		if path == auditPath && (method == "POST" || method == "PUT" || method == "DELETE") {
			return true
		}
	}

	return false
}
