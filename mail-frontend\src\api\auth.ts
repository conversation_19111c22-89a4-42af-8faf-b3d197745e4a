import { api } from '@/utils/request'
import type { ApiResponse, LoginRequest, LoginResponse, UserInfo } from '@/types/api'

// 认证相关API
export const authApi = {
  // 用户登录
  login: (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    return api.post('/auth/login', data)
  },

  // 刷新token
  refreshToken: (refreshToken: string): Promise<ApiResponse<LoginResponse>> => {
    return api.post('/auth/refresh', { refreshToken })
  },

  // 用户登出
  logout: (): Promise<ApiResponse> => {
    return api.post('/auth/logout')
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<ApiResponse<UserInfo>> => {
    return api.get('/auth/me')
  },

  // 修改密码
  changePassword: (data: {
    oldPassword: string
    newPassword: string
  }): Promise<ApiResponse> => {
    return api.post('/auth/change-password', data)
  },

  // 验证token有效性
  validateToken: (): Promise<ApiResponse> => {
    return api.get('/auth/validate')
  },
}
