package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// MonitorController 系统监控控制器
type MonitorController struct {
	adminAPI *AdminAPI
}

// NewMonitorController 创建系统监控控制器
func NewMonitorController(adminAPI *AdminAPI) *MonitorController {
	return &MonitorController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册系统监控路由
func (c *MonitorController) RegisterRoutes(v1 *gin.RouterGroup) {
	monitorGroup := v1.Group("/monitor")
	monitorGroup.Use(middleware.JWTAuth(c.adminAPI.auth))
	{
		monitorHandler := handlers.NewMonitorHandler(c.adminAPI.services.Monitor, c.adminAPI.scheduler)
		c.adminAPI.logger.Info("注册系统监控路由", "prefix", "/api/v1/monitor")
		
		monitorGroup.GET("/statistics", monitorHandler.GetStatistics)
		monitorGroup.GET("/accounts", monitorHandler.GetAccountStatus)
		monitorGroup.GET("/sessions", monitorHandler.GetSessionStatus)
		monitorGroup.GET("/logs", monitorHandler.GetLogs)
		monitorGroup.GET("/tasks", monitorHandler.GetTaskStatus)
		monitorGroup.POST("/tasks/:task/:action", monitorHandler.ControlTask)
		
		c.adminAPI.logger.Info("系统监控路由注册完成", "routes", []string{
			"GET /api/v1/monitor/statistics",
			"GET /api/v1/monitor/accounts",
			"GET /api/v1/monitor/sessions",
			"GET /api/v1/monitor/logs",
			"GET /api/v1/monitor/tasks",
			"POST /api/v1/monitor/tasks/:task/:action",
		})
	}
}
