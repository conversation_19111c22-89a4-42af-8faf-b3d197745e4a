import { api } from '@/utils/request'
import type {
  ApiResponse,
  PaginatedResponse,
  ActivationCode,
  CreateActivationCodeRequest,
} from '@/types/api'

// 激活码管理API
export const activationApi = {
  // 获取激活码列表
  getActivationCodes: (params: {
    page?: number
    size?: number
    status?: string
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<ActivationCode>>> => {
    return api.get('/activation/list', { params })
  },

  // 创建激活码
  createActivationCodes: (
    data: CreateActivationCodeRequest
  ): Promise<ApiResponse<ActivationCode[]>> => {
    return api.post('/activation/generate', data)
  },

  // 获取单个激活码详情
  getActivationCode: (id: string): Promise<ApiResponse<ActivationCode>> => {
    return api.get(`/activation/${id}`)
  },

  // 删除激活码
  deleteActivationCode: (id: string): Promise<ApiResponse> => {
    return api.delete(`/activation/${id}`)
  },

  // 批量删除激活码
  batchDeleteActivationCodes: (ids: string[]): Promise<ApiResponse> => {
    return api.post('/activation/batch-delete', { ids })
  },

  // 重置激活码状态
  resetActivationCode: (id: string): Promise<ApiResponse> => {
    return api.post(`/activation/${id}/expire`)
  },

  // 导出激活码
  exportActivationCodes: (params: { status?: string; format?: 'csv' | 'xlsx' }): Promise<Blob> => {
    return api.get('/activation/export', {
      params,
      responseType: 'blob',
    })
  },

  // 获取激活码统计信息
  getActivationCodeStats: (): Promise<
    ApiResponse<{
      total: number
      active: number
      used: number
      expired: number
    }>
  > => {
    return api.get('/activation/stats')
  },
}
