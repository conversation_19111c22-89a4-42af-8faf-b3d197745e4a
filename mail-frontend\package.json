{"name": "go-mail-frontend", "version": "1.0.0", "description": "Go-Mail临时邮箱服务管理后台", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "axios": "^1.7.9", "echarts": "^5.5.1", "naive-ui": "^2.40.1", "pinia": "^2.3.0", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/node": "^22.10.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^5.2.0", "eslint": "^9.17.0", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "typescript": "^5.7.2", "vite": "^6.0.5", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^2.1.10"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}