import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { UserInfo, LoginRequest } from '@/types/api'
import { generateDeviceFingerprint } from '@/utils/device'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const user = ref<UserInfo | null>(null)
  const deviceFingerprint = ref<string>('')
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 初始化设备指纹
  const initializeDeviceFingerprint = async () => {
    if (!deviceFingerprint.value) {
      deviceFingerprint.value = await generateDeviceFingerprint()
    }
  }

  // 登录
  const login = async (loginData: Omit<LoginRequest, 'deviceFingerprint'>) => {
    try {
      isLoading.value = true

      // 确保设备指纹已生成
      await initializeDeviceFingerprint()

      const response = await authApi.login({
        ...loginData,
        deviceFingerprint: deviceFingerprint.value,
      })

      if (response.success && response.data) {
        token.value = response.data.token
        refreshToken.value = response.data.refresh_token
        user.value = response.data.user

        // 保存到localStorage
        localStorage.setItem('auth-token', token.value)
        localStorage.setItem('auth-refresh-token', refreshToken.value)
        localStorage.setItem('auth-user', JSON.stringify(user.value))
        localStorage.setItem('device-fingerprint', deviceFingerprint.value)

        return { success: true, message: '登录成功' }
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error: any) {
      console.error('Login error:', error)
      return { success: false, message: error.message || '登录失败' }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出API
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      refreshToken.value = ''
      user.value = null

      // 清除localStorage
      localStorage.removeItem('auth-token')
      localStorage.removeItem('auth-refresh-token')
      localStorage.removeItem('auth-user')

      // 重定向到登录页
      window.location.href = '/login'
    }
  }

  // 刷新token
  const refreshAuthToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      const response = await authApi.refreshToken(refreshToken.value)

      if (response.success && response.data) {
        token.value = response.data.token
        refreshToken.value = response.data.refresh_token
        user.value = response.data.user

        // 更新localStorage
        localStorage.setItem('auth-token', token.value)
        localStorage.setItem('auth-refresh-token', refreshToken.value)
        localStorage.setItem('auth-user', JSON.stringify(user.value))

        return true
      } else {
        throw new Error(response.message || 'Token refresh failed')
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      await logout()
      return false
    }
  }

  // 验证token有效性
  const validateToken = async () => {
    try {
      if (!token.value) return false

      const response = await authApi.validateToken()
      return response.success
    } catch (error) {
      console.error('Token validation error:', error)
      return false
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const response = await authApi.getCurrentUser()
      if (response.success && response.data) {
        user.value = response.data
        localStorage.setItem('auth-user', JSON.stringify(user.value))
        return response.data
      }
      return null
    } catch (error) {
      console.error('Get current user error:', error)
      return null
    }
  }

  // 初始化认证状态
  const initializeAuth = async () => {
    try {
      // 从localStorage恢复状态
      const savedToken = localStorage.getItem('auth-token')
      const savedRefreshToken = localStorage.getItem('auth-refresh-token')
      const savedUser = localStorage.getItem('auth-user')
      const savedDeviceFingerprint = localStorage.getItem('device-fingerprint')

      console.log('Initializing auth with saved data:', {
        hasToken: !!savedToken,
        hasRefreshToken: !!savedRefreshToken,
        hasUser: !!savedUser,
        hasDeviceFingerprint: !!savedDeviceFingerprint,
      })

      if (savedToken && savedUser) {
        // 先设置状态，这样请求拦截器就能获取到token
        token.value = savedToken
        refreshToken.value = savedRefreshToken || ''
        user.value = JSON.parse(savedUser)
        deviceFingerprint.value = savedDeviceFingerprint || ''

        console.log('Auth state restored, validating token...')

        // 验证token是否仍然有效
        const isValid = await validateToken()
        console.log('Token validation result:', isValid)

        if (!isValid) {
          console.log('Token invalid, attempting refresh...')
          // 尝试刷新token
          const refreshed = await refreshAuthToken()
          if (!refreshed) {
            console.log('Token refresh failed, logging out...')
            // 刷新失败，清除状态
            await logout()
            return false
          }
          console.log('Token refreshed successfully')
        }

        console.log('Auth initialization successful')
        return true
      } else {
        console.log('No saved auth data found, initializing device fingerprint...')
        // 初始化设备指纹
        await initializeDeviceFingerprint()
        return false
      }
    } catch (error) {
      console.error('Initialize auth error:', error)
      await logout()
      return false
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    deviceFingerprint,
    isLoading,

    // 计算属性
    isAuthenticated,
    isAdmin,

    // 方法
    login,
    logout,
    refreshAuthToken,
    validateToken,
    getCurrentUser,
    initializeAuth,
    initializeDeviceFingerprint,
  }
})
