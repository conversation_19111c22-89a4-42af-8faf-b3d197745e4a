<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">
          <n-icon size="120" color="#d03050">
            <AlertCircleIcon />
          </n-icon>
        </div>
      </div>

      <div class="error-info">
        <h1>页面不存在</h1>
        <p>抱歉，您访问的页面不存在或已被移除。</p>

        <div class="error-details">
          <p>可能的原因：</p>
          <ul>
            <li>页面地址输入错误</li>
            <li>页面已被删除或移动</li>
            <li>您没有访问权限</li>
            <li>服务器配置问题</li>
          </ul>
        </div>

        <div class="error-actions">
          <n-space>
            <n-button type="primary" @click="goHome">
              <template #icon>
                <n-icon>
                  <HomeIcon />
                </n-icon>
              </template>
              返回首页
            </n-button>

            <n-button @click="goBack">
              <template #icon>
                <n-icon>
                  <ArrowBackIcon />
                </n-icon>
              </template>
              返回上页
            </n-button>

            <n-button @click="refresh">
              <template #icon>
                <n-icon>
                  <RefreshIcon />
                </n-icon>
              </template>
              刷新页面
            </n-button>
          </n-space>
        </div>
      </div>
    </div>

    <!-- 建议链接 -->
    <div class="suggestions">
      <h3>您可能想要访问：</h3>
      <div class="suggestion-links">
        <n-card
          v-for="link in suggestedLinks"
          :key="link.path"
          class="suggestion-card"
          hoverable
          @click="navigateTo(link.path)"
        >
          <div class="suggestion-content">
            <n-icon size="24" :color="link.color">
              <component :is="link.icon" />
            </n-icon>
            <div class="suggestion-info">
              <h4>{{ link.title }}</h4>
              <p>{{ link.description }}</p>
            </div>
          </div>
        </n-card>
      </div>
    </div>

    <!-- 联系信息 -->
    <div class="contact-info">
      <n-alert type="info" title="需要帮助？">
        如果您认为这是一个错误，请联系系统管理员或查看系统日志获取更多信息。
      </n-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { NIcon, NButton, NSpace, NCard, NAlert } from 'naive-ui'
import {
  AlertCircle as AlertCircleIcon,
  Home as HomeIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  SpeedometerOutline as DashboardIcon,
  Key as KeyIcon,
  DesktopOutline as MonitorIcon,
  Settings as SettingsIcon,
} from '@vicons/ionicons5'

const router = useRouter()

// 建议的链接
const suggestedLinks = [
  {
    path: '/dashboard',
    title: '系统概览',
    description: '查看系统运行状态和统计信息',
    icon: DashboardIcon,
    color: '#18a058',
  },
  {
    path: '/activation-codes',
    title: '激活管理',
    description: '管理和创建激活码',
    icon: KeyIcon,
    color: '#2080f0',
  },
  {
    path: '/system-monitor',
    title: '系统监控',
    description: '监控系统性能和任务状态',
    icon: MonitorIcon,
    color: '#f0a020',
  },
  {
    path: '/system-config',
    title: '系统配置',
    description: '配置系统参数和设置',
    icon: SettingsIcon,
    color: '#d03050',
  },
]

// 导航方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const refresh = () => {
  window.location.reload()
}

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.not-found-content {
  max-width: 600px;
  text-align: center;
  margin-bottom: 40px;
}

.error-illustration {
  position: relative;
  margin-bottom: 32px;
}

.error-code {
  font-size: 120px;
  font-weight: 900;
  color: #d03050;
  line-height: 1;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-icon {
  margin-bottom: 24px;
}

.error-info h1 {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.error-info > p {
  font-size: 16px;
  color: #666;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.error-details {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 20px;
  margin: 24px 0;
  text-align: left;
}

.error-details p {
  margin: 0 0 12px 0;
  font-weight: 600;
  color: #333;
}

.error-details ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.error-details li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.error-actions {
  margin-top: 32px;
}

.suggestions {
  max-width: 800px;
  width: 100%;
  margin-bottom: 32px;
}

.suggestions h3 {
  text-align: center;
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.suggestion-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.suggestion-card {
  cursor: pointer;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.suggestion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.suggestion-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.suggestion-info {
  flex: 1;
  text-align: left;
}

.suggestion-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.suggestion-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.contact-info {
  max-width: 600px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found {
    padding: 20px 16px;
  }

  .error-code {
    font-size: 80px;
  }

  .error-info h1 {
    font-size: 24px;
  }

  .error-actions {
    margin-top: 24px;
  }

  .suggestion-links {
    grid-template-columns: 1fr;
  }

  .error-actions .n-space {
    flex-direction: column;
    width: 100%;
  }

  .error-actions .n-button {
    width: 100%;
  }
}

/* 暗色主题适配 */
:global(.dark) .not-found {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

:global(.dark) .error-info h1 {
  color: #fff;
}

:global(.dark) .error-info > p {
  color: #ccc;
}

:global(.dark) .error-details {
  background: rgba(255, 255, 255, 0.1);
}

:global(.dark) .error-details p {
  color: #fff;
}

:global(.dark) .error-details ul {
  color: #ccc;
}

:global(.dark) .suggestions h3 {
  color: #fff;
}

:global(.dark) .suggestion-info h4 {
  color: #fff;
}

:global(.dark) .suggestion-info p {
  color: #ccc;
}
</style>
