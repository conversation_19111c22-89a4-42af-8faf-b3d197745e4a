package errors

import (
	"fmt"
	"net/http"
)

// ErrorType 错误类型枚举
type ErrorType int

const (
	ErrorTypeUnknown ErrorType = iota
	ErrorTypeNetwork
	ErrorTypeAuthentication
	ErrorTypeSession
	ErrorTypeValidation
	ErrorTypeConfiguration
	ErrorTypeProxy
	ErrorTypeTimeout
	ErrorTypeRateLimit
)

func (e ErrorType) String() string {
	switch e {
	case ErrorTypeNetwork:
		return "network"
	case ErrorTypeAuthentication:
		return "authentication"
	case ErrorTypeSession:
		return "session"
	case ErrorTypeValidation:
		return "validation"
	case ErrorTypeConfiguration:
		return "configuration"
	case ErrorTypeProxy:
		return "proxy"
	case ErrorTypeTimeout:
		return "timeout"
	case ErrorTypeRateLimit:
		return "rate_limit"
	default:
		return "unknown"
	}
}

// MailError 自定义错误类型
type MailError struct {
	Type       ErrorType
	Code       string
	Message    string
	Details    map[string]interface{}
	Cause      error
	StatusCode int
	Retryable  bool
}

func (e *MailError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s:%s] %s: %v", e.Type, e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s:%s] %s", e.Type, e.Code, e.Message)
}

func (e *MailError) Unwrap() error {
	return e.Cause
}

func (e *MailError) IsRetryable() bool {
	return e.Retryable
}

func (e *MailError) GetType() ErrorType {
	return e.Type
}

func (e *MailError) GetCode() string {
	return e.Code
}

func (e *MailError) GetStatusCode() int {
	return e.StatusCode
}

func (e *MailError) WithDetail(key string, value interface{}) *MailError {
	if e.Details == nil {
		e.Details = make(map[string]interface{})
	}
	e.Details[key] = value
	return e
}

// 网络错误构造函数
func NewNetworkError(code, message string, cause error) *MailError {
	return &MailError{
		Type:      ErrorTypeNetwork,
		Code:      code,
		Message:   message,
		Cause:     cause,
		Retryable: true,
	}
}

// 认证错误构造函数
func NewAuthenticationError(code, message string, cause error) *MailError {
	return &MailError{
		Type:       ErrorTypeAuthentication,
		Code:       code,
		Message:    message,
		Cause:      cause,
		StatusCode: http.StatusUnauthorized,
		Retryable:  false,
	}
}

// 会话错误构造函数
func NewSessionError(code, message string, cause error) *MailError {
	return &MailError{
		Type:      ErrorTypeSession,
		Code:      code,
		Message:   message,
		Cause:     cause,
		Retryable: true,
	}
}

// 验证错误构造函数
func NewValidationError(code, message string, cause error) *MailError {
	return &MailError{
		Type:      ErrorTypeValidation,
		Code:      code,
		Message:   message,
		Cause:     cause,
		Retryable: false,
	}
}

// 配置错误构造函数
func NewConfigurationError(code, message string, cause error) *MailError {
	return &MailError{
		Type:      ErrorTypeConfiguration,
		Code:      code,
		Message:   message,
		Cause:     cause,
		Retryable: false,
	}
}

// 代理错误构造函数
func NewProxyError(code, message string, cause error) *MailError {
	return &MailError{
		Type:      ErrorTypeProxy,
		Code:      code,
		Message:   message,
		Cause:     cause,
		Retryable: true,
	}
}

// 超时错误构造函数
func NewTimeoutError(code, message string, cause error) *MailError {
	return &MailError{
		Type:      ErrorTypeTimeout,
		Code:      code,
		Message:   message,
		Cause:     cause,
		Retryable: true,
	}
}

// 限流错误构造函数
func NewRateLimitError(code, message string, cause error) *MailError {
	return &MailError{
		Type:      ErrorTypeRateLimit,
		Code:      code,
		Message:   message,
		Cause:     cause,
		Retryable: true,
	}
}

// 预定义错误代码
const (
	// 网络错误代码
	ErrCodeNetworkConnection = "NET_001"
	ErrCodeNetworkTimeout    = "NET_002"
	ErrCodeNetworkDNS        = "NET_003"

	// 认证错误代码
	ErrCodeAuthInvalidCredentials = "AUTH_001"
	ErrCodeAuthAccountLocked      = "AUTH_002"
	ErrCodeAuthAccountExpired     = "AUTH_003"
	ErrCodeAuthTwoFactorRequired  = "AUTH_004"

	// 会话错误代码
	ErrCodeSessionExpired  = "SESS_001"
	ErrCodeSessionInvalid  = "SESS_002"
	ErrCodeSessionNotFound = "SESS_003"
	ErrCodeSessionConflict = "SESS_004"

	// 验证错误代码
	ErrCodeValidationRequired = "VAL_001"
	ErrCodeValidationFormat   = "VAL_002"
	ErrCodeValidationRange    = "VAL_003"
	ErrCodeValidationConflict = "VAL_004"

	// 配置错误代码
	ErrCodeConfigInvalid  = "CFG_001"
	ErrCodeConfigMissing  = "CFG_002"
	ErrCodeConfigConflict = "CFG_003"

	// 代理错误代码
	ErrCodeProxyConnection = "PROXY_001"
	ErrCodeProxyAuth       = "PROXY_002"
	ErrCodeProxyTimeout    = "PROXY_003"
	ErrCodeProxyNotFound   = "PROXY_004"

	// 超时错误代码
	ErrCodeTimeoutRequest = "TIMEOUT_001"
	ErrCodeTimeoutLogin   = "TIMEOUT_002"

	// 限流错误代码
	ErrCodeRateLimitExceeded = "RATE_001"
	ErrCodeRateLimitQuota    = "RATE_002"
)

// 预定义错误消息
var (
	ErrInvalidCredentials = NewAuthenticationError(ErrCodeAuthInvalidCredentials, "用户名或密码错误", nil)
	ErrAccountLocked      = NewAuthenticationError(ErrCodeAuthAccountLocked, "账户已被锁定", nil)
	ErrSessionExpired     = NewSessionError(ErrCodeSessionExpired, "会话已过期", nil)
	ErrSessionNotFound    = NewSessionError(ErrCodeSessionNotFound, "会话不存在", nil)
	ErrProxyNotAvailable  = NewProxyError(ErrCodeProxyNotFound, "没有可用的代理", nil)
	ErrNetworkTimeout     = NewTimeoutError(ErrCodeTimeoutRequest, "网络请求超时", nil)
	ErrRateLimitExceeded  = NewRateLimitError(ErrCodeRateLimitExceeded, "请求频率超限", nil)
)

// IsRetryableError 判断错误是否可重试
func IsRetryableError(err error) bool {
	if mailErr, ok := err.(*MailError); ok {
		return mailErr.IsRetryable()
	}
	return false
}

// GetErrorType 获取错误类型
func GetErrorType(err error) ErrorType {
	if mailErr, ok := err.(*MailError); ok {
		return mailErr.GetType()
	}
	return ErrorTypeUnknown
}

// GetErrorCode 获取错误代码
func GetErrorCode(err error) string {
	if mailErr, ok := err.(*MailError); ok {
		return mailErr.GetCode()
	}
	return ""
}
