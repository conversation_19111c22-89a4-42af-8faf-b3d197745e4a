import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ThemeMode, Language } from '@/types/common'

export const useAppStore = defineStore('app', () => {
  // 状态
  const theme = ref<ThemeMode>('light')
  const language = ref<Language>('zh-CN')
  const sidebarCollapsed = ref(false)
  const loading = ref(false)
  const title = ref('Go-Mail管理后台')

  // 计算属性
  const isDark = computed(() => {
    if (theme.value === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return theme.value === 'dark'
  })

  // 操作方法
  const setTheme = (newTheme: ThemeMode) => {
    theme.value = newTheme
    localStorage.setItem('app-theme', newTheme)
  }

  const setLanguage = (newLanguage: Language) => {
    language.value = newLanguage
    localStorage.setItem('app-language', newLanguage)
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebar-collapsed', String(sidebarCollapsed.value))
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebar-collapsed', String(collapsed))
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setTitle = (newTitle: string) => {
    title.value = newTitle
    document.title = newTitle
  }

  // 初始化应用设置
  const initializeApp = () => {
    // 恢复主题设置
    const savedTheme = localStorage.getItem('app-theme') as ThemeMode
    if (savedTheme) {
      theme.value = savedTheme
    }

    // 恢复语言设置
    const savedLanguage = localStorage.getItem('app-language') as Language
    if (savedLanguage) {
      language.value = savedLanguage
    }

    // 恢复侧边栏状态
    const savedSidebarState = localStorage.getItem('sidebar-collapsed')
    if (savedSidebarState !== null) {
      sidebarCollapsed.value = savedSidebarState === 'true'
    }

    // 监听系统主题变化
    if (theme.value === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', () => {
        // 触发响应式更新
        theme.value = 'auto'
      })
    }
  }

  // 重置应用设置
  const resetAppSettings = () => {
    theme.value = 'light'
    language.value = 'zh-CN'
    sidebarCollapsed.value = false
    localStorage.removeItem('app-theme')
    localStorage.removeItem('app-language')
    localStorage.removeItem('sidebar-collapsed')
  }

  return {
    // 状态
    theme,
    language,
    sidebarCollapsed,
    loading,
    title,
    
    // 计算属性
    isDark,
    
    // 方法
    setTheme,
    setLanguage,
    toggleSidebar,
    setSidebarCollapsed,
    setLoading,
    setTitle,
    initializeApp,
    resetAppSettings,
  }
})
