package database

import (
	"time"
)

// AccountRecord 账户数据库记录结构
type AccountRecord struct {
	ID             int       `db:"id" json:"id"`
	Email          string    `db:"email" json:"email"`                     // 邮箱地址
	Password       string    `db:"password" json:"-"`                      // 加密后的密码
	CookieData     string    `db:"cookie_data" json:"-"`                   // JSON格式的cookie信息
	LoginStatus    string    `db:"login_status" json:"login_status"`       // 登录状态：success/failed
	LastLoginTime  *time.Time `db:"last_login_time" json:"last_login_time"` // 最后登录成功时间
	EmailStatus    string    `db:"email_status" json:"email_status"`       // 邮箱状态：valid/invalid
	UsageStatus    string    `db:"usage_status" json:"usage_status"`       // 使用状态：in_use/available
	UsageID        string    `db:"usage_id" json:"usage_id"`               // 使用者ID
	SessionID      string    `db:"session_id" json:"session_id"`           // 当前会话ID
	JSessionID     string    `db:"jsession_id" json:"jsession_id"`         // 邮件服务器会话ID
	NavigatorSID   string    `db:"navigator_sid" json:"navigator_sid"`     // Navigator会话ID
	CreatedAt      time.Time `db:"created_at" json:"created_at"`           // 创建时间
	UpdatedAt      time.Time `db:"updated_at" json:"updated_at"`           // 更新时间
}

// LoginRecord 登录记录结构
type LoginRecord struct {
	ID          int       `db:"id" json:"id"`
	Email       string    `db:"email" json:"email"`
	LoginTime   time.Time `db:"login_time" json:"login_time"`
	LoginStatus string    `db:"login_status" json:"login_status"` // success/failed
	ErrorMsg    string    `db:"error_msg" json:"error_msg"`       // 错误信息
	IPAddress   string    `db:"ip_address" json:"ip_address"`     // 登录IP
	UserAgent   string    `db:"user_agent" json:"user_agent"`     // 用户代理
}

// CookieInfo Cookie信息结构（用于JSON序列化）
type CookieInfo struct {
	Name     string    `json:"name"`
	Value    string    `json:"value"`
	Domain   string    `json:"domain"`
	Path     string    `json:"path"`
	Expires  time.Time `json:"expires"`
	Secure   bool      `json:"secure"`
	HttpOnly bool      `json:"http_only"`
}

// AccountStatus 账户状态常量
const (
	LoginStatusSuccess = "success"
	LoginStatusFailed  = "failed"
	
	EmailStatusValid   = "valid"
	EmailStatusInvalid = "invalid"
	
	UsageStatusInUse     = "in_use"
	UsageStatusAvailable = "available"
)
