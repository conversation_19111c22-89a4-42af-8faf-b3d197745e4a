package handlers

import (
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/logger"
	"go-mail/internal/services/activation"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// ActivationHandler 激活码处理器
type ActivationHandler struct {
	activationService *activation.Service
}

// NewActivationHandler 创建激活码处理器
func NewActivationHandler(activationService *activation.Service) *ActivationHandler {
	return &ActivationHandler{
		activationService: activationService,
	}
}

// GenerateCodes 批量生成激活码
func (h *ActivationHandler) GenerateCodes(c *gin.Context) {
	var req database.ActivationCodeGenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证参数
	if req.Count <= 0 || req.Count > 1000 {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "生成数量必须在1-1000之间",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	if req.ExpiryDays <= 0 || req.ExpiryDays > 365 {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "有效期必须在1-365天之间",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 设置默认前缀
	if req.Prefix == "" {
		req.Prefix = "GOMAIL"
	}

	// 生成激活码
	response, err := h.activationService.GenerateCodes(req.Count, req.ExpiryDays, req.Prefix, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "生成激活码失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "激活码生成成功",
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// VerifyCode 验证激活码（管理后台使用）
func (h *ActivationHandler) VerifyCode(c *gin.Context) {
	var req struct {
		Code       string              `json:"code" binding:"required"`
		DeviceInfo database.DeviceInfo `json:"device_fingerprint" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证激活码
	err := h.activationService.VerifyCode(req.Code, req.DeviceInfo)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "激活码验证失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "激活码验证成功",
		Data: gin.H{
			"valid":        true,
			"device_bound": true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ListCodes 获取激活码列表
func (h *ActivationHandler) ListCodes(c *gin.Context) {
	// 获取日志记录器
	log := logger.GetDefaultLogger().WithContext(c.Request.Context())
	requestID := c.GetString("request_id")

	// 记录请求开始
	log.WithFields(map[string]any{
		"operation":  "ListCodes",
		"request_id": requestID,
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
	}).Info("开始处理激活码列表查询")

	// 获取查询参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "20")
	status := c.Query("status")
	search := c.Query("search")

	// 记录请求参数
	log.WithFields(map[string]any{
		"page_str":   pageStr,
		"size_str":   sizeStr,
		"status":     status,
		"search":     search,
		"request_id": requestID,
	}).Debug("解析请求参数")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		log.WithFields(map[string]any{
			"page_str":   pageStr,
			"error":      err,
			"request_id": requestID,
		}).Warn("页码参数无效，使用默认值")
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		log.WithFields(map[string]any{
			"size_str":   sizeStr,
			"error":      err,
			"request_id": requestID,
		}).Warn("页面大小参数无效，使用默认值")
		size = 20
	}

	// 记录最终参数
	log.WithFields(map[string]any{
		"page":       page,
		"size":       size,
		"status":     status,
		"search":     search,
		"request_id": requestID,
	}).Info("参数解析完成")

	// 查询激活码列表
	start := time.Now()
	codes, total, err := h.activationService.ListCodes(page, size, status)
	duration := time.Since(start)

	if err != nil {
		// 记录详细错误信息
		log.WithFields(map[string]any{
			"operation":  "ListCodes",
			"error":      err.Error(),
			"page":       page,
			"size":       size,
			"status":     status,
			"search":     search,
			"duration":   duration.String(),
			"request_id": requestID,
		}).Error("查询激活码列表失败")

		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "查询激活码列表失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: requestID,
		})
		return
	}

	// 转换数据格式，处理指针类型字段
	convertedCodes := make([]map[string]interface{}, len(codes))
	for i, code := range codes {
		convertedCodes[i] = map[string]interface{}{
			"id":                 code.ID,
			"code":               code.Code,
			"device_fingerprint": stringPtrToString(code.DeviceFingerprint),
			"mac_address":        stringPtrToString(code.MacAddress),
			"status":             code.Status,
			"expires_at":         code.ExpiresAt,
			"used_at":            code.UsedAt,
			"created_at":         code.CreatedAt,
			"description":        stringPtrToString(code.Description),
			"batch_id":           stringPtrToString(code.BatchID),
		}
	}

	// 记录成功结果
	log.WithFields(map[string]any{
		"operation":    "ListCodes",
		"total_count":  total,
		"result_count": len(codes),
		"page":         page,
		"size":         size,
		"status":       status,
		"search":       search,
		"duration":     duration.String(),
		"request_id":   requestID,
	}).Info("激活码列表查询成功")

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "查询成功",
		Data: gin.H{
			"total": total,
			"page":  page,
			"size":  size,
			"items": convertedCodes,
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: requestID,
	})
}

// GetCodeInfo 获取激活码信息
func (h *ActivationHandler) GetCodeInfo(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "激活码不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	codeInfo, err := h.activationService.GetCodeInfo(code)
	if err != nil {
		c.JSON(http.StatusNotFound, database.APIResponse{
			Code:      404,
			Message:   "激活码不存在",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "查询成功",
		Data:      codeInfo,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// DeleteCode 删除激活码
func (h *ActivationHandler) DeleteCode(c *gin.Context) {
	codeIDStr := c.Param("id")
	codeID, err := strconv.Atoi(codeIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的激活码ID",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	err = h.activationService.DeleteCode(codeID)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "删除激活码失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "删除成功",
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ExpireCode 使激活码过期
func (h *ActivationHandler) ExpireCode(c *gin.Context) {
	codeIDStr := c.Param("id")
	codeID, err := strconv.Atoi(codeIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的激活码ID",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	err = h.activationService.ExpireCode(codeID)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "操作失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "激活码已设置为过期",
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// BatchDeleteCodes 批量删除激活码
func (h *ActivationHandler) BatchDeleteCodes(c *gin.Context) {
	// 获取日志记录器
	log := logger.GetDefaultLogger().WithContext(c.Request.Context())
	requestID := c.GetString("request_id")

	// 记录请求开始
	log.WithFields(map[string]any{
		"operation":  "BatchDeleteCodes",
		"request_id": requestID,
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
	}).Info("开始处理批量删除激活码")

	// 解析请求体
	var req struct {
		IDs []string `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		log.WithFields(map[string]any{
			"error":      err.Error(),
			"request_id": requestID,
		}).Error("请求参数解析失败")

		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数格式错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: requestID,
		})
		return
	}

	// 验证参数
	if len(req.IDs) == 0 {
		log.WithFields(map[string]any{
			"request_id": requestID,
		}).Warn("删除列表为空")

		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "删除列表不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: requestID,
		})
		return
	}

	// 转换字符串ID为整数ID
	var codeIDs []int
	for _, idStr := range req.IDs {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			log.WithFields(map[string]any{
				"invalid_id": idStr,
				"error":      err.Error(),
				"request_id": requestID,
			}).Error("ID格式无效")

			c.JSON(http.StatusBadRequest, database.APIResponse{
				Code:      400,
				Message:   "ID格式无效",
				Error:     fmt.Sprintf("无效的ID: %s", idStr),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: requestID,
			})
			return
		}
		codeIDs = append(codeIDs, id)
	}

	// 记录转换后的参数
	log.WithFields(map[string]any{
		"ids_count":  len(codeIDs),
		"ids":        codeIDs,
		"request_id": requestID,
	}).Info("参数解析完成")

	// 执行批量删除
	start := time.Now()
	err := h.activationService.BatchDeleteCodes(codeIDs)
	duration := time.Since(start)

	if err != nil {
		// 记录详细错误信息
		log.WithFields(map[string]any{
			"operation":  "BatchDeleteCodes",
			"error":      err.Error(),
			"ids_count":  len(codeIDs),
			"ids":        codeIDs,
			"duration":   duration.String(),
			"request_id": requestID,
		}).Error("批量删除激活码失败")

		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "批量删除激活码失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: requestID,
		})
		return
	}

	// 记录成功结果
	log.WithFields(map[string]any{
		"operation":  "BatchDeleteCodes",
		"ids_count":  len(codeIDs),
		"ids":        codeIDs,
		"duration":   duration.String(),
		"request_id": requestID,
	}).Info("批量删除激活码成功")

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   fmt.Sprintf("成功删除 %d 个激活码", len(codeIDs)),
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: requestID,
	})
}

// ExportCodes 导出激活码
func (h *ActivationHandler) ExportCodes(c *gin.Context) {
	format := c.DefaultQuery("format", "csv")
	status := c.Query("status")

	// 获取激活码列表
	codes, _, err := h.activationService.ListCodes(1, 10000, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取激活码列表失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 设置响应头
	filename := "activation_codes." + format
	c.Header("Content-Disposition", "attachment; filename="+filename)

	if format == "csv" {
		c.Header("Content-Type", "text/csv")
		// 简单的CSV格式
		csvData := "ID,Code,Status,Created At,Expires At\n"
		for _, code := range codes {
			csvData += fmt.Sprintf("%d,%s,%s,%s,%s\n",
				code.ID, code.Code, code.Status,
				code.CreatedAt.Format("2006-01-02 15:04:05"),
				code.ExpiresAt.Format("2006-01-02 15:04:05"))
		}
		c.String(http.StatusOK, csvData)
	} else {
		c.JSON(http.StatusOK, database.APIResponse{
			Code:      200,
			Message:   "导出成功",
			Data:      codes,
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
	}
}

// GetStats 获取激活码统计信息
func (h *ActivationHandler) GetStats(c *gin.Context) {
	stats, err := h.activationService.GetStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取统计信息失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      stats,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// stringPtrToString 将字符串指针转换为字符串，NULL值转换为空字符串
func stringPtrToString(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}
