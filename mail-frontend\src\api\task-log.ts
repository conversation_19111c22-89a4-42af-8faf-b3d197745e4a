import request from '@/utils/request'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

// 任务日志相关类型定义
export interface TaskLog {
  id: number
  task_id: string
  operation_type: 'import' | 'verify' | 'login'
  email: string
  status: 'success' | 'failed' | 'running' | 'pending'
  start_time: string
  end_time?: string
  duration_ms?: number
  error_message?: string
  batch_id?: string
  detail_log_path?: string
  created_at: string
  updated_at: string
}

// 详细日志步骤接口
export interface TaskLogStep {
  step: string
  timestamp: string
  status: 'success' | 'failed' | 'running'
  details: string
  duration?: number
  error_message?: string
  response_data?: Record<string, any>
}

// 详细日志接口
export interface TaskLogDetail {
  task_id: string
  email: string
  start_time: string
  end_time?: string
  steps: TaskLogStep[]
  final_status: string
  error_details?: string
  metadata?: Record<string, any>
}

// 详细日志响应接口
export interface TaskLogDetailResponse {
  taskLog: TaskLog
  detail: TaskLogDetail
}

// 任务日志查询参数
export interface TaskLogFilterRequest {
  page?: number
  page_size?: number
  start_time?: string
  end_time?: string
  operation_type?: string
  status?: string
  email?: string
  batch_id?: string
}

// 创建任务日志请求
export interface CreateTaskLogRequest {
  task_id: string
  operation_type: string
  email: string
  status: string
  start_time: string
  batch_id?: string
}

// 更新任务日志请求
export interface UpdateTaskLogRequest {
  status?: string
  end_time?: string
  duration_ms?: number
  error_message?: string
  detail_log_path?: string
}

// 任务日志API接口
export const taskLogApi = {
  // 获取任务日志列表
  getTaskLogs(
    params: TaskLogFilterRequest
  ): Promise<ApiResponse<PaginatedResponse<TaskLog>>> {
    return request.get('/mailbox/task-logs', { params })
  },

  // 获取任务详细日志
  getTaskLogDetail(id: number): Promise<ApiResponse<TaskLogDetailResponse>> {
    return request.get(`/mailbox/task-logs/${id}/details`)
  },

  // 创建任务日志
  createTaskLog(data: CreateTaskLogRequest): Promise<ApiResponse<TaskLog>> {
    return request.post('/mailbox/task-logs', data)
  },

  // 更新任务日志
  updateTaskLog(taskId: string, data: UpdateTaskLogRequest): Promise<ApiResponse> {
    return request.put(`/mailbox/task-logs/${taskId}`, data)
  },
}
