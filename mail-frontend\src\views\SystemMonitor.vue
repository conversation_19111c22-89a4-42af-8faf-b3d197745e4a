<template>
  <div class="system-monitor">
    <div class="page-header">
      <h1>系统监控</h1>
      <p>实时监控系统运行状态和性能指标</p>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-grid">
      <n-card title="系统状态" class="status-card">
        <div class="status-item">
          <div class="status-indicator" :class="systemStatus.status"></div>
          <div class="status-info">
            <div class="status-label">{{ systemStatus.label }}</div>
            <div class="status-uptime">运行时间: {{ systemStatus.uptime }}</div>
          </div>
        </div>
      </n-card>

      <n-card title="内存使用" class="status-card">
        <div class="metric-item">
          <n-progress
            type="circle"
            :percentage="systemMetrics.memoryUsage"
            :color="getProgressColor(systemMetrics.memoryUsage)"
          >
            {{ systemMetrics.memoryUsage }}%
          </n-progress>
        </div>
      </n-card>

      <n-card title="CPU使用率" class="status-card">
        <div class="metric-item">
          <template v-if="coreLoading">
            <n-skeleton height="80px" circle />
            <n-skeleton text :repeat="2" />
          </template>
          <template v-else>
            <n-progress
              type="circle"
              :percentage="systemMetrics.cpuUsage"
              :color="getProgressColor(systemMetrics.cpuUsage)"
            >
              {{ systemMetrics.cpuUsage }}%
            </n-progress>
            <div class="metric-detail">
              <div>核心数: {{ systemMetrics.cpuCores }}</div>
            </div>
          </template>
        </div>
      </n-card>

      <n-card title="磁盘使用" class="status-card">
        <div class="metric-item">
          <n-progress
            type="circle"
            :percentage="systemMetrics.diskPercent"
            :color="getProgressColor(systemMetrics.diskPercent)"
          >
            {{ systemMetrics.diskPercent }}%
          </n-progress>
          <div class="metric-detail">
            <div>已用: {{ systemMetrics.diskUsed }}</div>
            <div>总计: {{ systemMetrics.diskTotal }}</div>
          </div>
        </div>
      </n-card>

      <n-card title="数据库" class="status-card">
        <div class="metric-item">
          <div class="database-info">
            <div class="db-size">
              <div class="label">数据库大小</div>
              <div class="value">{{ systemMetrics.databaseSize }}</div>
            </div>
            <div class="work-dir-size">
              <div class="label">工作目录</div>
              <div class="value">{{ systemMetrics.workDirSize }}</div>
            </div>
          </div>
        </div>
      </n-card>

      <n-card title="内存详情" class="status-card">
        <div class="metric-item">
          <div class="memory-details">
            <div class="memory-item">
              <span class="label">应用占用:</span>
              <span class="value">{{ systemMetrics.appMemoryUsage }}</span>
            </div>
            <div class="memory-item">
              <span class="label">系统已用:</span>
              <span class="value">{{ systemMetrics.memoryUsed }}</span>
            </div>
            <div class="memory-item">
              <span class="label">系统总计:</span>
              <span class="value">{{ systemMetrics.memoryTotal }}</span>
            </div>
            <div class="memory-item">
              <span class="label">使用率:</span>
              <span class="value">{{ systemMetrics.memoryPercent }}%</span>
            </div>
          </div>
        </div>
      </n-card>

      <n-card title="数据库状态" class="status-card">
        <div class="metric-item">
          <template v-if="databaseLoading">
            <n-skeleton height="20px" width="60%" />
            <n-skeleton text :repeat="3" />
          </template>
          <template v-else>
            <div class="database-status">
              <div class="status-indicator" :class="{ error: !databaseMetrics.connectionOK }"></div>
              <div class="status-text">
                {{ databaseMetrics.connectionOK ? '连接正常' : '连接异常' }}
              </div>
              <div class="db-metrics">
                <div class="db-metric">
                  <span class="label">大小:</span>
                  <span class="value">{{ databaseMetrics.size }}</span>
                </div>
                <div class="db-metric">
                  <span class="label">查询时间:</span>
                  <span class="value">{{ databaseMetrics.avgQueryTime.toFixed(2) }}ms</span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </n-card>

      <n-card title="表记录统计" class="status-card">
        <div class="metric-item">
          <template v-if="databaseLoading">
            <n-skeleton text :repeat="5" />
          </template>
          <template v-else>
            <div class="table-counts">
              <div
                v-for="(count, table) in databaseMetrics.tableCounts"
                :key="table"
                class="table-count-item"
              >
                <span class="table-name">{{ table }}:</span>
                <span class="table-count">{{ count.toLocaleString() }}</span>
              </div>
            </div>
          </template>
        </div>
      </n-card>

      <n-card title="性能指标" class="status-card">
        <div class="metric-item">
          <div class="performance-metrics">
            <div class="perf-metric">
              <span class="label">活跃会话:</span>
              <span class="value">{{ performanceMetrics.activeSessions }}</span>
            </div>
            <div class="perf-metric">
              <span class="label">响应时间:</span>
              <span class="value">{{ performanceMetrics.avgResponseTime.toFixed(2) }}ms</span>
            </div>
            <div class="perf-metric">
              <span class="label">错误率:</span>
              <span class="value">{{ performanceMetrics.errorRate.toFixed(2) }}%</span>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 定时任务监控 -->
    <n-card title="定时任务状态" class="tasks-card">
      <template #header-extra>
        <n-button @click="refreshTasks">
          <template #icon>
            <n-icon>
              <RefreshIcon />
            </n-icon>
          </template>
          刷新
        </n-button>
      </template>

      <n-data-table
        :columns="taskColumns"
        :data="tasks"
        :loading="tasksLoading"
        :pagination="false"
      />
    </n-card>

    <!-- 系统日志 -->
    <n-card title="系统日志" class="logs-card">
      <template #header-extra>
        <n-space>
          <n-select
            v-model:value="logLevel"
            :options="logLevelOptions"
            placeholder="日志级别"
            style="width: 120px"
            @update:value="fetchLogs"
          />
          <n-button @click="fetchLogs">
            <template #icon>
              <n-icon>
                <RefreshIcon />
              </n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>

      <div class="logs-container">
        <div v-for="log in logs" :key="log.id" class="log-item" :class="log.level">
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>

        <div v-if="logs.length === 0" class="empty-logs">
          <n-empty description="暂无日志记录" />
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, h } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NProgress,
  NDataTable,
  NSelect,
  NSpace,
  NEmpty,
  NTag,
  NSkeleton,
  useMessage,
  type DataTableColumns,
} from 'naive-ui'
import {
  Refresh as RefreshIcon,
  Play as PlayIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
} from '@vicons/ionicons5'
import { systemApi } from '@/api/system'
import type { TaskInfo } from '@/types/api'

const message = useMessage()

// 响应式数据
const coreLoading = ref(false)
const databaseLoading = ref(false)
const tasksLoading = ref(false)
const logsLoading = ref(false)
const logLevel = ref('info')
const tasks = ref<TaskInfo[]>([])
const logs = ref<any[]>([])

// 系统状态
const systemStatus = reactive({
  status: 'healthy',
  label: '运行正常',
  uptime: '0天0小时0分钟',
})

// 系统指标
const systemMetrics = reactive({
  memoryUsage: 65,
  appMemoryUsage: '0 B',
  memoryTotal: '0 B',
  memoryUsed: '0 B',
  memoryPercent: 0,
  cpuUsage: 32,
  cpuCores: 0,
  diskUsage: '0 B',
  diskTotal: '0 B',
  diskUsed: '0 B',
  diskPercent: 0,
  databaseSize: '0 B',
  workDirSize: '0 B',
})

// 数据库指标
const databaseMetrics = reactive({
  size: '0 B',
  tableCounts: {} as Record<string, number>,
  connectionOK: false,
  avgQueryTime: 0,
})

// 性能指标
const performanceMetrics = reactive({
  avgResponseTime: 0,
  totalRequests: 0,
  errorRate: 0,
  activeSessions: 0,
})

// 日志级别选项
const logLevelOptions = [
  { label: '全部', value: '' },
  { label: 'DEBUG', value: 'debug' },
  { label: 'INFO', value: 'info' },
  { label: 'WARN', value: 'warn' },
  { label: 'ERROR', value: 'error' },
]

// 定时器
let statusTimer: NodeJS.Timeout | null = null

// 任务表格列配置
const taskColumns = computed<DataTableColumns<TaskInfo>>(() => [
  {
    title: '任务名称',
    key: 'name',
    width: 200,
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: row => {
      let status: { label: string; type: 'success' | 'default' | 'error' | 'warning' }

      if (!row.enabled) {
        status = { label: '已禁用', type: 'default' }
      } else if (row.is_running) {
        status = { label: '运行中', type: 'success' }
      } else if (row.last_error) {
        status = { label: '错误', type: 'error' }
      } else {
        status = { label: '等待中', type: 'warning' }
      }

      return h(NTag, { type: status.type }, { default: () => status.label })
    },
  },
  {
    title: '上次运行',
    key: 'last_run',
    width: 180,
    render: row => (row.last_run ? formatTime(row.last_run) : '-'),
  },
  {
    title: '下次运行',
    key: 'next_run',
    width: 180,
    render: row => (row.next_run ? formatTime(row.next_run) : '-'),
  },
  {
    title: '运行次数',
    key: 'run_count',
    width: 100,
  },
  {
    title: '错误次数',
    key: 'error_count',
    width: 100,
    render: row => {
      return h(
        'span',
        {
          style: {
            color: row.error_count > 0 ? '#d03050' : '#18a058',
          },
        },
        row.error_count
      )
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: row => {
      return h('div', { class: 'action-buttons' }, [
        h(
          NButton,
          {
            size: 'small',
            type: row.enabled ? 'error' : 'primary',
            quaternary: true,
            onClick: () => toggleTask(row),
          },
          {
            icon: () =>
              h(NIcon, null, {
                default: () => h(row.enabled ? StopIcon : PlayIcon),
              }),
            default: () => (row.enabled ? '禁用' : '启用'),
          }
        ),
        h(
          NButton,
          {
            size: 'small',
            quaternary: true,
            onClick: () => runTask(row),
          },
          {
            icon: () => h(NIcon, null, { default: () => h(SettingsIcon) }),
            default: () => '执行',
          }
        ),
      ])
    },
  },
])

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#18a058'
  if (percentage < 80) return '#f0a020'
  return '#d03050'
}

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

// 获取系统状态（优化版本 - 分批加载）
const fetchSystemStatus = async () => {
  // 首先快速加载核心指标
  await fetchCoreMetrics()

  // 然后异步加载数据库统计
  setTimeout(() => {
    fetchDatabaseMetrics()
  }, 100)
}

// 获取核心系统指标（快速响应）
const fetchCoreMetrics = async () => {
  coreLoading.value = true
  try {
    const response = await systemApi.getSystemStatsWithMode('core')
    if (response.success && response.data) {
      const systemData = response.data.system
      if (systemData) {
        systemMetrics.memoryUsage = systemData.memory_percent || 0
        systemMetrics.appMemoryUsage = systemData.app_memory_usage || '0 B'
        systemMetrics.memoryTotal = systemData.memory_total || '0 B'
        systemMetrics.memoryUsed = systemData.memory_used || '0 B'
        systemMetrics.memoryPercent = systemData.memory_percent || 0
        systemMetrics.cpuUsage = systemData.cpu_usage || 0
        systemMetrics.cpuCores = systemData.cpu_cores || 0
        systemStatus.uptime = systemData.uptime || '0天0小时0分钟'
      }
    }
  } catch (error) {
    console.error('Failed to fetch core metrics:', error)
    message.error('获取核心指标失败')
  } finally {
    coreLoading.value = false
  }
}

// 获取数据库统计信息
const fetchDatabaseMetrics = async () => {
  databaseLoading.value = true
  try {
    const response = await systemApi.getSystemStatsWithMode('database')
    if (response.success && response.data) {
      const databaseData = response.data.database
      if (databaseData) {
        databaseMetrics.size = databaseData.size || '0 B'
        databaseMetrics.tableCounts = databaseData.table_counts || {}
        databaseMetrics.connectionOK = databaseData.connection_ok || false
        databaseMetrics.avgQueryTime = databaseData.avg_query_time_ms || 0
      }
    }
  } catch (error) {
    console.error('Failed to fetch database metrics:', error)
    message.error('获取数据库统计失败')
  } finally {
    databaseLoading.value = false
  }
}

// 获取任务列表
const fetchTasks = async () => {
  try {
    tasksLoading.value = true
    const response = await systemApi.getTasks()
    if (response.success && response.data) {
      tasks.value = response.data
    }
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    message.error('获取任务列表失败')
  } finally {
    tasksLoading.value = false
  }
}

// 获取系统日志
const fetchLogs = async () => {
  try {
    logsLoading.value = true
    const response = await systemApi.getSystemLogs({
      level: logLevel.value || undefined,
      page: 1,
      pageSize: 100,
    })
    if (response.success && response.data) {
      // 兼容大小写字段名
      logs.value = response.data.Items || response.data.items || []
    }
  } catch (error) {
    console.error('Failed to fetch logs:', error)
    message.error('获取系统日志失败')
  } finally {
    logsLoading.value = false
  }
}

// 切换任务状态
const toggleTask = async (task: TaskInfo) => {
  try {
    if (task.enabled) {
      await systemApi.disableTask(task.name)
      message.success(`任务 ${task.name} 已禁用`)
    } else {
      await systemApi.enableTask(task.name)
      message.success(`任务 ${task.name} 已启用`)
    }
    await fetchTasks()
  } catch (error) {
    console.error('Failed to toggle task:', error)
    message.error('操作失败')
  }
}

// 手动执行任务
const runTask = async (task: TaskInfo) => {
  try {
    await systemApi.runTask(task.name)
    message.success(`任务 ${task.name} 已手动执行`)
    await fetchTasks()
  } catch (error) {
    console.error('Failed to run task:', error)
    message.error('执行失败')
  }
}

// 刷新任务列表
const refreshTasks = () => {
  fetchTasks()
}

// 启动定时刷新
const startAutoRefresh = () => {
  statusTimer = setInterval(() => {
    fetchSystemStatus()
  }, 30000) // 每30秒刷新一次
}

// 停止定时刷新
const stopAutoRefresh = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
}

// 组件挂载时初始化
onMounted(() => {
  fetchSystemStatus()
  fetchTasks()
  fetchLogs()
  startAutoRefresh()
})

// 组件卸载时清理
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.system-monitor {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.status-card {
  text-align: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #18a058;
  transition: background-color 0.3s ease;
}

.status-indicator.error {
  background: #d03050;
}

.status-indicator.warning {
  background: #f0a020;
}

.status-indicator.warning {
  background: #f0a020;
}

.status-info {
  flex: 1;
  text-align: left;
}

.status-label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.status-uptime {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.metric-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  gap: 8px;
}

.metric-detail {
  text-align: center;
  font-size: 12px;
  color: var(--n-text-color-2);
  line-height: 1.4;
}

.database-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 0 16px;
}

.db-size,
.work-dir-size {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--n-color-target);
  border-radius: 6px;
}

.database-info .label {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.database-info .value {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.memory-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 0 16px;
}

.memory-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
}

.memory-item .label {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.memory-item .value {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.database-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 0 16px;
}

.status-text {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.db-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.db-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: var(--n-color-target);
  border-radius: 4px;
}

.db-metric .label {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.db-metric .value {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.table-counts {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 0 16px;
  max-height: 120px;
  overflow-y: auto;
}

.table-count-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background: var(--n-color-target);
  border-radius: 4px;
  border-left: 3px solid var(--n-primary-color);
}

.table-name {
  font-size: 12px;
  color: var(--n-text-color-2);
  text-transform: capitalize;
}

.table-count {
  font-weight: 600;
  color: var(--n-text-color-1);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 0 16px;
}

.perf-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background: var(--n-color-target);
  border-radius: 4px;
}

.perf-metric .label {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.perf-metric .value {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.tasks-card,
.logs-card {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  background: var(--n-code-color);
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  border-bottom: 1px solid var(--n-border-color);
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.error {
  background: rgba(208, 48, 80, 0.1);
}

.log-item.warn {
  background: rgba(240, 160, 32, 0.1);
}

.log-time {
  color: var(--n-text-color-3);
  min-width: 120px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
}

.log-level.ERROR {
  color: #d03050;
}

.log-level.WARN {
  color: #f0a020;
}

.log-level.INFO {
  color: #2080f0;
}

.log-level.DEBUG {
  color: #666;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.empty-logs {
  padding: 40px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .status-card {
    min-height: auto;
  }

  .log-item {
    flex-direction: column;
    gap: 4px;
  }

  .log-time,
  .log-level {
    min-width: auto;
  }
}

@media (min-width: 1200px) {
  .status-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1600px) {
  .status-grid {
    grid-template-columns: repeat(5, 1fr);
  }

  .system-monitor {
    max-width: 1400px;
  }
}
</style>
