package client

import (
	"compress/gzip"
	"context"
	"fmt"
	"go-mail/internal/errors"
	"go-mail/internal/types"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
)

// getRedirectToken 获取跳转令牌
func (c *LoginClient) getRedirectToken(ctx context.Context, client *http.Client, ott string) (string, error) {
	// 构建请求URL - 修复：使用abd=true保持一致性
	reqURL := fmt.Sprintf("https://navigator-lxa.mail.com/login?edition=us&usertype=standard&ibaInfo=abd%%3Dtrue&auth_time=%s&uasServiceID=mc_starter_mailcom&lang=en&ott=%s",
		"2025-07-17T07:09:01Z", ott) // 时间戳可以固定，服务器会忽略

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建获取令牌请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "获取令牌请求失败", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("获取令牌请求返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 读取响应内容 - 修复：处理gzip压缩
	var bodyReader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建gzip读取器失败", err)
		}
		defer gzipReader.Close()
		bodyReader = gzipReader
	}

	body, err := io.ReadAll(bodyReader)
	if err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "读取响应内容失败", err)
	}

	// 从响应内容中提取重定向URL
	redirectURL, err := c.extractRedirectURL(string(body))
	if err != nil {
		return "", errors.NewSessionError(errors.ErrCodeSessionInvalid, "提取重定向URL失败", err)
	}

	return redirectURL, nil
}

// extractRedirectURL 从响应内容中提取重定向URL
func (c *LoginClient) extractRedirectURL(body string) (string, error) {
	// 使用正则表达式提取redirectUrl字段
	re := regexp.MustCompile(`"redirectUrl":"([^"]+)"`)
	matches := re.FindStringSubmatch(body)
	if len(matches) < 2 {
		return "", fmt.Errorf("未找到重定向URL，响应体长度: %d", len(body))
	}

	// 处理转义字符
	redirectURL := strings.ReplaceAll(matches[1], "\\/", "/")
	return redirectURL, nil
}

// getSessionID 获取会话ID
func (c *LoginClient) getSessionID(ctx context.Context, client *http.Client, redirectURL string) (string, error) {
	// 构建完整URL - 修复：使用abd=true保持一致性
	fullURL := fmt.Sprintf("https://navigator-lxa.mail.com%s&tz=8&ibaInfo=abd%%3Dtrue", redirectURL)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建获取会话ID请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)
	req.Header.Set("Referer", fmt.Sprintf("https://navigator-lxa.mail.com%s", redirectURL))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "获取会话ID请求失败", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusFound && resp.StatusCode != http.StatusSeeOther {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("获取会话ID请求返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 从Location头获取重定向URL并提取SID
	location := resp.Header.Get("Location")
	if location == "" {
		return "", errors.NewSessionError(errors.ErrCodeSessionInvalid, "未获取到重定向URL", nil)
	}

	// 提取SID参数
	sid, err := c.extractSIDFromURL(location)
	if err != nil {
		return "", errors.NewSessionError(errors.ErrCodeSessionInvalid, "提取SID失败", err)
	}

	return sid, nil
}

// extractSIDFromURL 从URL中提取SID参数
func (c *LoginClient) extractSIDFromURL(urlStr string) (string, error) {
	// 处理相对URL
	if strings.HasPrefix(urlStr, "/") {
		urlStr = "https://navigator-lxa.mail.com" + urlStr
	}

	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("解析URL失败: %w", err)
	}

	sid := parsedURL.Query().Get("sid")
	if sid == "" {
		return "", fmt.Errorf("URL中未找到SID参数")
	}

	return sid, nil
}

// getFinalSession 获取最终会话
func (c *LoginClient) getFinalSession(ctx context.Context, client *http.Client, sid string) (string, string, string, error) {
	// 构建请求URL
	reqURL := fmt.Sprintf("https://navigator-lxa.mail.com/?sid=%s", sid)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return "", "", "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建获取最终会话请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", "", "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "获取最终会话请求失败", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", "", "", errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("获取最终会话请求返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 读取响应内容 - 修复：处理gzip压缩
	var bodyReader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return "", "", "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建gzip读取器失败", err)
		}
		defer gzipReader.Close()
		bodyReader = gzipReader
	}

	body, err := io.ReadAll(bodyReader)
	if err != nil {
		return "", "", "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "读取响应内容失败", err)
	}

	// 从响应内容中提取JSESSIONID、Navigator SID和最终URL
	jsessionID, navigatorSID, finalURL, err := c.extractJSessionID(string(body))
	if err != nil {
		return "", "", "", errors.NewSessionError(errors.ErrCodeSessionInvalid, "提取JSESSIONID失败", err)
	}

	// 修复：返回JSESSIONID、Navigator SID和最终URL
	return jsessionID, navigatorSID, finalURL, nil
}

// extractJSessionID 从响应内容中提取JSESSIONID和Navigator SID
func (c *LoginClient) extractJSessionID(body string) (string, string, string, error) {
	// 修复：增强URL提取逻辑，只针对特定的URL格式进行匹配
	// 1. 首先从响应体中提取 "default" 字段中包含完整会话信息的URL
	// 只匹配包含 3c-lxa.mail.com/mail/client/start 的URL，这些URL通常包含完整的会话信息
	urlRe := regexp.MustCompile(`"default":"(https:\\?/\\?/3c-lxa\.mail\.com\\?/mail\\?/client\\?/start[^"]+)"`)
	urlMatches := urlRe.FindStringSubmatch(body)

	if len(urlMatches) < 2 {
		// 如果没有找到start URL，尝试匹配home URL作为备选
		homeUrlRe := regexp.MustCompile(`"default":"(https:\\?/\\?/3c-lxa\.mail\.com\\?/mail\\?/client\\?/home[^"]+)"`)
		urlMatches = homeUrlRe.FindStringSubmatch(body)

		if len(urlMatches) < 2 {
			// 提供更详细的错误信息
			return "", "", "", fmt.Errorf("未找到包含会话信息的URL，响应体可能不包含完整的default字段或URL格式不匹配")
		}
	}

	fmt.Printf("extractJSessionID urlMatches: %v\n", urlMatches)

	// 处理转义字符，获得完整的URL
	finalURL := strings.ReplaceAll(urlMatches[1], "\\/", "/")
	fmt.Printf("extractJSessionID finalURL: %s\n", finalURL)

	// 2. 然后从这个URL中解析出 jsessionid 参数值（位于分号和问号之间）
	jsessionID, err := c.extractJSessionIDFromURL(finalURL)
	if err != nil {
		return "", "", finalURL, fmt.Errorf("从URL提取JSESSIONID失败，URL可能不包含jsessionid参数: %w (URL: %s)", err, finalURL)
	}

	// 3. 最后从同一个URL中提取 navsid 查询参数和fragment部分
	navigatorSID, err := c.extractNavigatorSID(finalURL)
	if err != nil {
		return jsessionID, "", finalURL, fmt.Errorf("提取Navigator SID失败，URL可能不包含navsid参数或fragment: %w (URL: %s)", err, finalURL)
	}
	// finalURL 替换 start; 改成 folder;
	finalURL = strings.Replace(finalURL, "start", "folder", 1)
	return jsessionID, navigatorSID, finalURL, nil
}

// extractJSessionIDFromURL 从URL中提取JSESSIONID（位于分号和问号之间）
func (c *LoginClient) extractJSessionIDFromURL(urlStr string) (string, error) {
	// 使用正则表达式匹配 ;jsessionid=值 的模式
	// 匹配分号后的jsessionid，直到遇到问号、井号或字符串结束
	re := regexp.MustCompile(`;jsessionid=([^?&#]+)`)
	matches := re.FindStringSubmatch(urlStr)
	if len(matches) < 2 {
		return "", fmt.Errorf("URL中未找到jsessionid参数")
	}

	return matches[1], nil
}

// extractNavigatorSID 从URL中提取Navigator SID（包含fragment部分）
func (c *LoginClient) extractNavigatorSID(urlStr string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("解析URL失败: %w", err)
	}

	// 提取navsid参数
	navsid := parsedURL.Query().Get("navsid")
	if navsid == "" {
		return "", fmt.Errorf("URL中未找到navsid参数")
	}

	// 获取fragment部分
	fragment := parsedURL.Fragment
	if fragment != "" {
		// 组合navsid和fragment
		return navsid + "#" + fragment, nil
	}

	// 如果没有fragment，只返回navsid
	return navsid, nil
}

// Logout 执行登出操作
func (c *LoginClient) Logout(ctx context.Context, session *types.Session) error {
	// 实现登出逻辑
	return nil
}

// ValidateSession 验证会话有效性
func (c *LoginClient) ValidateSession(ctx context.Context, session *types.Session) error {
	// 实现会话验证逻辑
	return nil
}

// RefreshSession 刷新会话
func (c *LoginClient) RefreshSession(ctx context.Context, session *types.Session) error {
	// 实现会话刷新逻辑
	return nil
}
