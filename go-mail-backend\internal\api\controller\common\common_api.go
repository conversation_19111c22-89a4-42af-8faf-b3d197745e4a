package common

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// CommonAPI 公共API控制器
type CommonAPI struct{}

// NewCommonAPI 创建公共API控制器
func NewCommonAPI() *CommonAPI {
	return &CommonAPI{}
}

// RegisterRoutes 注册公共路由
func (c *CommonAPI) RegisterRoutes(engine *gin.Engine) {
	// 健康检查路由
	c.registerHealthRoutes(engine)
	
	// 静态文件服务路由
	c.registerStaticRoutes(engine)
}

// registerHealthRoutes 注册健康检查路由
func (c *CommonAPI) registerHealthRoutes(engine *gin.Engine) {
	engine.GET("/health", func(ctx *gin.Context) {
		ctx.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Format(time.RFC3339),
			"version":   "1.0.0",
		})
	})
}

// registerStaticRoutes 注册静态文件服务路由
func (c *CommonAPI) registerStaticRoutes(engine *gin.Engine) {
	// 静态文件服务（管理后台前端）
	engine.Static("/static", "./web/static")
	engine.StaticFile("/", "./web/index.html")
	engine.StaticFile("/favicon.ico", "./web/favicon.ico")
}
