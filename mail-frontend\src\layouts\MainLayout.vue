<template>
  <n-layout has-sider class="main-layout">
    <!-- 侧边栏 -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed-width="64"
      :width="240"
      :collapsed="appStore.sidebarCollapsed"
      show-trigger
      @collapse="appStore.setSidebarCollapsed(true)"
      @expand="appStore.setSidebarCollapsed(false)"
    >
      <div class="sidebar-header">
        <div class="logo">
          <n-icon size="32" color="#18a058">
            <MailIcon />
          </n-icon>
          <span v-show="!appStore.sidebarCollapsed" class="logo-text">Go-Mail</span>
        </div>
      </div>

      <n-menu
        :collapsed="appStore.sidebarCollapsed"
        :collapsed-width="64"
        :collapsed-icon-size="22"
        :options="menuOptions"
        :value="activeMenuKey"
        @update:value="handleMenuSelect"
      />
    </n-layout-sider>

    <!-- 主内容区 -->
    <n-layout>
      <!-- 顶部导航栏 -->
      <n-layout-header bordered class="header">
        <div class="header-left">
          <n-button quaternary @click="appStore.toggleSidebar" class="sidebar-toggle">
            <template #icon>
              <n-icon>
                <MenuIcon />
              </n-icon>
            </template>
          </n-button>

          <n-breadcrumb class="breadcrumb">
            <n-breadcrumb-item
              v-for="item in breadcrumbItems"
              :key="item.path"
              :clickable="!!item.path"
              @click="item.path && $router.push(item.path)"
            >
              {{ item.title }}
            </n-breadcrumb-item>
          </n-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 主题切换 -->
          <n-button quaternary @click="toggleTheme" class="theme-toggle">
            <template #icon>
              <n-icon>
                <SunnyIcon v-if="appStore.isDark" />
                <MoonIcon v-else />
              </n-icon>
            </template>
          </n-button>

          <!-- 用户菜单 -->
          <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
            <n-button quaternary class="user-menu">
              <template #icon>
                <n-icon>
                  <PersonIcon />
                </n-icon>
              </template>
              <span class="username">{{ authStore.user?.username }}</span>
            </n-button>
          </n-dropdown>
        </div>
      </n-layout-header>

      <!-- 内容区域 -->
      <n-layout-content class="content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NLayout,
  NLayoutSider,
  NLayoutHeader,
  NLayoutContent,
  NMenu,
  NBreadcrumb,
  NBreadcrumbItem,
  NButton,
  NIcon,
  NDropdown,
  type MenuOption,
} from 'naive-ui'
import {
  Mail as MailIcon,
  Menu as MenuIcon,
  Sunny as SunnyIcon,
  Moon as MoonIcon,
  Person as PersonIcon,
  SpeedometerOutline as DashboardIcon,
  Key as KeyIcon,
  DesktopOutline as MonitorIcon,
  Settings as SettingsIcon,
  LogOut as LogOutIcon,
  Person as ProfileIcon,
  MailOutline as MailboxIcon,
} from '@vicons/ionicons5'
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const authStore = useAuthStore()

// 菜单选项
const menuOptions = computed<MenuOption[]>(() => [
  {
    label: '系统概览',
    key: '/dashboard',
    icon: () => h(NIcon, null, { default: () => h(DashboardIcon) }),
  },
  {
    label: '激活管理',
    key: '/activation-codes',
    icon: () => h(NIcon, null, { default: () => h(KeyIcon) }),
  },
  {
    label: '邮箱管理',
    key: '/mailbox-management',
    icon: () => h(NIcon, null, { default: () => h(MailboxIcon) }),
  },
  {
    label: '系统监控',
    key: '/system-monitor',
    icon: () => h(NIcon, null, { default: () => h(MonitorIcon) }),
  },
  {
    label: '系统配置',
    key: '/system-config',
    icon: () => h(NIcon, null, { default: () => h(SettingsIcon) }),
  },
])

// 当前激活的菜单项
const activeMenuKey = computed(() => route.path)

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = [{ title: '首页', path: '/' }]

  if (route.meta.title) {
    items.push({ title: route.meta.title as string, path: route.path })
  }

  return items
})

// 用户菜单选项
const userMenuOptions = computed(() => [
  {
    label: '个人设置',
    key: 'profile',
    icon: () => h(NIcon, null, { default: () => h(ProfileIcon) }),
  },
  {
    type: 'divider',
    key: 'divider',
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: () => h(NIcon, null, { default: () => h(LogOutIcon) }),
  },
])

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  router.push(key)
}

// 处理用户菜单选择
const handleUserMenuSelect = (key: string) => {
  switch (key) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      authStore.logout()
      break
  }
}

// 切换主题
const toggleTheme = () => {
  appStore.setTheme(appStore.isDark ? 'light' : 'dark')
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--n-border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color);
}

.logo-text {
  transition: opacity 0.3s ease;
}

.header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-toggle {
  display: none;
}

.breadcrumb {
  margin-left: 8px;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-size: 14px;
}

.content {
  padding: 24px;
  overflow: auto;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-toggle {
    display: flex;
  }

  .breadcrumb {
    display: none;
  }

  .username {
    display: none;
  }

  .content {
    padding: 16px;
  }
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
