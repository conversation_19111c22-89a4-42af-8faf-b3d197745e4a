package handlers

import (
	"go-mail/internal/database"
	"go-mail/internal/services/activation"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// ClientActivationHandler 客户端激活码处理器
type ClientActivationHandler struct {
	activationService *activation.Service
}

// NewClientActivationHandler 创建客户端激活码处理器
func NewClientActivationHandler(activationService *activation.Service) *ClientActivationHandler {
	return &ClientActivationHandler{
		activationService: activationService,
	}
}

// VerifyActivationCode 验证激活码（客户端使用）
func (h *ClientActivationHandler) VerifyActivationCode(c *gin.Context) {
	// 从中间件获取已验证的信息
	activationCode := c.GetString("activation_code")
	deviceInfo, exists := c.Get("device_info")
	if !exists {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      4002,
			Message:   "设备信息缺失",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	macAddress := c.GetString("mac_address")

	// 类型断言
	deviceInfoStruct, ok := deviceInfo.(database.DeviceInfo)
	if !ok {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      4001,
			Message:   "设备信息格式错误",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证激活码（这里实际上在中间件中已经验证过了，这里主要是返回验证结果）
	err := h.activationService.ValidateActivationCode(activationCode, deviceInfoStruct, macAddress)
	if err != nil {
		var code int
		var message string
		
		switch err.Error() {
		case "activation_code_not_found":
			code = 1001
			message = "激活码不存在"
		case "activation_code_expired":
			code = 1003
			message = "激活码已过期"
		case "device_fingerprint_mismatch":
			code = 1004
			message = "设备指纹不匹配"
		case "mac_address_mismatch":
			code = 1005
			message = "MAC地址不匹配"
		default:
			code = 1001
			message = "激活码验证失败"
		}

		c.JSON(http.StatusUnauthorized, database.APIResponse{
			Code:      code,
			Message:   message,
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 获取激活码详细信息
	codeInfo, err := h.activationService.GetCodeInfo(activationCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取激活码信息失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "激活码验证成功",
		Data: gin.H{
			"valid":         true,
			"expires_at":    codeInfo.ExpiresAt.Format(time.RFC3339),
			"permissions":   []string{"basic_mailbox"},
			"device_matched": true,
			"code_info": gin.H{
				"id":          codeInfo.ID,
				"code":        codeInfo.Code,
				"status":      codeInfo.Status,
				"created_at":  codeInfo.CreatedAt.Format(time.RFC3339),
				"expires_at":  codeInfo.ExpiresAt.Format(time.RFC3339),
				"description": codeInfo.Description,
				"batch_id":    codeInfo.BatchID,
			},
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}
