package handlers

import (
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/scheduler"
	"net/http"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
)

// SystemHandler 系统管理处理器
type SystemHandler struct {
	database  *database.Database
	scheduler *scheduler.Scheduler
}

// NewSystemHandler 创建系统管理处理器
func NewSystemHandler(database *database.Database, scheduler *scheduler.Scheduler) *SystemHandler {
	return &SystemHandler{
		database:  database,
		scheduler: scheduler,
	}
}

// CleanupSystem 系统清理
func (h *SystemHandler) CleanupSystem(c *gin.Context) {
	var req struct {
		CleanExpiredMailboxes bool `json:"cleanExpiredMailboxes"`
		CleanOldLogs          bool `json:"cleanOldLogs"`
		CleanTempFiles        bool `json:"cleanTempFiles"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	var cleanupResults []string
	var totalCleaned int64

	// 清理过期邮箱
	if req.CleanExpiredMailboxes {
		result, err := h.database.GetDB().Exec(`
			UPDATE temp_mailboxes 
			SET status = 'released', released_at = ? 
			WHERE status = 'allocated' 
			AND expires_at < ?`,
			time.Now(), time.Now())

		if err != nil {
			c.JSON(http.StatusInternalServerError, database.APIResponse{
				Code:      500,
				Message:   "清理过期邮箱失败",
				Error:     err.Error(),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: c.GetString("request_id"),
			})
			return
		}

		mailboxesCleaned, _ := result.RowsAffected()
		totalCleaned += mailboxesCleaned
		cleanupResults = append(cleanupResults, fmt.Sprintf("清理过期邮箱: %d个", mailboxesCleaned))
	}

	// 清理旧日志
	if req.CleanOldLogs {
		cutoffTime := time.Now().AddDate(0, 0, -7)
		result, err := h.database.GetDB().Exec(`
			DELETE FROM mail_records 
			WHERE created_at < ? 
			AND mailbox_id IN (
				SELECT id FROM temp_mailboxes 
				WHERE status = 'released'
			)`,
			cutoffTime)

		if err != nil {
			c.JSON(http.StatusInternalServerError, database.APIResponse{
				Code:      500,
				Message:   "清理旧日志失败",
				Error:     err.Error(),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: c.GetString("request_id"),
			})
			return
		}

		logsCleaned, _ := result.RowsAffected()
		totalCleaned += logsCleaned
		cleanupResults = append(cleanupResults, fmt.Sprintf("清理旧日志: %d条", logsCleaned))
	}

	// 清理临时文件（这里只是示例，实际可能需要清理文件系统）
	if req.CleanTempFiles {
		// 清理过期的激活码
		expiredCutoff := time.Now().AddDate(0, 0, -30)
		result, err := h.database.GetDB().Exec(`
			DELETE FROM activation_codes 
			WHERE status = 'expired' 
			AND expires_at < ?`,
			expiredCutoff)

		if err != nil {
			c.JSON(http.StatusInternalServerError, database.APIResponse{
				Code:      500,
				Message:   "清理临时文件失败",
				Error:     err.Error(),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: c.GetString("request_id"),
			})
			return
		}

		tempFilesCleaned, _ := result.RowsAffected()
		totalCleaned += tempFilesCleaned
		cleanupResults = append(cleanupResults, fmt.Sprintf("清理临时文件: %d个", tempFilesCleaned))
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "系统清理完成",
		Data: gin.H{
			"results":       cleanupResults,
			"total_cleaned": totalCleaned,
			"timestamp":     time.Now().Format(time.RFC3339),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// BackupSystem 系统备份
func (h *SystemHandler) BackupSystem(c *gin.Context) {
	// 生成备份ID
	backupId := "backup_" + time.Now().Format("20060102_150405")

	// 这里应该实现实际的备份逻辑
	// 目前只是返回一个模拟的响应

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "备份创建成功",
		Data: gin.H{
			"backupId":    backupId,
			"downloadUrl": "/api/v1/system/backup/download/" + backupId,
			"size":        "1.2MB",
			"timestamp":   time.Now().Format(time.RFC3339),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetVersionInfo 获取版本信息
func (h *SystemHandler) GetVersionInfo(c *gin.Context) {
	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "获取成功",
		Data: gin.H{
			"version":   "1.0.0",
			"buildTime": "2025-01-20T10:00:00Z",
			"gitCommit": "abc123def456",
			"goVersion": runtime.Version(),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}
