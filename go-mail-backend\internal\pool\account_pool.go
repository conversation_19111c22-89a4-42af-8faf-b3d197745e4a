package pool

import (
	"fmt"
	"go-mail/internal/errors"
	"go-mail/internal/types"
	"sync"
	"time"
)

// AccountPool 账户池实现
type AccountPool struct {
	accounts map[string]*types.Account
	mutex    sync.RWMutex
}

// NewAccountPool 创建新的账户池
func NewAccountPool() *AccountPool {
	return &AccountPool{
		accounts: make(map[string]*types.Account),
	}
}

// Add 添加账户
func (p *AccountPool) Add(account types.Account) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 验证账户信息
	if err := p.validateAccount(account); err != nil {
		return err
	}

	// 检查账户是否已存在
	if _, exists := p.accounts[account.Username]; exists {
		return errors.NewValidationError(errors.ErrCodeValidationConflict, 
			fmt.Sprintf("账户 %s 已存在", account.Username), nil)
	}

	// 设置默认值
	if account.Status == types.AccountStatusUnknown {
		account.Status = types.AccountStatusInactive
	}
	if account.Metadata == nil {
		account.Metadata = make(map[string]string)
	}

	// 添加账户
	p.accounts[account.Username] = &account

	return nil
}

// Remove 移除账户
func (p *AccountPool) Remove(username string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if _, exists := p.accounts[username]; !exists {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("账户 %s 不存在", username), nil)
	}

	delete(p.accounts, username)
	return nil
}

// Get 获取账户
func (p *AccountPool) Get(username string) (*types.Account, error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	account, exists := p.accounts[username]
	if !exists {
		return nil, errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("账户 %s 不存在", username), nil)
	}

	// 返回副本以避免外部修改
	accountCopy := *account
	return &accountCopy, nil
}

// GetAvailable 获取所有可用账户
func (p *AccountPool) GetAvailable() []types.Account {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	var available []types.Account
	for _, account := range p.accounts {
		if account.Status == types.AccountStatusActive || account.Status == types.AccountStatusInactive {
			available = append(available, *account)
		}
	}

	return available
}

// GetByStatus 根据状态获取账户
func (p *AccountPool) GetByStatus(status types.AccountStatus) []types.Account {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	var accounts []types.Account
	for _, account := range p.accounts {
		if account.Status == status {
			accounts = append(accounts, *account)
		}
	}

	return accounts
}

// UpdateStatus 更新账户状态
func (p *AccountPool) UpdateStatus(username string, status types.AccountStatus) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	account, exists := p.accounts[username]
	if !exists {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("账户 %s 不存在", username), nil)
	}

	account.Status = status
	return nil
}

// Count 获取账户总数
func (p *AccountPool) Count() int {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	return len(p.accounts)
}

// Clear 清空账户池
func (p *AccountPool) Clear() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.accounts = make(map[string]*types.Account)
	return nil
}

// UpdateLoginInfo 更新登录信息
func (p *AccountPool) UpdateLoginInfo(username string, success bool) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	account, exists := p.accounts[username]
	if !exists {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("账户 %s 不存在", username), nil)
	}

	if success {
		account.LastLogin = time.Now()
		account.LoginCount++
		account.Status = types.AccountStatusActive
	} else {
		// 登录失败，可能需要更新状态
		if account.Status == types.AccountStatusActive {
			account.Status = types.AccountStatusInactive
		}
	}

	return nil
}

// GetAccountInfo 获取账户信息摘要
func (p *AccountPool) GetAccountInfo() []types.AccountInfo {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	var infos []types.AccountInfo
	for _, account := range p.accounts {
		info := types.AccountInfo{
			Username:   account.Username,
			Status:     account.Status,
			LastLogin:  account.LastLogin,
			LoginCount: account.LoginCount,
		}
		infos = append(infos, info)
	}

	return infos
}

// validateAccount 验证账户信息
func (p *AccountPool) validateAccount(account types.Account) error {
	if account.Username == "" {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, "用户名不能为空", nil)
	}

	if account.Password == "" {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, "密码不能为空", nil)
	}

	// 验证邮箱格式
	if !p.isValidEmail(account.Username) {
		return errors.NewValidationError(errors.ErrCodeValidationFormat, "用户名必须是有效的邮箱地址", nil)
	}

	return nil
}

// isValidEmail 验证邮箱格式
func (p *AccountPool) isValidEmail(email string) bool {
	// 简单的邮箱格式验证
	if len(email) < 3 {
		return false
	}

	atIndex := -1
	for i, char := range email {
		if char == '@' {
			if atIndex != -1 {
				return false // 多个@符号
			}
			atIndex = i
		}
	}

	if atIndex <= 0 || atIndex >= len(email)-1 {
		return false // @符号位置不正确
	}

	// 检查域名部分是否包含点号
	domain := email[atIndex+1:]
	dotIndex := -1
	for i, char := range domain {
		if char == '.' {
			dotIndex = i
			break
		}
	}

	return dotIndex > 0 && dotIndex < len(domain)-1
}

// GetStatistics 获取账户池统计信息
func (p *AccountPool) GetStatistics() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	stats := make(map[string]interface{})
	statusCount := make(map[string]int)

	totalLogins := 0
	for _, account := range p.accounts {
		statusCount[account.Status.String()]++
		totalLogins += account.LoginCount
	}

	stats["total_accounts"] = len(p.accounts)
	stats["status_distribution"] = statusCount
	stats["total_logins"] = totalLogins

	return stats
}
