package handlers

import (
	"go-mail/internal/database"
	"go-mail/internal/services/config"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// ConfigHandler 配置处理器
type ConfigHandler struct {
	configService *config.Service
}

// NewConfigHandler 创建配置处理器
func NewConfigHandler(configService *config.Service) *ConfigHandler {
	return &ConfigHandler{
		configService: configService,
	}
}

// GetConfig 获取系统配置
func (h *ConfigHandler) GetConfig(c *gin.Context) {
	systemConfig, err := h.configService.GetConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取系统配置失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      systemConfig,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// UpdateConfig 更新系统配置
func (h *ConfigHandler) UpdateConfig(c *gin.Context) {
	var systemConfig config.SystemConfig
	if err := c.ShouldBindJSON(&systemConfig); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证配置
	if err := h.configService.ValidateConfig(&systemConfig); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "配置验证失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 更新配置
	if err := h.configService.UpdateConfig(&systemConfig); err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "更新配置失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "配置更新成功",
		Data:      systemConfig,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetConfigItem 获取单个配置项
func (h *ConfigHandler) GetConfigItem(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "配置键不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	configItem, err := h.configService.GetConfigItem(key)
	if err != nil {
		c.JSON(http.StatusNotFound, database.APIResponse{
			Code:      404,
			Message:   "配置项不存在",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      configItem,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// SetConfigItem 设置单个配置项
func (h *ConfigHandler) SetConfigItem(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "配置键不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	var req struct {
		Value    string `json:"value" binding:"required"`
		Type     string `json:"type"`
		Category string `json:"category"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 设置默认值
	if req.Type == "" {
		req.Type = database.ConfigTypeString
	}
	if req.Category == "" {
		req.Category = database.ConfigCategorySystem
	}

	// 设置配置项
	err := h.configService.SetConfigItem(key, req.Value, req.Type, req.Category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "设置配置项失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "设置成功",
		Data: gin.H{
			"key":      key,
			"value":    req.Value,
			"type":     req.Type,
			"category": req.Category,
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// DeleteConfigItem 删除配置项
func (h *ConfigHandler) DeleteConfigItem(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "配置键不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	err := h.configService.DeleteConfigItem(key)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "删除配置项失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "删除成功",
		Data: gin.H{
			"key": key,
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ListConfigItems 获取配置项列表
func (h *ConfigHandler) ListConfigItems(c *gin.Context) {
	category := c.Query("category")

	configItems, err := h.configService.ListConfigItems(category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取配置项列表失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data: gin.H{
			"items":    configItems,
			"total":    len(configItems),
			"category": category,
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ResetToDefaults 重置为默认配置
func (h *ConfigHandler) ResetToDefaults(c *gin.Context) {
	// 确认操作
	var req struct {
		Confirm bool `json:"confirm" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	if !req.Confirm {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请确认重置操作",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 重置配置
	err := h.configService.ResetToDefaults()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "重置配置失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "配置已重置为默认值",
		Data: gin.H{
			"reset_at": time.Now().Format(time.RFC3339),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ExportConfig 导出配置
func (h *ConfigHandler) ExportConfig(c *gin.Context) {
	category := c.Query("category")

	configItems, err := h.configService.ListConfigItems(category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "导出配置失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 设置响应头为文件下载
	filename := "config_export.json"
	if category != "" {
		filename = "config_" + category + "_export.json"
	}

	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/json")

	c.JSON(http.StatusOK, gin.H{
		"export_time": time.Now().Format(time.RFC3339),
		"category":    category,
		"items":       configItems,
		"total":       len(configItems),
	})
}
