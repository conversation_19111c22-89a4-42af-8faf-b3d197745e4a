# 邮箱管理 API 接口文档

## 概述

邮箱管理模块提供了完整的邮箱账户批量操作和监控功能，包括批量导入、验证任务、状态筛选和定时任务控制等功能。

## 基础信息

- **Base URL**: `/api/v1/mailbox`
- **认证方式**: JWT Bearer <PERSON>ken
- **内容类型**: `application/json`

## 接口列表

### 1. 批量导入邮箱账户

**接口地址**: `POST /batch-import`

**功能描述**: 批量导入邮箱账户，支持自动验证和标签管理

**请求参数**:
```json
{
  "accounts": [
    {
      "email": "<EMAIL>",
      "password": "password123"
    },
    {
      "email": "<EMAIL>", 
      "password": "password456"
    }
  ],
  "source": "批量导入",
  "tags": ["测试", "客户提供"],
  "auto_verify": true,
  "description": "测试批量导入功能"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "批量导入任务已创建，正在后台执行",
  "data": {
    "operation_id": "batch_import_1234567890",
    "status": "pending",
    "message": "批量导入任务已创建",
    "total_count": 2
  }
}
```

**频率限制**: 每小时最多3次
**并发限制**: 最多3个并发操作
**请求大小限制**: 最大10MB

---

### 2. 获取账户列表

**接口地址**: `GET /accounts`

**功能描述**: 获取邮箱账户列表，支持多种筛选条件和分页

**查询参数**:
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20，最大100）
- `status[]`: 登录状态筛选（success, failed, pending）
- `verification_status[]`: 验证状态筛选（verified, unverified, failed）
- `import_source[]`: 导入来源筛选（manual, batch_import, api）
- `tags[]`: 标签筛选
- `sort_by`: 排序字段（默认created_at）
- `sort_order`: 排序方向（asc, desc，默认desc）

**请求示例**:
```
GET /accounts?page=1&page_size=20&status[]=success&verification_status[]=verified
```

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "login_status": "success",
        "verification_status": "verified",
        "import_source": "batch_import",
        "tags": ["测试"],
        "created_at": "2025-01-20T10:00:00Z",
        "last_verification_time": "2025-01-20T11:00:00Z",
        "is_disabled": false
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

---

### 3. 启动验证任务

**接口地址**: `POST /verify`

**功能描述**: 对指定邮箱账户启动批量验证任务

**请求参数**:
```json
{
  "account_emails": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "verification_type": "login",
  "concurrent_limit": 10,
  "retry_limit": 3
}
```

**参数说明**:
- `verification_type`: 验证类型（login, health_check, session_test）
- `concurrent_limit`: 并发限制（1-50，默认10）
- `retry_limit`: 重试次数（0-10，默认3）

**响应示例**:
```json
{
  "success": true,
  "message": "验证任务已创建，正在后台执行",
  "data": {
    "operation_id": "verify_1234567890",
    "status": "pending",
    "total_count": 2
  }
}
```

**频率限制**: 每10分钟最多10次
**并发限制**: 最多5个并发验证任务

---

### 4. 获取批量操作状态

**接口地址**: `GET /batch-operation/{operation_id}`

**功能描述**: 查询指定批量操作的执行状态和进度

**路径参数**:
- `operation_id`: 操作ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "id": 1,
    "operation_id": "batch_import_1234567890",
    "operation_type": "import",
    "status": "completed",
    "total_count": 100,
    "processed_count": 100,
    "success_count": 95,
    "failed_count": 5,
    "created_by": "admin",
    "created_at": "2025-01-20T10:00:00Z",
    "started_at": "2025-01-20T10:00:05Z",
    "completed_at": "2025-01-20T10:05:30Z",
    "progress_data": {
      "current_batch": 10,
      "total_batches": 10
    }
  }
}
```

**状态说明**:
- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 已完成
- `failed`: 执行失败
- `cancelled`: 已取消

---

### 5. 任务控制

**接口地址**: `POST /task-control`

**功能描述**: 控制定时任务的启动、暂停、停止、重置等操作

**请求参数**:
```json
{
  "action": "start",
  "task_id": "batch_mailbox_verification"
}
```

**参数说明**:
- `action`: 操作类型（start, pause, stop, reset）
- `task_id`: 任务ID

**响应示例**:
```json
{
  "success": true,
  "message": "任务控制成功"
}
```

**频率限制**: 每小时最多5次

---

### 6. 获取任务调度器状态

**接口地址**: `GET /scheduler-status`

**功能描述**: 获取所有定时任务的状态信息

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "task_name": "batch_mailbox_verification",
      "task_type": "batch_verification",
      "status": "running",
      "last_run_at": "2025-01-20T10:00:00Z",
      "next_run_at": "2025-01-20T11:00:00Z",
      "run_count": 24,
      "error_count": 0,
      "config_data": {
        "concurrent_limit": 10,
        "retry_limit": 3
      }
    }
  ]
}
```

---

### 7. 获取邮箱统计信息

**接口地址**: `GET /statistics`

**功能描述**: 获取邮箱账户的统计信息

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "total_accounts": 1000,
    "active_accounts": 850,
    "verified_accounts": 800,
    "failed_accounts": 150,
    "disabled_accounts": 50,
    "new_imports_today": 20,
    "verification_success_rate": 85.5,
    "avg_verification_time_ms": 2500,
    "created_at": "2025-01-20T12:00:00Z"
  }
}
```

---

## 错误码说明

### HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未认证或认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求过于频繁
- `500`: 服务器内部错误
- `503`: 服务不可用（并发限制）

### 业务错误码
```json
{
  "success": false,
  "message": "错误描述",
  "error": "error_code",
  "timestamp": "2025-01-20T12:00:00Z"
}
```

常见错误码：
- `rate_limit_exceeded`: 频率限制超出
- `concurrency_limit_exceeded`: 并发限制超出
- `request_too_large`: 请求体过大
- `invalid_email_format`: 邮箱格式错误
- `operation_not_found`: 操作不存在
- `task_not_found`: 任务不存在

---

## 安全特性

### 1. 认证授权
- 所有接口都需要JWT认证
- 支持令牌刷新机制
- 操作审计日志记录

### 2. 频率限制
- 批量导入：每小时3次
- 验证任务：每10分钟10次
- 任务控制：每小时5次

### 3. 并发控制
- 批量操作：最多3个并发
- 验证任务：最多5个并发
- 请求超时：30秒

### 4. 数据安全
- 邮箱密码AES-256-GCM加密存储
- 传输层TLS加密
- 敏感信息脱敏日志

### 5. 输入验证
- 邮箱格式验证
- 请求大小限制（10MB）
- SQL注入防护
- XSS防护

---

## 性能优化

### 1. 缓存策略
- 统计信息缓存（5分钟）
- 任务状态缓存（1分钟）
- 用户权限缓存（10分钟）

### 2. 批处理优化
- 批量导入分批处理（100个/批）
- 验证任务并发控制
- 数据库连接池优化

### 3. 监控指标
- 接口响应时间
- 错误率统计
- 并发数监控
- 系统资源使用率

---

## 使用示例

### JavaScript/TypeScript
```typescript
// 批量导入邮箱
const importAccounts = async (accounts: AccountData[]) => {
  const response = await fetch('/api/v1/mailbox/batch-import', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      accounts,
      source: '批量导入',
      auto_verify: true
    })
  });
  
  return response.json();
};

// 查询操作状态
const checkOperationStatus = async (operationId: string) => {
  const response = await fetch(`/api/v1/mailbox/batch-operation/${operationId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.json();
};
```

### cURL
```bash
# 批量导入
curl -X POST "http://localhost:8080/api/v1/mailbox/batch-import" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "accounts": [
      {"email": "<EMAIL>", "password": "password123"}
    ],
    "source": "测试导入",
    "auto_verify": true
  }'

# 获取账户列表
curl -X GET "http://localhost:8080/api/v1/mailbox/accounts?page=1&page_size=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
