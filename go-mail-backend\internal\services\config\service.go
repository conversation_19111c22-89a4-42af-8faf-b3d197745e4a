package config

import (
	"encoding/json"
	"fmt"
	"go-mail/internal/database"
	"strconv"
	"time"
)

// Service 配置服务
type Service struct {
	db *database.Database
}

// NewService 创建配置服务
func NewService(db *database.Database) *Service {
	return &Service{
		db: db,
	}
}

// ConfigItem 配置项
type ConfigItem struct {
	Key      string `json:"key"`
	Value    string `json:"value"`
	Type     string `json:"type"`
	Category string `json:"category"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	Mailbox struct {
		AutoReleaseMinutes     int `json:"auto_release_minutes"`
		MaxConcurrentAllocations int `json:"max_concurrent_allocations"`
	} `json:"mailbox"`
	Security struct {
		ActivationCodeExpiryDays int    `json:"activation_code_expiry_days"`
		JWTSecret               string `json:"jwt_secret"`
		AESKey                  string `json:"aes_key"`
	} `json:"security"`
	System struct {
		Name    string `json:"name"`
		Version string `json:"version"`
	} `json:"system"`
}

// GetConfig 获取系统配置
func (s *Service) GetConfig() (*SystemConfig, error) {
	config := &SystemConfig{}

	// 查询所有配置
	rows, err := s.db.GetDB().Query("SELECT key, value, type, category FROM system_config")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	configMap := make(map[string]string)
	for rows.Next() {
		var key, value, configType, category string
		if err := rows.Scan(&key, &value, &configType, &category); err != nil {
			continue
		}
		configMap[key] = value
	}

	// 填充配置结构
	if val, ok := configMap["mailbox_auto_release_minutes"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			config.Mailbox.AutoReleaseMinutes = intVal
		}
	}

	if val, ok := configMap["max_concurrent_allocations"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			config.Mailbox.MaxConcurrentAllocations = intVal
		}
	}

	if val, ok := configMap["activation_code_expiry_days"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			config.Security.ActivationCodeExpiryDays = intVal
		}
	}

	if val, ok := configMap["jwt_secret"]; ok {
		config.Security.JWTSecret = val
	}

	if val, ok := configMap["aes_key"]; ok {
		config.Security.AESKey = val
	}

	if val, ok := configMap["system_name"]; ok {
		config.System.Name = val
	}

	if val, ok := configMap["version"]; ok {
		config.System.Version = val
	}

	return config, nil
}

// UpdateConfig 更新系统配置
func (s *Service) UpdateConfig(config *SystemConfig) error {
	// 开始事务
	tx, err := s.db.GetDB().Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 更新配置项
	updates := map[string]interface{}{
		"mailbox_auto_release_minutes":     config.Mailbox.AutoReleaseMinutes,
		"max_concurrent_allocations":       config.Mailbox.MaxConcurrentAllocations,
		"activation_code_expiry_days":      config.Security.ActivationCodeExpiryDays,
		"jwt_secret":                       config.Security.JWTSecret,
		"aes_key":                          config.Security.AESKey,
		"system_name":                      config.System.Name,
		"version":                          config.System.Version,
	}

	for key, value := range updates {
		var valueStr string
		switch v := value.(type) {
		case int:
			valueStr = strconv.Itoa(v)
		case string:
			valueStr = v
		default:
			valueStr = fmt.Sprintf("%v", v)
		}

		_, err = tx.Exec(`
			UPDATE system_config 
			SET value = ?, updated_at = CURRENT_TIMESTAMP 
			WHERE key = ?`,
			valueStr, key)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// GetConfigItem 获取单个配置项
func (s *Service) GetConfigItem(key string) (*ConfigItem, error) {
	var item ConfigItem
	err := s.db.GetDB().QueryRow(`
		SELECT key, value, type, category 
		FROM system_config 
		WHERE key = ?`,
		key).Scan(&item.Key, &item.Value, &item.Type, &item.Category)
	
	if err != nil {
		return nil, fmt.Errorf("配置项不存在: %s", key)
	}

	return &item, nil
}

// SetConfigItem 设置单个配置项
func (s *Service) SetConfigItem(key, value, configType, category string) error {
	_, err := s.db.GetDB().Exec(`
		INSERT OR REPLACE INTO system_config (key, value, type, category, updated_at)
		VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)`,
		key, value, configType, category)
	return err
}

// DeleteConfigItem 删除配置项
func (s *Service) DeleteConfigItem(key string) error {
	result, err := s.db.GetDB().Exec("DELETE FROM system_config WHERE key = ?", key)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("配置项不存在: %s", key)
	}

	return nil
}

// ListConfigItems 获取配置项列表
func (s *Service) ListConfigItems(category string) ([]ConfigItem, error) {
	var query string
	var args []interface{}

	if category != "" {
		query = "SELECT key, value, type, category FROM system_config WHERE category = ? ORDER BY key"
		args = append(args, category)
	} else {
		query = "SELECT key, value, type, category FROM system_config ORDER BY category, key"
	}

	rows, err := s.db.GetDB().Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var items []ConfigItem
	for rows.Next() {
		var item ConfigItem
		if err := rows.Scan(&item.Key, &item.Value, &item.Type, &item.Category); err != nil {
			continue
		}
		items = append(items, item)
	}

	return items, nil
}

// GetConfigValue 获取配置值（泛型方法）
func (s *Service) GetConfigValue(key string, defaultValue interface{}) interface{} {
	item, err := s.GetConfigItem(key)
	if err != nil {
		return defaultValue
	}

	switch defaultValue.(type) {
	case int:
		if val, err := strconv.Atoi(item.Value); err == nil {
			return val
		}
	case bool:
		if val, err := strconv.ParseBool(item.Value); err == nil {
			return val
		}
	case string:
		return item.Value
	case time.Duration:
		if val, err := time.ParseDuration(item.Value); err == nil {
			return val
		}
	}

	return defaultValue
}

// SetConfigValue 设置配置值（泛型方法）
func (s *Service) SetConfigValue(key string, value interface{}, category string) error {
	var valueStr, configType string

	switch v := value.(type) {
	case int:
		valueStr = strconv.Itoa(v)
		configType = database.ConfigTypeInt
	case bool:
		valueStr = strconv.FormatBool(v)
		configType = database.ConfigTypeBool
	case string:
		valueStr = v
		configType = database.ConfigTypeString
	case time.Duration:
		valueStr = v.String()
		configType = database.ConfigTypeString
	default:
		// 尝试JSON序列化
		if jsonBytes, err := json.Marshal(v); err == nil {
			valueStr = string(jsonBytes)
			configType = database.ConfigTypeJSON
		} else {
			valueStr = fmt.Sprintf("%v", v)
			configType = database.ConfigTypeString
		}
	}

	return s.SetConfigItem(key, valueStr, configType, category)
}

// ResetToDefaults 重置为默认配置
func (s *Service) ResetToDefaults() error {
	// 删除所有现有配置
	_, err := s.db.GetDB().Exec("DELETE FROM system_config")
	if err != nil {
		return err
	}

	// 重新创建默认配置
	return s.createDefaultConfig()
}

// createDefaultConfig 创建默认配置（与database包中的方法相同）
func (s *Service) createDefaultConfig() error {
	defaultConfigs := []struct {
		Key      string
		Value    string
		Type     string
		Category string
	}{
		{"mailbox_auto_release_minutes", "3", "int", "mailbox"},
		{"max_concurrent_allocations", "100", "int", "mailbox"},
		{"activation_code_expiry_days", "30", "int", "security"},
		{"jwt_secret", "go-mail-jwt-secret-key", "string", "security"},
		{"aes_key", "go-mail-aes-key-32-bytes-long!!", "string", "security"},
		{"system_name", "Go-Mail临时邮箱服务", "string", "system"},
		{"version", "1.0.0", "string", "system"},
	}

	for _, config := range defaultConfigs {
		err := s.SetConfigItem(config.Key, config.Value, config.Type, config.Category)
		if err != nil {
			return err
		}
	}

	return nil
}

// ValidateConfig 验证配置
func (s *Service) ValidateConfig(config *SystemConfig) error {
	// 验证邮箱配置
	if config.Mailbox.AutoReleaseMinutes < 1 || config.Mailbox.AutoReleaseMinutes > 60 {
		return fmt.Errorf("自动释放时间必须在1-60分钟之间")
	}

	if config.Mailbox.MaxConcurrentAllocations < 1 || config.Mailbox.MaxConcurrentAllocations > 1000 {
		return fmt.Errorf("最大并发分配数必须在1-1000之间")
	}

	// 验证安全配置
	if config.Security.ActivationCodeExpiryDays < 1 || config.Security.ActivationCodeExpiryDays > 365 {
		return fmt.Errorf("激活码有效期必须在1-365天之间")
	}

	if len(config.Security.JWTSecret) < 16 {
		return fmt.Errorf("JWT密钥长度不能少于16个字符")
	}

	if len(config.Security.AESKey) != 32 {
		return fmt.Errorf("AES密钥长度必须为32个字符")
	}

	// 验证系统配置
	if config.System.Name == "" {
		return fmt.Errorf("系统名称不能为空")
	}

	if config.System.Version == "" {
		return fmt.Errorf("系统版本不能为空")
	}

	return nil
}
