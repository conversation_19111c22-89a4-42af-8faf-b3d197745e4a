// 设备指纹生成工具

// 获取浏览器信息
function getBrowserInfo() {
  const ua = navigator.userAgent
  const browser = {
    name: '',
    version: '',
  }

  if (ua.includes('Chrome')) {
    browser.name = 'Chrome'
    browser.version = ua.match(/Chrome\/(\d+)/)?.[1] || ''
  } else if (ua.includes('Firefox')) {
    browser.name = 'Firefox'
    browser.version = ua.match(/Firefox\/(\d+)/)?.[1] || ''
  } else if (ua.includes('Safari')) {
    browser.name = 'Safari'
    browser.version = ua.match(/Version\/(\d+)/)?.[1] || ''
  } else if (ua.includes('Edge')) {
    browser.name = 'Edge'
    browser.version = ua.match(/Edge\/(\d+)/)?.[1] || ''
  }

  return browser
}

// 获取屏幕信息
function getScreenInfo() {
  return {
    width: screen.width,
    height: screen.height,
    colorDepth: screen.colorDepth,
    pixelDepth: screen.pixelDepth,
  }
}

// 获取时区信息
function getTimezoneInfo() {
  return {
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    offset: new Date().getTimezoneOffset(),
  }
}

// 获取语言信息
function getLanguageInfo() {
  return {
    language: navigator.language,
    languages: navigator.languages.join(','),
  }
}

// 获取平台信息
function getPlatformInfo() {
  return {
    platform: navigator.platform,
    userAgent: navigator.userAgent,
    cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack,
  }
}

// 获取Canvas指纹
function getCanvasFingerprint() {
  try {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) return ''

    canvas.width = 200
    canvas.height = 50

    // 绘制文本
    ctx.textBaseline = 'top'
    ctx.font = '14px Arial'
    ctx.fillStyle = '#f60'
    ctx.fillRect(125, 1, 62, 20)
    ctx.fillStyle = '#069'
    ctx.fillText('Go-Mail Fingerprint', 2, 15)
    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)'
    ctx.fillText('Go-Mail Fingerprint', 4, 17)

    return canvas.toDataURL()
  } catch (error) {
    console.warn('Canvas fingerprint generation failed:', error)
    return ''
  }
}

// 获取WebGL指纹
function getWebGLFingerprint() {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    if (!gl) return ''

    // 正确类型转换为WebGLRenderingContext
    const webglContext = gl as WebGLRenderingContext
    const debugInfo = webglContext.getExtension('WEBGL_debug_renderer_info')
    if (!debugInfo) return ''

    return {
      vendor: webglContext.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
      renderer: webglContext.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL),
    }
  } catch (error) {
    console.warn('WebGL fingerprint generation failed:', error)
    return ''
  }
}

// 生成简单的哈希
function simpleHash(str: string): string {
  let hash = 0
  if (str.length === 0) return hash.toString()

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // Convert to 32bit integer
  }

  return Math.abs(hash).toString(36)
}

// 生成设备指纹
export async function generateDeviceFingerprint(): Promise<string> {
  try {
    const components = {
      browser: getBrowserInfo(),
      screen: getScreenInfo(),
      timezone: getTimezoneInfo(),
      language: getLanguageInfo(),
      platform: getPlatformInfo(),
      canvas: getCanvasFingerprint(),
      webgl: getWebGLFingerprint(),
      timestamp: Date.now(),
    }

    // 将所有组件序列化为字符串
    const fingerprintString = JSON.stringify(components)

    // 生成哈希
    const hash = simpleHash(fingerprintString)

    // 添加前缀和时间戳
    const fingerprint = `fp_${hash}_${Date.now().toString(36)}`

    console.log('Generated device fingerprint:', fingerprint)
    return fingerprint
  } catch (error) {
    console.error('Device fingerprint generation failed:', error)
    // 生成一个基于时间的备用指纹
    return `fp_fallback_${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 验证设备指纹格式
export function validateDeviceFingerprint(fingerprint: string): boolean {
  return /^fp_[a-z0-9]+_[a-z0-9]+$/.test(fingerprint)
}

// 获取设备信息摘要（用于显示）
export function getDeviceInfoSummary() {
  const browser = getBrowserInfo()
  const platform = getPlatformInfo()

  return {
    browser: `${browser.name} ${browser.version}`,
    platform: platform.platform,
    language: navigator.language,
    screen: `${screen.width}x${screen.height}`,
  }
}
