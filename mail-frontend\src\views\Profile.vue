<template>
  <div class="profile">
    <div class="page-header">
      <h1>个人设置</h1>
      <p>管理您的账户信息和偏好设置</p>
    </div>

    <div class="profile-grid">
      <!-- 基本信息 -->
      <n-card title="基本信息" class="profile-card">
        <div class="user-info">
          <div class="avatar-section">
            <n-avatar size="large" :style="{ backgroundColor: '#18a058' }">
              <n-icon size="32">
                <PersonIcon />
              </n-icon>
            </n-avatar>
            <div class="user-details">
              <h3>{{ authStore.user?.username }}</h3>
              <p>{{ authStore.user?.role === 'admin' ? '系统管理员' : '普通用户' }}</p>
              <p class="last-login">上次登录: {{ formatTime(authStore.user?.lastLoginTime) }}</p>
            </div>
          </div>
        </div>
      </n-card>

      <!-- 修改密码 -->
      <n-card title="修改密码" class="profile-card">
        <n-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordFormRules"
          label-placement="top"
        >
          <n-form-item label="当前密码" path="oldPassword">
            <n-input
              v-model:value="passwordForm.oldPassword"
              type="password"
              placeholder="请输入当前密码"
              show-password-on="mousedown"
            />
          </n-form-item>

          <n-form-item label="新密码" path="newPassword">
            <n-input
              v-model:value="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password-on="mousedown"
            />
          </n-form-item>

          <n-form-item label="确认新密码" path="confirmPassword">
            <n-input
              v-model:value="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password-on="mousedown"
            />
          </n-form-item>

          <n-form-item>
            <n-button type="primary" :loading="passwordLoading" @click="changePassword" block>
              修改密码
            </n-button>
          </n-form-item>
        </n-form>
      </n-card>

      <!-- 偏好设置 -->
      <n-card title="偏好设置" class="profile-card">
        <div class="preference-item">
          <div class="preference-label">
            <n-icon>
              <MoonIcon />
            </n-icon>
            <span>深色主题</span>
          </div>
          <n-switch :value="appStore.isDark" @update:value="toggleTheme" />
        </div>

        <div class="preference-item">
          <div class="preference-label">
            <n-icon>
              <LanguageIcon />
            </n-icon>
            <span>语言设置</span>
          </div>
          <n-select
            :value="appStore.language"
            :options="languageOptions"
            style="width: 120px"
            @update:value="changeLanguage"
          />
        </div>

        <div class="preference-item">
          <div class="preference-label">
            <n-icon>
              <MenuIcon />
            </n-icon>
            <span>侧边栏折叠</span>
          </div>
          <n-switch
            :value="appStore.sidebarCollapsed"
            @update:value="appStore.setSidebarCollapsed"
          />
        </div>
      </n-card>

      <!-- 设备信息 -->
      <n-card title="设备信息" class="profile-card">
        <div class="device-info">
          <div class="device-item">
            <span class="device-label">设备指纹:</span>
            <code class="device-value">{{ authStore.deviceFingerprint }}</code>
          </div>
          <div class="device-item">
            <span class="device-label">浏览器:</span>
            <span class="device-value">{{ deviceInfo.browser }}</span>
          </div>
          <div class="device-item">
            <span class="device-label">平台:</span>
            <span class="device-value">{{ deviceInfo.platform }}</span>
          </div>
          <div class="device-item">
            <span class="device-label">屏幕分辨率:</span>
            <span class="device-value">{{ deviceInfo.screen }}</span>
          </div>
          <div class="device-item">
            <span class="device-label">语言:</span>
            <span class="device-value">{{ deviceInfo.language }}</span>
          </div>
        </div>
      </n-card>

      <!-- 安全设置 -->
      <n-card title="安全设置" class="profile-card">
        <div class="security-actions">
          <n-button type="warning" @click="showLogoutModal = true" block>
            <template #icon>
              <n-icon>
                <LogOutIcon />
              </n-icon>
            </template>
            退出登录
          </n-button>
        </div>
      </n-card>

      <!-- 系统信息 -->
      <n-card title="系统信息" class="profile-card">
        <div class="system-info">
          <div class="info-item">
            <span class="info-label">前端版本:</span>
            <span class="info-value">{{ frontendVersion }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">构建时间:</span>
            <span class="info-value">{{ buildTime }}</span>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 退出登录确认模态框 -->
    <n-modal v-model:show="showLogoutModal" preset="dialog" title="退出登录">
      <template #default>
        <p>确定要退出登录吗？退出后需要重新输入用户名和密码。</p>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showLogoutModal = false">取消</n-button>
          <n-button type="primary" @click="handleLogout">确认退出</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  NCard,
  NAvatar,
  NIcon,
  NForm,
  NFormItem,
  NInput,
  NButton,
  NSwitch,
  NSelect,
  NModal,
  NSpace,
  useMessage,
  type FormInst,
  type FormRules,
} from 'naive-ui'
import {
  Person as PersonIcon,
  Moon as MoonIcon,
  Language as LanguageIcon,
  Menu as MenuIcon,
  LogOut as LogOutIcon,
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { authApi } from '@/api/auth'
import { getDeviceInfoSummary } from '@/utils/device'

const message = useMessage()
const authStore = useAuthStore()
const appStore = useAppStore()

// 响应式数据
const passwordLoading = ref(false)
const showLogoutModal = ref(false)

// 表单引用
const passwordFormRef = ref<FormInst | null>(null)

// 密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 密码表单验证规则
const passwordFormRules: FormRules = {
  oldPassword: [
    {
      required: true,
      message: '请输入当前密码',
      trigger: ['input', 'blur'],
    },
  ],
  newPassword: [
    {
      required: true,
      message: '请输入新密码',
      trigger: ['input', 'blur'],
    },
    {
      min: 6,
      message: '密码长度不能少于6个字符',
      trigger: ['input', 'blur'],
    },
  ],
  confirmPassword: [
    {
      required: true,
      message: '请确认新密码',
      trigger: ['input', 'blur'],
    },
    {
      validator: (_rule: any, value: string) => {
        return value === passwordForm.newPassword
      },
      message: '两次输入的密码不一致',
      trigger: ['input', 'blur'],
    },
  ],
}

// 语言选项
const languageOptions = [
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
]

// 设备信息
const deviceInfo = getDeviceInfoSummary()

// 前端版本信息
const frontendVersion = import.meta.env.VITE_APP_VERSION || '1.0.0'
const buildTime = new Date().toLocaleString()

// 格式化时间
const formatTime = (timestamp?: string) => {
  if (!timestamp) return '未知'
  return new Date(timestamp).toLocaleString()
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    await authApi.changePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword,
    })

    message.success('密码修改成功')

    // 重置表单
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
    })
  } catch (error) {
    console.error('Failed to change password:', error)
    message.error('密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

// 切换主题
const toggleTheme = (isDark: boolean) => {
  appStore.setTheme(isDark ? 'dark' : 'light')
}

// 切换语言
const changeLanguage = (language: string) => {
  appStore.setLanguage(language as any)
  message.success('语言设置已更新')
}

// 处理退出登录
const handleLogout = () => {
  authStore.logout()
  showLogoutModal.value = false
}
</script>

<style scoped>
.profile {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.profile-card {
  height: fit-content;
}

.user-info {
  padding: 16px 0;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-details h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
}

.user-details p {
  margin: 0 0 4px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.last-login {
  font-size: 12px !important;
  color: var(--n-text-color-3) !important;
}

.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.preference-item:last-child {
  border-bottom: none;
}

.preference-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.device-info,
.system-info {
  padding: 8px 0;
}

.device-item,
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.device-label,
.info-label {
  color: var(--n-text-color-2);
  min-width: 100px;
}

.device-value,
.info-value {
  color: var(--n-text-color-1);
  text-align: right;
  word-break: break-all;
}

.device-value code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: var(--n-code-color);
  padding: 2px 6px;
  border-radius: 4px;
}

.security-actions {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }

  .device-item,
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .device-label,
  .info-label {
    min-width: auto;
  }

  .device-value,
  .info-value {
    text-align: left;
  }
}
</style>
