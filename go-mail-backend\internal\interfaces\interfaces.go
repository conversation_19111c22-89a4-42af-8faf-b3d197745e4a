package interfaces

import (
	"context"
	"go-mail/internal/types"
	"net/http"
)

// MailManager 邮件管理系统主接口
type MailManager interface {
	// 批量操作
	BatchLogin(ctx context.Context, accounts []types.Account) (*types.BatchResult, error)
	BatchLogout(ctx context.Context, sessionIDs []string) error
	BatchHealthCheck(ctx context.Context) (*types.HealthReport, error)

	// 账户管理
	AddAccount(account types.Account) error
	RemoveAccount(username string) error
	GetAccountStatus(username string) (*types.AccountStatus, error)
	ListAccounts() []types.AccountInfo

	// 代理管理
	SetProxyPool(proxies []types.ProxyConfig) error
	RotateProxy(sessionID string) error
	GetProxyStatus() []types.ProxyStatus

	// 配置管理
	UpdateConfig(config *types.ManagerConfig) error
	GetConfig() *types.ManagerConfig

	// 监控和统计
	GetStatistics() *types.Statistics
	GetLogs(filter types.LogFilter) []types.LogEntry

	// 生命周期管理
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	IsRunning() bool
}

// AccountPool 账户池管理接口
type AccountPool interface {
	Add(account types.Account) error
	Remove(username string) error
	Get(username string) (*types.Account, error)
	GetAvailable() []types.Account
	GetByStatus(status types.AccountStatus) []types.Account
	UpdateStatus(username string, status types.AccountStatus) error
	UpdateLoginInfo(username string, success bool) error
	GetAccountInfo() []types.AccountInfo
	Count() int
	Clear() error
}

// SessionPool 会话池管理接口
type SessionPool interface {
	Create(account types.Account, proxy *types.ProxyConfig) (*types.Session, error)
	Get(sessionID string) (*types.Session, error)
	Remove(sessionID string) error
	GetActive() []types.Session
	GetByAccount(username string) (*types.Session, error)
	UpdateSession(sessionID string, jsessionID, navigatorSID, finalURL string) error
	SetClient(sessionID string, client *http.Client) error
	Cleanup() error // 清理过期会话
	Count() int
	Clear() error
}

// ProxyManager 代理管理接口
type ProxyManager interface {
	Add(proxy types.ProxyConfig) error
	Remove(proxyID string) error
	Get(proxyID string) (*types.ProxyConfig, error)
	GetAvailable() []types.ProxyConfig
	GetNext() (*types.ProxyConfig, error) // 获取下一个可用代理
	HealthCheck(proxyID string) error
	UpdateStatus(proxyID string, enabled bool) error
	GetProxyStatus() []types.ProxyStatus
	BatchHealthCheck() error
	Count() int
	Clear() error
}

// LoginClient 登录客户端接口
type LoginClient interface {
	Login(ctx context.Context, account types.Account, proxy *types.ProxyConfig) (*types.LoginResult, error)
	Logout(ctx context.Context, session *types.Session) error
	ValidateSession(ctx context.Context, session *types.Session) error
	RefreshSession(ctx context.Context, session *types.Session) error
}

// TaskScheduler 任务调度器接口
type TaskScheduler interface {
	Schedule(ctx context.Context, task Task) error
	Cancel(taskID string) error
	GetStatus(taskID string) TaskStatus
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

// Task 任务接口
type Task interface {
	ID() string
	Execute(ctx context.Context) error
	GetType() TaskType
	GetPriority() int
}

// TaskType 任务类型
type TaskType int

const (
	TaskTypeLogin TaskType = iota
	TaskTypeLogout
	TaskTypeHealthCheck
	TaskTypeCleanup
)

// TaskStatus 任务状态
type TaskStatus int

const (
	TaskStatusPending TaskStatus = iota
	TaskStatusRunning
	TaskStatusCompleted
	TaskStatusFailed
	TaskStatusCancelled
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Fatal(msg string, fields ...interface{})
}

// ConfigManager 配置管理接口
type ConfigManager interface {
	Load() (*types.ManagerConfig, error)
	Save(config *types.ManagerConfig) error
	Watch(callback func(*types.ManagerConfig)) error
	Validate(config *types.ManagerConfig) error
}

// StateManager 状态管理接口
type StateManager interface {
	SaveState(key string, value interface{}) error
	LoadState(key string, value interface{}) error
	DeleteState(key string) error
	ListKeys() []string
	Clear() error
}

// EventBus 事件总线接口
type EventBus interface {
	Subscribe(eventType EventType, handler EventHandler) error
	Unsubscribe(eventType EventType, handler EventHandler) error
	Publish(event Event) error
}

// Event 事件接口
type Event interface {
	Type() EventType
	Data() interface{}
	Timestamp() int64
}

// EventType 事件类型
type EventType int

const (
	EventTypeAccountAdded EventType = iota
	EventTypeAccountRemoved
	EventTypeSessionCreated
	EventTypeSessionExpired
	EventTypeLoginSuccess
	EventTypeLoginFailed
	EventTypeProxyRotated
	EventTypeHealthCheckCompleted
)

// EventHandler 事件处理器
type EventHandler func(event Event) error

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	IncrementCounter(name string, tags map[string]string)
	RecordGauge(name string, value float64, tags map[string]string)
	RecordHistogram(name string, value float64, tags map[string]string)
	RecordTimer(name string, duration int64, tags map[string]string)
}
