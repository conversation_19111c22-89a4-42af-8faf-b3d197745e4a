## 获取别名邮箱

GET /mail/client/settings/allEmailAddresses;jsessionid=7F2BC6C096B5E5C01F28CFB68E465681-n2.lxa07a HTTP/1.1
Host: 3c-lxa.mail.com
Connection: keep-alive
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: iframe
Referer: https://3c-lxa.mail.com/mail/client/settings/signature/;jsessionid=7F2BC6C096B5E5C01F28CFB68E465681-n2.lxa07a?navsid=84b2f7e5886ca04988b575ba1c05f1ba43cdf469c3a81de77314a03e755983aa90720bcfe68cf899fdb38fb463db0f0e&iac_appname=mail_settings&iac_token=7cf87619ccbc201f90c4da3ef7a16645&navigator_theme=mailcomblue&navigator_bg=mailcomblue
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: en,zh-CN;q=0.9,zh;q=0.8
Cookie: GDNA=true; uiconsent={%22permissionFeature%22:[%22fullConsent%22]}; _ga=GA1.1.1348752036.1752998656; wa=9ed62dc666d2c569a9de26633f800670; _autuserid2=7529071897527846430; cookieKID=kid%40autoref%40www.mail.com; cookiePartner=kid%40autoref%40www.mail.com; tp_id=UJbATKZTaYNtndY6Gxc1AGHtXIV2hjVvmyAgAcTkCwH1-dLk78lQTDAUbw19vkws-XO17A; NGUserID=qhY3GPpx-171-1752998733-0; ua_id=019826dd-e58b-7b45-9929-566907e334ac; adtrgtng=eyJhIjp7ImNzIjoxLCJjYSI6MSwiaSI6MSwiYSI6MCwidWkiOjAsImZwIjowfX0=; tpid_sec=eyJraWQiOiJtYWlsY29tMSIsImVuYyI6IkEyNTZHQ00iLCJhbGciOiJkaXIifQ..flyB6ApLd19AAYny.JpNLVFktGcvdW9yt9xPD0_5Kh86f7OtJFhe2gNFmjzbdNKFZSpcgP2FBGL_jAcgLsaVdGB36cNm7p94WAVcDD1IcIwCNM1JcCBX5vhflDlRYaVB7FKjodLsrzkJcyGmLhSLuT4PCfjkUf9fgaWo.G-uV4CqOWnB6hYy7VJMrwA; _ga_V2FMR8VFB6=GS2.1.s1753082153$o4$g0$t1753082156$j57$l0$h0; 2Q7NMZu76R546cfc02-21f8-45a5=~1982bd6fc48#546cfc02-21f8-45a5-9bfe-e1960c61889d; iac_token=7cf87619ccbc201f90c4da3ef7a16645; 2Q7NMZu76R078f56cb-72bd-42fe=~1982bd72171#078f56cb-72bd-42fe-9345-cee6c2838974; utag_main=_sn:4$_se:6%3Bexp-session$_ss:0%3Bexp-session$_st:1753084080984%3Bexp-session$ses_id:*************%3Bexp-session$_pn:2%3Bexp-session; 2Q7NMZu76Ra8839c2b-5d89-48fd=~1982bd8d3ce#a8839c2b-5d89-48fd-8519-2a5448857ad3


### 返回html

#### 获取别名邮箱列表
.....(省略其他html，下面是别名邮箱列表).....

<div class="table_body">

<div class="table_body-row table_row is-first" id="id49" title="Use this address as default sender address" data-row-id="-1096578409">
<div class="table_field table_col-12" id="id4a">

<strong>
<EMAIL>
</strong>
(Default sender address)

</div>
</div>

<div class="table_body-row table_row" id="id4b" data-row-id="-1381365044">
<div class="table_field table_col-12" id="id4c">

<EMAIL>

</div>
</div>

<div class="table_body-row table_row" id="id4d" data-row-id="1920274577">
<div class="table_field table_col-12" id="id4e">

<EMAIL>

</div>
</div>

<div class="table_body-row table_row is-last" id="id4f" data-row-id="-1425478176">
<div class="table_field table_col-12" id="id50">

<EMAIL>

</div>
</div>
<div class="table_body-row table_row is-last" id="idb2" data-row-id="**********">
<div class="table_field table_col-12" id="idb3">

<EMAIL>

</div>
</div>
</div>


<div class="js-template is-hidden" data-template-name="hoverMenu" data-template-prevent-detach="true" prevent-detach="true">

PS：需要从html中获取别名邮箱id，用于删除别名邮箱。


#### 获取别名可自定义域名列表

<select name="fieldSet:fieldSet_body:grid:addressSelection:domainSelection" class="form-element form-element-select">

<optgroup label="Hobbies">

<option value="option0">2trom.com</option>

<option value="option1">activist.com</option>

<option value="option2">artlover.com</option>

<option value="option3">atheist.com</option>

<option value="option4">bikerider.com</option>

<option value="option5">birdlover.com</option>

<option value="option6">boardermail.com</option>

<option value="option7">brew-master.com</option>

<option value="option8">bsdmail.com</option>

<option value="option9">catlover.com</option>

<option value="option10">chef.net</option>

<option value="option11">collector.org</option>

<option value="option12">cutey.com</option>

<option value="option13">dbzmail.com</option>

<option value="option14">doglover.com</option>

<option value="option15">doramail.com</option>

<option value="option16">galaxyhit.com</option>

<option value="option17">gardener.com</option>

<option value="option18">greenmail.net</option>

<option value="option19">hackermail.com</option>

<option value="option20">hilarious.com</option>

<option value="option21">keromail.com</option>

<option value="option22">kittymail.com</option>

<option value="option23">linuxmail.org</option>

<option value="option24">lovecat.com</option>

<option value="option25">marchmail.com</option>

<option value="option26">musician.org</option>

<option value="option27">nonpartisan.com</option>

<option value="option28">petlover.com</option>

<option value="option29">photographer.net</option>

<option value="option30">songwriter.net</option>

<option value="option31">techie.com</option>

<option value="option32">theplate.com</option>

<option value="option33">toke.com</option>

<option value="option34">uymail.com</option>

</optgroup>
...(很多域名，省略)

</select>

PS：需要从html中获取域名对应选择id，用于创建别名邮箱。










## 创建别名邮箱

POST /mail/client/settings/allEmailAddresses;jsessionid=7F2BC6C096B5E5C01F28CFB68E465681-n2.lxa07a?2-1.0-splitPanel-splitpanelContainer-splitpanelRight-splitPanel_body-childContainer-internalAliasChapter-internalAliasChapter_body-internalAliasPanel-form-fieldSet-fieldSet_body-grid-button-button HTTP/1.1
Host: 3c-lxa.mail.com
Connection: keep-alive
Content-Length: 202
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
Wicket-Ajax: true
Wicket-Ajax-BaseURL: settings/allEmailAddresses
X-Requested-With: XMLHttpRequest
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: application/xml, text/xml, */*; q=0.01
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
Wicket-FocusedElementId: id30
Origin: https://3c-lxa.mail.com
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=7F2BC6C096B5E5C01F28CFB68E465681-n2.lxa07a
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: en,zh-CN;q=0.9,zh;q=0.8
Cookie: GDNA=true; uiconsent={%22permissionFeature%22:[%22fullConsent%22]}; _ga=GA1.1.1348752036.1752998656; wa=9ed62dc666d2c569a9de26633f800670; _autuserid2=7529071897527846430; cookieKID=kid%40autoref%40www.mail.com; cookiePartner=kid%40autoref%40www.mail.com; tp_id=UJbATKZTaYNtndY6Gxc1AGHtXIV2hjVvmyAgAcTkCwH1-dLk78lQTDAUbw19vkws-XO17A; NGUserID=qhY3GPpx-171-1752998733-0; ua_id=019826dd-e58b-7b45-9929-566907e334ac; adtrgtng=eyJhIjp7ImNzIjoxLCJjYSI6MSwiaSI6MSwiYSI6MCwidWkiOjAsImZwIjowfX0=; tpid_sec=eyJraWQiOiJtYWlsY29tMSIsImVuYyI6IkEyNTZHQ00iLCJhbGciOiJkaXIifQ..flyB6ApLd19AAYny.JpNLVFktGcvdW9yt9xPD0_5Kh86f7OtJFhe2gNFmjzbdNKFZSpcgP2FBGL_jAcgLsaVdGB36cNm7p94WAVcDD1IcIwCNM1JcCBX5vhflDlRYaVB7FKjodLsrzkJcyGmLhSLuT4PCfjkUf9fgaWo.G-uV4CqOWnB6hYy7VJMrwA; _ga_V2FMR8VFB6=GS2.1.s1753082153$o4$g0$t1753082156$j57$l0$h0; 2Q7NMZu76R546cfc02-21f8-45a5=~1982bd6fc48#546cfc02-21f8-45a5-9bfe-e1960c61889d; iac_token=7cf87619ccbc201f90c4da3ef7a16645; 2Q7NMZu76R078f56cb-72bd-42fe=~1982bd72171#078f56cb-72bd-42fe-9345-cee6c2838974; utag_main=_sn:4$_se:6%3Bexp-session$_ss:0%3Bexp-session$_st:1753084080984%3Bexp-session$ses_id:*************%3Bexp-session$_pn:2%3Bexp-session; 2Q7NMZu76Ra8839c2b-5d89-48fd=~1982bd8d3ce#a8839c2b-5d89-48fd-8519-2a5448857ad3

fieldSet%3AfieldSet_body%3Agrid%3AaddressSelection%3AlocalPart=augg8866&fieldSet%3AfieldSet_body%3Agrid%3AaddressSelection%3AdomainSelection=option138&fieldSet%3AfieldSet_body%3Agrid%3Abutton%3Abutton=1

参数说明： 
augg8866可自定义邮箱别名，
option138 是域名对应选择id，需要从上面的html中获取。



### 返回html
...( 返回html)...
如果在html里面搜索出现：Your e-mail address (<EMAIL>) was successfully created. 则表示创建成功，否则创建失败。

<div class="system-message_topmessage headline headline-layout3" id="ida1">Your e-mail address (<EMAIL>) was successfully created.</div>

如果出现：This e-mail-address (<EMAIL>) is not available! 则表示该邮箱别名已存在，不能创建。
<h4 class="headline headline-layout4" data-webdriver="headline">This e-mail-address (<EMAIL>) is not available!</h4>


---

## 删除别名邮箱


GET /mail/client/settings/allEmailAddresses;jsessionid=DB940C016DAC1BF31B410ECAEAD361A4-n1.lxa10a?3-1.0-splitPanel-splitpanelContainer-splitpanelRight-splitPanel_body-childContainer-addressesListChapter-addressesListChapter_body-addresses-hoverTemplate-hoverIconPanel-hoverIcons-2-hoverIcon&rowId=**********&_=1753083616405 HTTP/1.1
Host: 3c-lxa.mail.com
Connection: keep-alive
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
Wicket-Ajax: true
Wicket-Ajax-BaseURL: settings/allEmailAddresses
X-Requested-With: XMLHttpRequest
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: application/xml, text/xml, */*; q=0.01
Wicket-FocusedElementId: id65
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=DB940C016DAC1BF31B410ECAEAD361A4-n1.lxa10a
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: en,zh-CN;q=0.9,zh;q=0.8
Cookie: GDNA=true; uiconsent={%22permissionFeature%22:[%22fullConsent%22]}; _ga=GA1.1.1348752036.1752998656; wa=9ed62dc666d2c569a9de26633f800670; _autuserid2=7529071897527846430; cookieKID=kid%40autoref%40www.mail.com; cookiePartner=kid%40autoref%40www.mail.com; tp_id=UJbATKZTaYNtndY6Gxc1AGHtXIV2hjVvmyAgAcTkCwH1-dLk78lQTDAUbw19vkws-XO17A; NGUserID=qhY3GPpx-171-1752998733-0; ua_id=019826dd-e58b-7b45-9929-566907e334ac; adtrgtng=eyJhIjp7ImNzIjoxLCJjYSI6MSwiaSI6MSwiYSI6MCwidWkiOjAsImZwIjowfX0=; tpid_sec=eyJraWQiOiJtYWlsY29tMSIsImVuYyI6IkEyNTZHQ00iLCJhbGciOiJkaXIifQ..flyB6ApLd19AAYny.JpNLVFktGcvdW9yt9xPD0_5Kh86f7OtJFhe2gNFmjzbdNKFZSpcgP2FBGL_jAcgLsaVdGB36cNm7p94WAVcDD1IcIwCNM1JcCBX5vhflDlRYaVB7FKjodLsrzkJcyGmLhSLuT4PCfjkUf9fgaWo.G-uV4CqOWnB6hYy7VJMrwA; _ga_V2FMR8VFB6=GS2.1.s1753082153$o4$g0$t1753082156$j57$l0$h0; 2Q7NMZu76R546cfc02-21f8-45a5=~1982bd6fc48#546cfc02-21f8-45a5-9bfe-e1960c61889d; iac_token=7cf87619ccbc201f90c4da3ef7a16645; 2Q7NMZu76R078f56cb-72bd-42fe=~1982bd72171#078f56cb-72bd-42fe-9345-cee6c2838974; 2Q7NMZu76Ra8839c2b-5d89-48fd=~1982bd8d3ce#a8839c2b-5d89-48fd-8519-2a5448857ad3; 2Q7NMZu76R0e3fa4bc-160f-43a2=~1982beaf495#0e3fa4bc-160f-43a2-a00b-cc6fb76822ec; utag_main=_sn:4$_se:12%3Bexp-session$_ss:0%3Bexp-session$_st:*************%3Bexp-session$ses_id:*************%3Bexp-session$_pn:4%3Bexp-session; 2Q7NMZu76R381b97a9-20eb-4466=~1982bec2aa0#381b97a9-20eb-4466-b341-5a045052654f


参数说明：
【rowId】
rowId=********** 是别名邮箱id，需要从上面的html中获取。


### 返回html


...(省略其他html，下面是删除别名邮箱).....
出现：<p id="idc5">Should this address be deleted, it will no longer be available for use in this account.</p> 则表示删除成功，否则删除失败。

<p id="idc5">Should this address be deleted, it will no longer be available for use in this account.</p>




