package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// UserController 用户管理控制器
type UserController struct {
	adminAPI *AdminAPI
}

// NewUserController 创建用户管理控制器
func NewUserController(adminAPI *AdminAPI) *UserController {
	return &UserController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册用户管理路由
func (c *UserController) RegisterRoutes(v1 *gin.RouterGroup) {
	userGroup := v1.Group("/users")
	userGroup.Use(middleware.JWTAuth(c.adminAPI.auth))
	{
		authHandler := handlers.NewAuthHandler(c.adminAPI.services.Auth, c.adminAPI.database)
		c.adminAPI.logger.Info("注册用户管理路由", "prefix", "/api/v1/users")

		// 用户管理操作
		userGroup.GET("", authHandler.ListUsers)                   // 获取用户列表
		userGroup.POST("", authHandler.CreateUser)                 // 创建用户
		userGroup.PUT("/:id/password", authHandler.UpdatePassword) // 更新用户密码
		userGroup.PUT("/:id/enable", authHandler.EnableUser)       // 启用用户
		userGroup.PUT("/:id/disable", authHandler.DisableUser)     // 禁用用户
		userGroup.DELETE("/:id", authHandler.DeleteUser)           // 删除用户

		c.adminAPI.logger.Info("用户管理路由注册完成", "routes", []string{
			"GET /api/v1/users",
			"POST /api/v1/users",
			"PUT /api/v1/users/:id/password",
			"PUT /api/v1/users/:id/enable",
			"PUT /api/v1/users/:id/disable",
			"DELETE /api/v1/users/:id",
		})
	}
}
