// 通用类型定义

// 菜单项类型
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  disabled?: boolean
}

// 表格列类型
export interface TableColumn {
  key: string
  title: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  render?: (row: any) => any
}

// 表单规则类型
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any) => boolean | Promise<boolean>
}

// 图表数据类型
export interface ChartData {
  name: string
  value: number
  color?: string
}

export interface LineChartData {
  time: string
  value: number
  category?: string
}

// 通知类型
export interface NotificationItem {
  id: string
  title: string
  content: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: string
  read: boolean
}

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'auto'

// 语言类型
export type Language = 'zh-CN' | 'en-US'

// 状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// 操作结果类型
export interface OperationResult {
  success: boolean
  message: string
  data?: any
}

// 分页参数类型
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
}

// 时间范围类型
export interface TimeRange {
  start: string
  end: string
}

// 文件上传类型
export interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  url?: string
  error?: string
}
