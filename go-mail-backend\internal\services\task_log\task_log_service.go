package task_log

import (
	"database/sql"
	"fmt"
	"go-mail/internal/database"
	"strings"
	"time"
)

// TaskLogService 任务日志数据库服务
type TaskLogService struct {
	db               *database.Database
	detailLogService *DetailLogService
}

// NewTaskLogService 创建任务日志服务
func NewTaskLogService(db *database.Database, detailLogService *DetailLogService) *TaskLogService {
	return &TaskLogService{
		db:               db,
		detailLogService: detailLogService,
	}
}

// CreateTaskLog 创建任务日志记录
func (s *TaskLogService) CreateTaskLog(req database.CreateTaskLogRequest) (*database.TaskLog, error) {
	// 创建详细日志文件
	detailLogPath, err := s.detailLogService.CreateLogFile(req.TaskID, req.Email)
	if err != nil {
		return nil, fmt.Errorf("创建详细日志文件失败: %w", err)
	}

	// 插入数据库记录
	insertSQL := `
		INSERT INTO task_logs (
			task_id, operation_type, email, status, start_time, 
			batch_id, detail_log_path, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	_, err = s.db.GetDB().Exec(insertSQL,
		req.TaskID, req.OperationType, req.Email, req.Status, req.StartTime,
		req.BatchID, detailLogPath, now, now)

	if err != nil {
		return nil, fmt.Errorf("插入任务日志失败: %w", err)
	}

	// 查询并返回创建的记录
	return s.GetTaskLogByTaskID(req.TaskID)
}

// UpdateTaskLog 更新任务日志
func (s *TaskLogService) UpdateTaskLog(taskID string, req database.UpdateTaskLogRequest) error {
	var setParts []string
	var args []interface{}

	if req.Status != nil {
		setParts = append(setParts, "status = ?")
		args = append(args, *req.Status)
	}

	if req.EndTime != nil {
		setParts = append(setParts, "end_time = ?")
		args = append(args, *req.EndTime)
	}

	if req.DurationMs != nil {
		setParts = append(setParts, "duration_ms = ?")
		args = append(args, *req.DurationMs)
	}

	if req.ErrorMessage != nil {
		setParts = append(setParts, "error_message = ?")
		args = append(args, *req.ErrorMessage)
	}

	if req.DetailLogPath != nil {
		setParts = append(setParts, "detail_log_path = ?")
		args = append(args, *req.DetailLogPath)
	}

	if len(setParts) == 0 {
		return fmt.Errorf("没有要更新的字段")
	}

	// 添加 updated_at
	setParts = append(setParts, "updated_at = ?")
	args = append(args, time.Now())

	// 添加 WHERE 条件
	args = append(args, taskID)

	updateSQL := fmt.Sprintf("UPDATE task_logs SET %s WHERE task_id = ?", strings.Join(setParts, ", "))

	_, err := s.db.GetDB().Exec(updateSQL, args...)
	if err != nil {
		return fmt.Errorf("更新任务日志失败: %w", err)
	}

	return nil
}

// GetTaskLogByTaskID 根据任务ID获取任务日志
func (s *TaskLogService) GetTaskLogByTaskID(taskID string) (*database.TaskLog, error) {
	querySQL := `
		SELECT id, task_id, operation_type, email, status, start_time, 
			   end_time, duration_ms, error_message, batch_id, detail_log_path,
			   created_at, updated_at
		FROM task_logs 
		WHERE task_id = ?`

	var taskLog database.TaskLog
	err := s.db.GetDB().QueryRow(querySQL, taskID).Scan(
		&taskLog.ID, &taskLog.TaskID, &taskLog.OperationType, &taskLog.Email,
		&taskLog.Status, &taskLog.StartTime, &taskLog.EndTime, &taskLog.DurationMs,
		&taskLog.ErrorMessage, &taskLog.BatchID, &taskLog.DetailLogPath,
		&taskLog.CreatedAt, &taskLog.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("任务日志不存在")
		}
		return nil, fmt.Errorf("查询任务日志失败: %w", err)
	}

	return &taskLog, nil
}

// GetTaskLogByID 根据ID获取任务日志
func (s *TaskLogService) GetTaskLogByID(id int) (*database.TaskLog, error) {
	querySQL := `
		SELECT id, task_id, operation_type, email, status, start_time, 
			   end_time, duration_ms, error_message, batch_id, detail_log_path,
			   created_at, updated_at
		FROM task_logs 
		WHERE id = ?`

	var taskLog database.TaskLog
	err := s.db.GetDB().QueryRow(querySQL, id).Scan(
		&taskLog.ID, &taskLog.TaskID, &taskLog.OperationType, &taskLog.Email,
		&taskLog.Status, &taskLog.StartTime, &taskLog.EndTime, &taskLog.DurationMs,
		&taskLog.ErrorMessage, &taskLog.BatchID, &taskLog.DetailLogPath,
		&taskLog.CreatedAt, &taskLog.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("任务日志不存在")
		}
		return nil, fmt.Errorf("查询任务日志失败: %w", err)
	}

	return &taskLog, nil
}

// GetTaskLogs 获取任务日志列表
func (s *TaskLogService) GetTaskLogs(filter database.TaskLogFilter) (*database.TaskLogResponse, error) {
	// 构建查询条件
	var whereParts []string
	var args []interface{}

	if filter.StartTime != nil {
		whereParts = append(whereParts, "start_time >= ?")
		args = append(args, *filter.StartTime)
	}

	if filter.EndTime != nil {
		whereParts = append(whereParts, "start_time <= ?")
		args = append(args, *filter.EndTime)
	}

	if filter.OperationType != nil {
		whereParts = append(whereParts, "operation_type = ?")
		args = append(args, *filter.OperationType)
	}

	if filter.Status != nil {
		whereParts = append(whereParts, "status = ?")
		args = append(args, *filter.Status)
	}

	if filter.Email != nil && *filter.Email != "" {
		whereParts = append(whereParts, "email LIKE ?")
		args = append(args, "%"+*filter.Email+"%")
	}

	if filter.BatchID != nil {
		whereParts = append(whereParts, "batch_id = ?")
		args = append(args, *filter.BatchID)
	}

	whereClause := ""
	if len(whereParts) > 0 {
		whereClause = "WHERE " + strings.Join(whereParts, " AND ")
	}

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM task_logs %s", whereClause)
	var total int
	err := s.db.GetDB().QueryRow(countSQL, args...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("查询总数失败: %w", err)
	}

	// 分页参数
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PageSize <= 0 {
		filter.PageSize = 20
	}

	offset := (filter.Page - 1) * filter.PageSize

	// 查询数据
	querySQL := fmt.Sprintf(`
		SELECT id, task_id, operation_type, email, status, start_time, 
			   end_time, duration_ms, error_message, batch_id, detail_log_path,
			   created_at, updated_at
		FROM task_logs %s 
		ORDER BY start_time DESC 
		LIMIT ? OFFSET ?`, whereClause)

	args = append(args, filter.PageSize, offset)

	rows, err := s.db.GetDB().Query(querySQL, args...)
	if err != nil {
		return nil, fmt.Errorf("查询任务日志失败: %w", err)
	}
	defer rows.Close()

	var taskLogs []database.TaskLog
	for rows.Next() {
		var taskLog database.TaskLog
		err := rows.Scan(
			&taskLog.ID, &taskLog.TaskID, &taskLog.OperationType, &taskLog.Email,
			&taskLog.Status, &taskLog.StartTime, &taskLog.EndTime, &taskLog.DurationMs,
			&taskLog.ErrorMessage, &taskLog.BatchID, &taskLog.DetailLogPath,
			&taskLog.CreatedAt, &taskLog.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("扫描任务日志失败: %w", err)
		}
		taskLogs = append(taskLogs, taskLog)
	}

	return &database.TaskLogResponse{
		Total: total,
		Page:  filter.Page,
		Size:  len(taskLogs),
		Items: taskLogs,
	}, nil
}

// GetTaskLogDetail 获取任务详细日志
func (s *TaskLogService) GetTaskLogDetail(id int) (*database.TaskLogDetailResponse, error) {
	// 获取任务日志基本信息
	taskLog, err := s.GetTaskLogByID(id)
	if err != nil {
		return nil, err
	}

	// 读取详细日志文件
	var detail database.TaskLogDetail
	if taskLog.DetailLogPath != nil && *taskLog.DetailLogPath != "" {
		detailPtr, err := s.detailLogService.ReadLogFile(*taskLog.DetailLogPath)
		if err != nil {
			return nil, fmt.Errorf("读取详细日志失败: %w", err)
		}
		detail = *detailPtr
	}

	return &database.TaskLogDetailResponse{
		TaskLog: *taskLog,
		Detail:  detail,
	}, nil
}
