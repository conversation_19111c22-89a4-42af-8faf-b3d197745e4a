package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	adminAPI *AdminAPI
}

// NewAuthController 创建认证控制器
func NewAuthController(adminAPI *AdminAPI) *AuthController {
	return &AuthController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册认证路由
func (c *AuthController) RegisterRoutes(v1 *gin.RouterGroup) {
	authGroup := v1.Group("/auth")
	{
		authHandler := handlers.NewAuthHandler(c.adminAPI.services.Auth, c.adminAPI.database)
		c.adminAPI.logger.Info("注册认证路由", "prefix", "/api/v1/auth")
		authGroup.POST("/login", authHandler.Login)
		authGroup.POST("/refresh", authHandler.RefreshToken)
		authGroup.POST("/logout", middleware.JWTAuth(c.adminAPI.auth), authHandler.Logout)
		authGroup.GET("/validate", middleware.JWTAuth(c.adminAPI.auth), authHandler.ValidateToken)
		authGroup.GET("/me", middleware.JWTAuth(c.adminAPI.auth), authHandler.GetProfile)
		c.adminAPI.logger.Info("认证路由注册完成", "routes", []string{
			"POST /api/v1/auth/login",
			"POST /api/v1/auth/refresh",
			"POST /api/v1/auth/logout",
			"GET /api/v1/auth/validate",
			"GET /api/v1/auth/me",
		})
	}
}
