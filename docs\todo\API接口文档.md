# Go-Mail 临时邮箱服务平台 - API接口文档

## 📋 API概览

**基础URL**: `http://localhost:8080/api/v1`  
**认证方式**: JWT Bearer <PERSON>ken (管理后台) / 激活码认证 (客户端)  
**响应格式**: JSON  
**字符编码**: UTF-8  

## 🔐 认证说明

### 管理后台认证
- 使用JWT Bearer Token
- 在请求头中添加: `Authorization: Bearer <token>`
- Token有效期: 24小时
- 刷新Token有效期: 7天

### 客户端认证
- 使用激活码 + 设备指纹
- 在请求头中添加: `X-Activation-Code: <code>`
- 设备信息在请求体中提供

## 📊 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "error": "",
  "timestamp": "2025-01-20T12:00:00Z",
  "request_id": "req_123456789"
}
```

---

## 🔑 认证接口 (Auth)

### 1. 管理员登录
**POST** `/auth/login`

**请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 86400,
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin"
    }
  }
}
```

### 2. 刷新令牌
**POST** `/auth/refresh`

**请求体**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 3. 登出
**POST** `/auth/logout`
- 需要JWT认证

### 4. 获取用户信息
**GET** `/auth/profile`
- 需要JWT认证

---

## 🎫 激活码管理接口 (Activation)

### 1. 批量生成激活码
**POST** `/activation/generate`
- 需要JWT认证

**请求体**:
```json
{
  "count": 100,
  "expiry_days": 30,
  "prefix": "GOMAIL",
  "description": "测试批次"
}
```

### 2. 激活码列表
**GET** `/activation/list?page=1&size=20&status=unused`
- 需要JWT认证

### 3. 验证激活码
**POST** `/activation/verify`
- 需要JWT认证

**请求体**:
```json
{
  "code": "GOMAIL-ABC123",
  "device_info": {
    "system_uuid": "uuid",
    "username": "user",
    "computer_name": "PC",
    "platform": "windows",
    "mac_address": "00:11:22:33:44:55"
  }
}
```

### 4. 删除激活码
**DELETE** `/activation/{id}`
- 需要JWT认证

---

## 📧 客户端邮箱接口 (Client)

### 1. 激活码验证
**POST** `/client/activation/verify`
- 需要激活码认证

**请求体**:
```json
{
  "device_info": {
    "system_uuid": "uuid",
    "username": "user", 
    "computer_name": "PC",
    "platform": "windows",
    "mac_address": "00:11:22:33:44:55"
  }
}
```

### 2. 分配临时邮箱
**POST** `/client/mailbox/allocate`
- 需要激活码认证

**请求体**:
```json
{
  "preferences": {
    "domain_suffix": "@mail.com",
    "alias_prefix": "temp"
  }
}
```

**响应**:
```json
{
  "code": 200,
  "message": "邮箱分配成功",
  "data": {
    "mailbox_id": 123,
    "email_address": "<EMAIL>",
    "expires_at": "2025-01-21T12:00:00Z",
    "status": "active"
  }
}
```

### 3. 查询邮件
**POST** `/client/mailbox/query`
- 需要激活码认证

**请求体**:
```json
{
  "mailbox_id": 123,
  "since": "2025-01-20T00:00:00Z"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "mails": [
      {
        "mail_id": "mail_001",
        "subject": "测试邮件",
        "from": "<EMAIL>",
        "date": "2025-01-20T10:30:00Z",
        "preview": "邮件预览内容..."
      }
    ],
    "total": 1
  }
}
```

### 4. 获取邮件详情
**POST** `/client/mailbox/mail/detail`
- 需要激活码认证

**请求体**:
```json
{
  "mailbox_id": 123,
  "mail_id": "mail_001"
}
```

### 5. 释放邮箱
**POST** `/client/mailbox/release`
- 需要激活码认证

### 6. 邮箱状态
**POST** `/client/mailbox/status`
- 需要激活码认证

---

## 📊 系统监控接口 (Monitor)

### 1. 系统统计
**GET** `/monitor/statistics`
- 需要JWT认证

**响应**:
```json
{
  "code": 200,
  "data": {
    "accounts": {
      "total": 10,
      "active": 8,
      "inactive": 2
    },
    "mailboxes": {
      "total_allocated": 150,
      "currently_active": 45,
      "today_allocated": 12,
      "today_released": 8
    },
    "activation_codes": {
      "total_generated": 1000,
      "used": 150,
      "unused": 800,
      "expired": 50
    }
  }
}
```

### 2. 账户状态
**GET** `/monitor/accounts`
- 需要JWT认证

### 3. 会话状态
**GET** `/monitor/sessions`
- 需要JWT认证

### 4. 系统日志
**GET** `/monitor/logs?level=error&start_time=2025-01-20T00:00:00Z&limit=100`
- 需要JWT认证

### 5. 健康检查
**GET** `/monitor/health`
- 需要JWT认证

### 6. 定时任务状态
**GET** `/monitor/tasks`
- 需要JWT认证

**响应**:
```json
{
  "code": 200,
  "data": {
    "scheduler_running": true,
    "task_count": 5,
    "tasks": {
      "mailbox_cleanup": {
        "name": "mailbox_cleanup",
        "last_run": "2025-01-20T12:00:00Z",
        "next_run": "2025-01-20T12:01:00Z",
        "run_count": 720,
        "error_count": 0,
        "is_running": false,
        "enabled": true
      }
    }
  }
}
```

### 7. 控制定时任务
**POST** `/monitor/tasks/{task}/{action}`
- 需要JWT认证
- action: enable | disable

---

## ⚙️ 配置管理接口 (Config)

### 1. 获取系统配置
**GET** `/config`
- 需要JWT认证

### 2. 更新系统配置
**PUT** `/config`
- 需要JWT认证

**请求体**:
```json
{
  "mailbox": {
    "default_expiry_hours": 24,
    "max_per_activation": 5,
    "cleanup_interval_minutes": 60
  },
  "activation": {
    "default_expiry_days": 30,
    "max_device_bindings": 1
  }
}
```

### 3. 获取单个配置项
**GET** `/config/{key}`
- 需要JWT认证

### 4. 设置单个配置项
**PUT** `/config/{key}`
- 需要JWT认证

**请求体**:
```json
{
  "value": "24",
  "type": "int",
  "category": "mailbox"
}
```

### 5. 配置项列表
**GET** `/config/items?category=mailbox`
- 需要JWT认证

### 6. 重置为默认配置
**POST** `/config/reset`
- 需要JWT认证

**请求体**:
```json
{
  "confirm": true
}
```

---

## 🔧 其他接口

### 健康检查
**GET** `/health`
- 无需认证

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-20T12:00:00Z",
  "version": "1.0.0",
  "uptime": "2h30m15s"
}
```

---

## 📝 错误码说明

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 200 | 操作成功 | 200 |
| 400 | 请求参数错误 | 400 |
| 401 | 未授权访问 | 401 |
| 403 | 禁止访问 | 403 |
| 404 | 资源不存在 | 404 |
| 500 | 服务器内部错误 | 500 |
| 503 | 服务不可用 | 503 |
| 1001 | 激活码不存在 | 400 |
| 1002 | 激活码无效 | 400 |
| 1003 | 激活码已过期 | 400 |
| 1004 | 设备指纹不匹配 | 400 |
| 2001 | 邮箱分配失败 | 400 |
| 2002 | 邮箱不存在 | 404 |
| 3001 | 账户不存在 | 404 |
| 4001 | 数据库错误 | 500 |

---

## 🧪 测试示例

### 使用curl测试

```bash
# 1. 管理员登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 2. 生成激活码
curl -X POST http://localhost:8080/api/v1/activation/generate \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"count":10,"expiry_days":30}'

# 3. 客户端验证激活码
curl -X POST http://localhost:8080/api/v1/client/activation/verify \
  -H "X-Activation-Code: GOMAIL-ABC123" \
  -H "Content-Type: application/json" \
  -d '{"device_info":{"system_uuid":"test","username":"user","computer_name":"PC","platform":"windows","mac_address":"00:11:22:33:44:55"}}'
```

---

**API文档版本**: v1.0.0  
**最后更新**: 2025-01-20  
**维护者**: 86
