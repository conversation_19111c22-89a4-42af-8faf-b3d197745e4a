package scheduler

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"
)

// Task 定时任务接口
type Task interface {
	Execute(ctx context.Context) error
	GetInterval() time.Duration
	GetName() string
	IsEnabled() bool
}

// TaskStatus 任务状态
type TaskStatus struct {
	Name       string    `json:"name"`
	LastRun    time.Time `json:"last_run"`
	NextRun    time.Time `json:"next_run"`
	RunCount   int64     `json:"run_count"`
	ErrorCount int64     `json:"error_count"`
	LastError  string    `json:"last_error,omitempty"`
	IsRunning  bool      `json:"is_running"`
	Enabled    bool      `json:"enabled"`
}

// Scheduler 任务调度器
type Scheduler struct {
	tasks    []Task
	statuses map[string]*TaskStatus
	running  bool
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
	mu       sync.RWMutex
	logger   *slog.Logger
}

// NewScheduler 创建新的任务调度器
func NewScheduler(logger *slog.Logger) *Scheduler {
	if logger == nil {
		logger = slog.Default()
	}

	return &Scheduler{
		tasks:    make([]Task, 0),
		statuses: make(map[string]*TaskStatus),
		logger:   logger,
	}
}

// AddTask 添加任务
func (s *Scheduler) AddTask(task Task) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.tasks = append(s.tasks, task)
	s.statuses[task.GetName()] = &TaskStatus{
		Name:    task.GetName(),
		NextRun: time.Now().Add(task.GetInterval()),
		Enabled: task.IsEnabled(),
	}

	s.logger.Info("添加定时任务",
		"task", task.GetName(),
		"interval", task.GetInterval(),
		"enabled", task.IsEnabled())
}

// Start 启动调度器
func (s *Scheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("调度器已经在运行")
	}

	s.ctx, s.cancel = context.WithCancel(ctx)
	s.running = true

	s.logger.Info("启动任务调度器", "task_count", len(s.tasks))

	// 为每个任务启动独立的goroutine
	for _, task := range s.tasks {
		if task.IsEnabled() {
			s.wg.Add(1)
			go s.runTask(task)
		}
	}

	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return
	}

	s.logger.Info("停止任务调度器")
	s.cancel()
	s.wg.Wait()
	s.running = false
}

// runTask 运行单个任务
func (s *Scheduler) runTask(task Task) {
	defer s.wg.Done()

	taskName := task.GetName()
	interval := task.GetInterval()
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	s.logger.Info("启动任务",
		"task", taskName,
		"interval", interval)

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info("任务停止", "task", taskName)
			return

		case <-ticker.C:
			if !task.IsEnabled() {
				continue
			}

			s.executeTask(task)
		}
	}
}

// executeTask 执行任务
func (s *Scheduler) executeTask(task Task) {
	taskName := task.GetName()

	s.mu.Lock()
	status := s.statuses[taskName]
	if status.IsRunning {
		s.mu.Unlock()
		s.logger.Warn("任务正在运行，跳过本次执行", "task", taskName)
		return
	}
	status.IsRunning = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		status.IsRunning = false
		status.LastRun = time.Now()
		status.NextRun = time.Now().Add(task.GetInterval())
		status.RunCount++
		s.mu.Unlock()
	}()

	s.logger.Debug("开始执行任务", "task", taskName)
	start := time.Now()

	// 执行任务
	if err := task.Execute(s.ctx); err != nil {
		s.mu.Lock()
		status.ErrorCount++
		status.LastError = err.Error()
		s.mu.Unlock()

		s.logger.Error("任务执行失败",
			"task", taskName,
			"error", err,
			"duration", time.Since(start))
	} else {
		s.mu.Lock()
		status.LastError = ""
		s.mu.Unlock()

		s.logger.Debug("任务执行成功",
			"task", taskName,
			"duration", time.Since(start))
	}
}

// GetTaskStatus 获取任务状态
func (s *Scheduler) GetTaskStatus(taskName string) (*TaskStatus, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	status, exists := s.statuses[taskName]
	if !exists {
		return nil, false
	}

	// 返回副本避免并发问题
	statusCopy := *status
	return &statusCopy, true
}

// GetAllTaskStatus 获取所有任务状态
func (s *Scheduler) GetAllTaskStatus() map[string]*TaskStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result := make(map[string]*TaskStatus)
	for name, status := range s.statuses {
		statusCopy := *status
		result[name] = &statusCopy
	}

	return result
}

// GetAllTaskStatusArray 获取所有任务状态（数组格式）
func (s *Scheduler) GetAllTaskStatusArray() []*TaskStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result := make([]*TaskStatus, 0, len(s.statuses))
	for _, status := range s.statuses {
		statusCopy := *status
		result = append(result, &statusCopy)
	}

	return result
}

// IsRunning 检查调度器是否在运行
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// GetTaskCount 获取任务数量
func (s *Scheduler) GetTaskCount() int {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return len(s.tasks)
}

// EnableTask 启用任务
func (s *Scheduler) EnableTask(taskName string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	status, exists := s.statuses[taskName]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskName)
	}

	status.Enabled = true
	s.logger.Info("启用任务", "task", taskName)
	return nil
}

// DisableTask 禁用任务
func (s *Scheduler) DisableTask(taskName string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	status, exists := s.statuses[taskName]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskName)
	}

	status.Enabled = false
	s.logger.Info("禁用任务", "task", taskName)
	return nil
}

// RunTask 手动执行任务
func (s *Scheduler) RunTask(taskName string) error {
	s.mu.RLock()
	var task Task
	for _, t := range s.tasks {
		if t.GetName() == taskName {
			task = t
			break
		}
	}
	s.mu.RUnlock()

	if task == nil {
		return fmt.Errorf("任务不存在: %s", taskName)
	}

	s.logger.Info("手动执行任务", "task", taskName)

	// 在新的 goroutine 中执行任务，避免阻塞
	go func() {
		s.executeTask(task)
	}()

	return nil
}

// BaseTask 基础任务结构
type BaseTask struct {
	name     string
	interval time.Duration
	enabled  bool
}

// NewBaseTask 创建基础任务
func NewBaseTask(name string, interval time.Duration) *BaseTask {
	return &BaseTask{
		name:     name,
		interval: interval,
		enabled:  true,
	}
}

// GetName 获取任务名称
func (t *BaseTask) GetName() string {
	return t.name
}

// GetInterval 获取执行间隔
func (t *BaseTask) GetInterval() time.Duration {
	return t.interval
}

// IsEnabled 检查任务是否启用
func (t *BaseTask) IsEnabled() bool {
	return t.enabled
}

// SetEnabled 设置任务启用状态
func (t *BaseTask) SetEnabled(enabled bool) {
	t.enabled = enabled
}
