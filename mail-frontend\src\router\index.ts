import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true,
    },
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      requiresAuth: true,
    },
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '系统概览',
          icon: 'dashboard',
          requiresAuth: true,
        },
      },
      {
        path: '/activation-codes',
        name: 'ActivationCodes',
        component: () => import('@/views/ActivationCodes.vue'),
        meta: {
          title: '激活管理',
          icon: 'key',
          requiresAuth: true,
        },
      },
      {
        path: '/system-monitor',
        name: 'SystemMonitor',
        component: () => import('@/views/SystemMonitor.vue'),
        meta: {
          title: '系统监控',
          icon: 'monitor',
          requiresAuth: true,
        },
      },
      {
        path: '/system-config',
        name: 'SystemConfig',
        component: () => import('@/views/SystemConfig.vue'),
        meta: {
          title: '系统配置',
          icon: 'settings',
          requiresAuth: true,
        },
      },
      {
        path: '/mailbox-management',
        name: 'MailboxManagement',
        component: () => import('@/views/MailboxManagement.vue'),
        meta: {
          title: '邮箱管理',
          icon: 'mail',
          requiresAuth: true,
        },
      },
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: {
          title: '个人设置',
          icon: 'person',
          requiresAuth: true,
          hideInMenu: true,
        },
      },
    ],
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true,
    },
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()
  const appStore = useAppStore()

  // 设置页面标题
  if (to.meta.title) {
    appStore.setTitle(`${to.meta.title} - Go-Mail管理后台`)
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果未认证，尝试初始化认证状态
    if (!authStore.isAuthenticated) {
      const initialized = await authStore.initializeAuth()
      if (!initialized) {
        // 认证失败，重定向到登录页
        next({
          path: '/login',
          query: { redirect: to.fullPath },
        })
        return
      }
    }
  }

  // 如果已认证且访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等逻辑
  console.log(`Route changed: ${from.path} -> ${to.path}`)
})

export default router
