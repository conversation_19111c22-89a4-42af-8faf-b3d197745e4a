<template>
  <div class="system-config">
    <div class="page-header">
      <h1>系统配置</h1>
      <p>管理系统的各项配置参数</p>
    </div>

    <n-card title="基础配置">
      <n-form
        ref="configFormRef"
        :model="configForm"
        :rules="configFormRules"
        label-placement="left"
        label-width="200px"
      >
        <n-form-item label="邮箱过期时间(分钟)" path="mailboxExpirationMinutes">
          <n-input-number
            v-model:value="configForm.mailboxExpirationMinutes"
            :min="1"
            :max="1440"
            placeholder="请输入邮箱过期时间"
            style="width: 200px"
          />
          <span class="form-help">邮箱分配后的有效时间，超时后自动释放</span>
        </n-form-item>

        <n-form-item label="单码最大邮箱数" path="maxMailboxesPerCode">
          <n-input-number
            v-model:value="configForm.maxMailboxesPerCode"
            :min="1"
            :max="100"
            placeholder="请输入最大邮箱数"
            style="width: 200px"
          />
          <span class="form-help">每个激活码最多可以分配的邮箱数量</span>
        </n-form-item>

        <n-form-item label="清理间隔(分钟)" path="cleanupIntervalMinutes">
          <n-input-number
            v-model:value="configForm.cleanupIntervalMinutes"
            :min="1"
            :max="1440"
            placeholder="请输入清理间隔"
            style="width: 200px"
          />
          <span class="form-help">系统自动清理过期数据的时间间隔</span>
        </n-form-item>

        <n-form-item label="启用自动清理" path="enableAutoCleanup">
          <n-switch v-model:value="configForm.enableAutoCleanup" />
          <span class="form-help">是否启用定时清理过期邮箱和数据</span>
        </n-form-item>

        <n-form-item label="单箱最大邮件数" path="maxMailsPerMailbox">
          <n-input-number
            v-model:value="configForm.maxMailsPerMailbox"
            :min="1"
            :max="1000"
            placeholder="请输入最大邮件数"
            style="width: 200px"
          />
          <span class="form-help">每个邮箱最多保存的邮件数量</span>
        </n-form-item>

        <n-form-item label="启用邮件通知" path="enableEmailNotifications">
          <n-switch v-model:value="configForm.enableEmailNotifications" />
          <span class="form-help">是否启用系统邮件通知功能</span>
        </n-form-item>

        <n-form-item>
          <n-space>
            <n-button type="primary" :loading="saveLoading" @click="saveConfig">
              保存配置
            </n-button>
            <n-button @click="resetConfig">重置</n-button>
            <n-button @click="loadDefaultConfig">恢复默认</n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 系统操作 -->
    <n-card title="系统操作" class="operations-card">
      <div class="operations-grid">
        <div class="operation-item">
          <h3>数据清理</h3>
          <p>清理过期的邮箱和邮件数据</p>
          <n-button type="warning" @click="showCleanupModal = true">
            执行清理
          </n-button>
        </div>

        <div class="operation-item">
          <h3>数据备份</h3>
          <p>备份系统数据到本地文件</p>
          <n-button type="info" :loading="backupLoading" @click="backupData">
            创建备份
          </n-button>
        </div>

        <div class="operation-item">
          <h3>系统重启</h3>
          <p>重启系统服务（谨慎操作）</p>
          <n-button type="error" @click="showRestartModal = true">
            重启系统
          </n-button>
        </div>
      </div>
    </n-card>

    <!-- 清理确认模态框 -->
    <n-modal v-model:show="showCleanupModal" preset="dialog" title="数据清理">
      <template #default>
        <p>请选择要清理的数据类型：</p>
        <n-checkbox-group v-model:value="cleanupOptions">
          <n-space vertical>
            <n-checkbox value="cleanExpiredMailboxes">清理过期邮箱</n-checkbox>
            <n-checkbox value="cleanOldLogs">清理旧日志</n-checkbox>
            <n-checkbox value="cleanTempFiles">清理临时文件</n-checkbox>
          </n-space>
        </n-checkbox-group>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showCleanupModal = false">取消</n-button>
          <n-button type="warning" :loading="cleanupLoading" @click="executeCleanup">
            确认清理
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 重启确认模态框 -->
    <n-modal v-model:show="showRestartModal" preset="dialog" title="系统重启">
      <template #default>
        <n-alert type="warning" title="警告">
          系统重启将中断所有正在进行的操作，请确认是否继续？
        </n-alert>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showRestartModal = false">取消</n-button>
          <n-button type="error" @click="restartSystem">
            确认重启
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  NCard,
  NForm,
  NFormItem,
  NInputNumber,
  NSwitch,
  NButton,
  NSpace,
  NModal,
  NCheckboxGroup,
  NCheckbox,
  NAlert,
  useMessage,
  type FormInst,
  type FormRules,
} from 'naive-ui'
import { systemApi } from '@/api/system'
import type { SystemConfig } from '@/types/api'

const message = useMessage()

// 响应式数据
const saveLoading = ref(false)
const backupLoading = ref(false)
const cleanupLoading = ref(false)
const showCleanupModal = ref(false)
const showRestartModal = ref(false)
const cleanupOptions = ref<string[]>([])

// 表单引用
const configFormRef = ref<FormInst | null>(null)

// 配置表单数据
const configForm = reactive<SystemConfig>({
  mailboxExpirationMinutes: 30,
  maxMailboxesPerCode: 5,
  cleanupIntervalMinutes: 60,
  enableAutoCleanup: true,
  maxMailsPerMailbox: 100,
  enableEmailNotifications: false,
})

// 表单验证规则
const configFormRules: FormRules = {
  mailboxExpirationMinutes: [
    {
      required: true,
      type: 'number',
      message: '请输入邮箱过期时间',
      trigger: ['input', 'blur'],
    },
    {
      type: 'number',
      min: 1,
      max: 1440,
      message: '过期时间应在1-1440分钟之间',
      trigger: ['input', 'blur'],
    },
  ],
  maxMailboxesPerCode: [
    {
      required: true,
      type: 'number',
      message: '请输入最大邮箱数',
      trigger: ['input', 'blur'],
    },
    {
      type: 'number',
      min: 1,
      max: 100,
      message: '最大邮箱数应在1-100之间',
      trigger: ['input', 'blur'],
    },
  ],
  cleanupIntervalMinutes: [
    {
      required: true,
      type: 'number',
      message: '请输入清理间隔',
      trigger: ['input', 'blur'],
    },
    {
      type: 'number',
      min: 1,
      max: 1440,
      message: '清理间隔应在1-1440分钟之间',
      trigger: ['input', 'blur'],
    },
  ],
  maxMailsPerMailbox: [
    {
      required: true,
      type: 'number',
      message: '请输入最大邮件数',
      trigger: ['input', 'blur'],
    },
    {
      type: 'number',
      min: 1,
      max: 1000,
      message: '最大邮件数应在1-1000之间',
      trigger: ['input', 'blur'],
    },
  ],
}

// 获取系统配置
const fetchSystemConfig = async () => {
  try {
    const response = await systemApi.getSystemConfig()
    if (response.success && response.data) {
      Object.assign(configForm, response.data)
    }
  } catch (error) {
    console.error('Failed to fetch system config:', error)
    message.error('获取系统配置失败')
  }
}

// 保存配置
const saveConfig = async () => {
  if (!configFormRef.value) return

  try {
    await configFormRef.value.validate()
    saveLoading.value = true

    // 逐个更新配置项
    for (const [key, value] of Object.entries(configForm)) {
      await systemApi.updateSystemConfig({
        key,
        value,
        description: `更新${key}配置`,
      })
    }

    message.success('配置保存成功')
  } catch (error) {
    console.error('Failed to save config:', error)
    message.error('配置保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 重置配置
const resetConfig = () => {
  fetchSystemConfig()
  message.info('配置已重置')
}

// 恢复默认配置
const loadDefaultConfig = async () => {
  try {
    await systemApi.resetSystemConfig()
    await fetchSystemConfig()
    message.success('已恢复默认配置')
  } catch (error) {
    console.error('Failed to reset config:', error)
    message.error('恢复默认配置失败')
  }
}

// 执行数据清理
const executeCleanup = async () => {
  if (cleanupOptions.value.length === 0) {
    message.warning('请选择要清理的数据类型')
    return
  }

  try {
    cleanupLoading.value = true
    
    const options = {
      cleanExpiredMailboxes: cleanupOptions.value.includes('cleanExpiredMailboxes'),
      cleanOldLogs: cleanupOptions.value.includes('cleanOldLogs'),
      cleanTempFiles: cleanupOptions.value.includes('cleanTempFiles'),
    }

    await systemApi.cleanupSystem(options)
    message.success('数据清理完成')
    showCleanupModal.value = false
    cleanupOptions.value = []
  } catch (error) {
    console.error('Failed to cleanup system:', error)
    message.error('数据清理失败')
  } finally {
    cleanupLoading.value = false
  }
}

// 备份数据
const backupData = async () => {
  try {
    backupLoading.value = true
    const response = await systemApi.backupSystem()
    
    if (response.success && response.data) {
      message.success('备份创建成功')
      // 这里可以触发下载
      if (response.data.downloadUrl) {
        window.open(response.data.downloadUrl, '_blank')
      }
    }
  } catch (error) {
    console.error('Failed to backup system:', error)
    message.error('备份创建失败')
  } finally {
    backupLoading.value = false
  }
}

// 重启系统
const restartSystem = () => {
  message.warning('系统重启功能暂未实现')
  showRestartModal.value = false
}

// 组件挂载时获取配置
onMounted(() => {
  fetchSystemConfig()
})
</script>

<style scoped>
.system-config {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.form-help {
  margin-left: 12px;
  font-size: 12px;
  color: var(--n-text-color-3);
}

.operations-card {
  margin-top: 24px;
}

.operations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.operation-item {
  text-align: center;
  padding: 24px;
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
}

.operation-item h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.operation-item p {
  margin: 0 0 16px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operations-grid {
    grid-template-columns: 1fr;
  }
}
</style>
