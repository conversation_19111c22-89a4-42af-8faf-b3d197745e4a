<template>
  <n-modal v-model:show="visible" preset="dialog" title="批量导入邮箱" style="width: 800px">
    <div class="batch-import-modal">
      <n-steps :current="currentStep" :status="stepStatus">
        <n-step title="输入数据" />
        <n-step title="预览确认" />
        <n-step title="导入结果" />
      </n-steps>

      <!-- 步骤1: 输入数据 -->
      <div v-if="currentStep === 1" class="step-content">
        <n-alert type="info" class="mb-4">
          <template #header>导入格式说明</template>
          <div>
            <p>每行一个邮箱账户，格式：<code>邮箱地址----密码</code></p>
            <p>支持的分隔符：<code>----</code>、<code>---</code>、<code>--</code>、制表符、空格</p>
            <p>示例：</p>
            <pre>
<EMAIL>----password123
<EMAIL>----password456</pre
            >
          </div>
        </n-alert>

        <n-form ref="formRef" :model="formData" :rules="rules">
          <n-form-item label="导入数据" path="importText">
            <n-input
              v-model:value="formData.importText"
              type="textarea"
              placeholder="请输入邮箱账户数据，每行一个..."
              :rows="10"
              show-count
            />
          </n-form-item>

          <n-form-item label="导入来源" path="source">
            <n-input v-model:value="formData.source" placeholder="例如：客户提供、爬虫获取等" />
          </n-form-item>

          <n-form-item label="标签">
            <n-dynamic-tags v-model:value="formData.tags" />
          </n-form-item>

          <n-form-item label="描述">
            <n-input
              v-model:value="formData.description"
              placeholder="可选，描述本次导入的目的或备注"
            />
          </n-form-item>

          <n-form-item>
            <n-checkbox v-model:checked="formData.autoVerify"> 导入后自动验证邮箱 </n-checkbox>
          </n-form-item>
        </n-form>

        <div class="step-actions">
          <n-space justify="end">
            <n-button @click="visible = false">取消</n-button>
            <n-button type="primary" @click="parseAndPreview" :loading="parsing">
              解析预览
            </n-button>
          </n-space>
        </div>
      </div>

      <!-- 步骤2: 预览确认 -->
      <div v-if="currentStep === 2" class="step-content">
        <n-alert :type="previewData.summary.invalid > 0 ? 'warning' : 'success'" class="mb-4">
          <template #header>解析结果</template>
          <div>
            <p>总计：{{ previewData.summary.total }} 行</p>
            <p>有效：{{ previewData.summary.valid }} 个账户</p>
            <p v-if="previewData.summary.invalid > 0" class="text-red-500">
              无效：{{ previewData.summary.invalid }} 行（格式错误）
            </p>
          </div>
        </n-alert>

        <!-- 有效账户预览 -->
        <div v-if="previewData.valid.length > 0" class="mb-4">
          <h4>有效账户预览（前10个）：</h4>
          <n-data-table
            :columns="previewColumns"
            :data="previewData.valid.slice(0, 10)"
            :pagination="false"
            size="small"
          />
          <p v-if="previewData.valid.length > 10" class="text-gray-500 mt-2">
            还有 {{ previewData.valid.length - 10 }} 个账户未显示...
          </p>
        </div>

        <!-- 无效数据提示 -->
        <div v-if="previewData.invalid.length > 0" class="mb-4">
          <h4 class="text-red-500">无效数据：</h4>
          <n-scrollbar style="max-height: 200px">
            <div
              v-for="(item, index) in previewData.invalid"
              :key="index"
              class="text-red-500 text-sm"
            >
              {{ item }}
            </div>
          </n-scrollbar>
        </div>

        <div class="step-actions">
          <n-space justify="end">
            <n-button @click="currentStep = 1">返回修改</n-button>
            <n-button
              type="primary"
              @click="confirmImport"
              :disabled="previewData.valid.length === 0"
              :loading="importing"
            >
              确认导入 ({{ previewData.valid.length }} 个账户)
            </n-button>
          </n-space>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 3" class="step-content">
        <div v-if="importResult" class="text-center">
          <n-result
            :status="importResult.success ? 'success' : 'error'"
            :title="importResult.success ? '导入成功' : '导入失败'"
            :description="importResult.message"
          >
            <template #footer>
              <div v-if="importResult.success && importResult.data">
                <p>操作ID：{{ importResult.data.operation_id }}</p>
                <p>总数量：{{ importResult.data.total_count }}</p>
                <p>状态：{{ importResult.data.status }}</p>

                <n-space justify="center" class="mt-4">
                  <n-button @click="viewProgress">查看进度</n-button>
                  <n-button type="primary" @click="resetAndClose">完成</n-button>
                </n-space>
              </div>
              <div v-else>
                <n-space justify="center" class="mt-4">
                  <n-button @click="currentStep = 1">重新导入</n-button>
                  <n-button @click="visible = false">关闭</n-button>
                </n-space>
              </div>
            </template>
          </n-result>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NModal,
  NSteps,
  NStep,
  NAlert,
  NForm,
  NFormItem,
  NInput,
  NDynamicTags,
  NCheckbox,
  NSpace,
  NButton,
  NDataTable,
  NScrollbar,
  NResult,
  useMessage,
} from 'naive-ui'
import {
  mailboxApi,
  type BatchImportRequest,
  type AccountImportData,
  type BatchOperationResponse,
} from '@/api/mailbox'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', result: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 响应式数据
const currentStep = ref(1)
const stepStatus = ref<'process' | 'finish' | 'error' | 'wait'>('process')
const parsing = ref(false)
const importing = ref(false)

const formData = reactive({
  importText: '',
  source: '批量导入',
  tags: [] as string[],
  description: '',
  autoVerify: true,
})

const previewData = ref<{
  valid: AccountImportData[]
  invalid: string[]
  summary: {
    total: number
    valid: number
    invalid: number
  }
}>({
  valid: [],
  invalid: [],
  summary: { total: 0, valid: 0, invalid: 0 },
})

const importResult = ref<{
  success: boolean
  message: string
  data?: BatchOperationResponse
} | null>(null)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

// 表单验证规则
const rules = {
  importText: {
    required: true,
    message: '请输入导入数据',
    trigger: 'blur',
  },
  source: {
    required: true,
    message: '请输入导入来源',
    trigger: 'blur',
  },
}

// 预览表格列
const previewColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_: any, index: number) => index + 1,
  },
  {
    title: '邮箱地址',
    key: 'email',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '密码',
    key: 'password',
    width: 120,
    render: (row: AccountImportData) => '●'.repeat(row.password.length),
  },
]

// 方法
const parseAndPreview = async () => {
  if (!formData.importText.trim()) {
    message.error('请输入导入数据')
    return
  }

  parsing.value = true
  try {
    // 解析导入文本
    const accounts = mailboxApi.parseBatchImportText(formData.importText)

    // 格式化预览数据
    previewData.value = mailboxApi.formatImportPreview(accounts)

    if (previewData.value.valid.length === 0) {
      message.error('没有解析到有效的账户数据，请检查格式')
      return
    }

    currentStep.value = 2
  } catch (error) {
    message.error('解析数据失败')
    console.error(error)
  } finally {
    parsing.value = false
  }
}

const confirmImport = async () => {
  importing.value = true
  try {
    const request: BatchImportRequest = {
      accounts: previewData.value.valid,
      source: formData.source,
      tags: formData.tags,
      auto_verify: formData.autoVerify,
      description: formData.description,
    }

    const response = await mailboxApi.batchImportAccounts(request)
    console.log('导入响应:', JSON.stringify(response))

    // 处理Axios响应包装 - 根据控制台日志，实际数据在response.data中
    const apiResponse = (response as any).data || response

    importResult.value = {
      success: (apiResponse as any).success,
      message: (apiResponse as any).message,
      data: (apiResponse as any).data,
    }
    console.log('导入结果:', JSON.stringify(importResult.value))

    currentStep.value = 3
    stepStatus.value = (apiResponse as any).success ? 'finish' : 'error'

    if ((apiResponse as any).success) {
      emit('success', (apiResponse as any).data)
    }
  } catch (error: any) {
    console.log('导入错误:', JSON.stringify(error))
    importResult.value = {
      success: false,
      message: error.message || '导入失败',
    }
    currentStep.value = 3
    stepStatus.value = 'error'
  } finally {
    importing.value = false
  }
}

const viewProgress = () => {
  // TODO: 打开进度查看页面
  message.info('功能开发中...')
}

const resetAndClose = () => {
  resetForm()
  visible.value = false
}

const resetForm = () => {
  currentStep.value = 1
  stepStatus.value = 'process'
  formData.importText = ''
  formData.source = '批量导入'
  formData.tags = []
  formData.description = ''
  formData.autoVerify = true
  previewData.value = {
    valid: [],
    invalid: [],
    summary: { total: 0, valid: 0, invalid: 0 },
  }
  importResult.value = null
}

// 监听弹窗关闭，重置表单
watch(visible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.batch-import-modal {
  padding: 20px 0;
}

.step-content {
  margin-top: 24px;
  min-height: 400px;
}

.step-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
}
</style>
