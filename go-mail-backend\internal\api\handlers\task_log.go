package handlers

import (
	"go-mail/internal/database"
	"go-mail/internal/services/task_log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// TaskLogHandler 任务日志处理器
type TaskLogHandler struct {
	taskLogService *task_log.TaskLogService
}

// NewTaskLogHandler 创建任务日志处理器
func NewTaskLogHandler(taskLogService *task_log.TaskLogService) *TaskLogHandler {
	return &TaskLogHandler{
		taskLogService: taskLogService,
	}
}

// GetTaskLogs 获取任务日志列表
// @Summary 获取任务日志列表
// @Description 根据筛选条件获取任务日志列表，支持分页
// @Tags 任务日志
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Param operation_type query string false "操作类型 (import/verify/login)"
// @Param status query string false "状态 (success/failed/running/pending)"
// @Param email query string false "邮箱地址 (支持模糊搜索)"
// @Param batch_id query string false "批次ID"
// @Param page query int false "页码 (默认1)"
// @Param page_size query int false "每页大小 (默认20)"
// @Success 200 {object} database.APIResponse{data=database.TaskLogResponse}
// @Failure 400 {object} database.APIResponse
// @Failure 500 {object} database.APIResponse
// @Router /api/v1/mailbox/task-logs [get]
func (h *TaskLogHandler) GetTaskLogs(c *gin.Context) {
	// 解析查询参数
	filter := database.TaskLogFilter{}

	// 时间范围
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filter.StartTime = &startTime
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filter.EndTime = &endTime
		}
	}

	// 操作类型
	if operationType := c.Query("operation_type"); operationType != "" {
		filter.OperationType = &operationType
	}

	// 状态
	if status := c.Query("status"); status != "" {
		filter.Status = &status
	}

	// 邮箱地址
	if email := c.Query("email"); email != "" {
		filter.Email = &email
	}

	// 批次ID
	if batchID := c.Query("batch_id"); batchID != "" {
		filter.BatchID = &batchID
	}

	// 分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filter.Page = page
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			filter.PageSize = pageSize
		}
	}

	// 查询任务日志
	response, err := h.taskLogService.GetTaskLogs(filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取任务日志失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取任务日志成功",
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetTaskLogDetail 获取任务详细日志
// @Summary 获取任务详细日志
// @Description 根据任务日志ID获取详细的执行日志
// @Tags 任务日志
// @Accept json
// @Produce json
// @Param id path int true "任务日志ID"
// @Success 200 {object} database.APIResponse{data=database.TaskLogDetailResponse}
// @Failure 400 {object} database.APIResponse
// @Failure 404 {object} database.APIResponse
// @Failure 500 {object} database.APIResponse
// @Router /api/v1/mailbox/task-logs/{id}/details [get]
func (h *TaskLogHandler) GetTaskLogDetail(c *gin.Context) {
	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的任务日志ID",
			Error:     "ID必须是数字",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 获取详细日志
	response, err := h.taskLogService.GetTaskLogDetail(id)
	if err != nil {
		if err.Error() == "任务日志不存在" {
			c.JSON(http.StatusNotFound, database.APIResponse{
				Code:      404,
				Message:   "任务日志不存在",
				Error:     err.Error(),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: c.GetString("request_id"),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取详细日志失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取详细日志成功",
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// CreateTaskLog 创建任务日志
// @Summary 创建任务日志
// @Description 创建新的任务日志记录
// @Tags 任务日志
// @Accept json
// @Produce json
// @Param request body database.CreateTaskLogRequest true "创建任务日志请求"
// @Success 201 {object} database.APIResponse{data=database.TaskLog}
// @Failure 400 {object} database.APIResponse
// @Failure 500 {object} database.APIResponse
// @Router /api/v1/mailbox/task-logs [post]
func (h *TaskLogHandler) CreateTaskLog(c *gin.Context) {
	var req database.CreateTaskLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 创建任务日志
	taskLog, err := h.taskLogService.CreateTaskLog(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "创建任务日志失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusCreated, database.APIResponse{
		Code:      201,
		Message:   "创建任务日志成功",
		Data:      taskLog,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// UpdateTaskLog 更新任务日志
// @Summary 更新任务日志
// @Description 更新指定任务日志的状态和信息
// @Tags 任务日志
// @Accept json
// @Produce json
// @Param task_id path string true "任务ID"
// @Param request body database.UpdateTaskLogRequest true "更新任务日志请求"
// @Success 200 {object} database.APIResponse
// @Failure 400 {object} database.APIResponse
// @Failure 404 {object} database.APIResponse
// @Failure 500 {object} database.APIResponse
// @Router /api/v1/mailbox/task-logs/{task_id} [put]
func (h *TaskLogHandler) UpdateTaskLog(c *gin.Context) {
	taskID := c.Param("task_id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "任务ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	var req database.UpdateTaskLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 更新任务日志
	err := h.taskLogService.UpdateTaskLog(taskID, req)
	if err != nil {
		if err.Error() == "任务日志不存在" {
			c.JSON(http.StatusNotFound, database.APIResponse{
				Code:      404,
				Message:   "任务日志不存在",
				Error:     err.Error(),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: c.GetString("request_id"),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "更新任务日志失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "更新任务日志成功",
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}
