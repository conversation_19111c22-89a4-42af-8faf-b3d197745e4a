<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>系统概览</h1>
      <p>Go-Mail临时邮箱服务管理后台</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <n-card class="stat-card" v-for="stat in statsCards" :key="stat.key">
        <div class="stat-content">
          <div class="stat-icon" :style="{ backgroundColor: stat.color + '20' }">
            <n-icon size="24" :color="stat.color">
              <component :is="stat.icon" />
            </n-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
        <div class="stat-trend" v-if="stat.trend">
          <n-icon size="14" :color="stat.trend > 0 ? '#18a058' : '#d03050'">
            <ArrowUpIcon v-if="stat.trend > 0" />
            <ArrowDownIcon v-else />
          </n-icon>
          <span :style="{ color: stat.trend > 0 ? '#18a058' : '#d03050' }">
            {{ Math.abs(stat.trend) }}%
          </span>
        </div>
      </n-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 邮箱使用趋势 -->
      <n-card title="邮箱使用趋势" class="chart-card">
        <template #header-extra>
          <n-select
            v-model:value="timeRange"
            :options="timeRangeOptions"
            size="small"
            style="width: 120px"
          />
        </template>
        <div class="chart-container">
          <v-chart :option="mailboxTrendOption" autoresize />
        </div>
      </n-card>

      <!-- 激活码状态分布 -->
      <n-card title="激活码状态分布" class="chart-card">
        <div class="chart-container">
          <v-chart :option="activationCodePieOption" autoresize />
        </div>
      </n-card>
    </div>

    <!-- 最近活动 -->
    <n-card title="最近活动" class="activity-card">
      <template #header-extra>
        <n-button text @click="refreshActivities">
          <template #icon>
            <n-icon>
              <RefreshIcon />
            </n-icon>
          </template>
          刷新
        </n-button>
      </template>

      <n-list>
        <n-list-item v-for="activity in recentActivities" :key="activity.id">
          <template #prefix>
            <n-avatar size="small" :style="{ backgroundColor: activity.color }">
              <n-icon>
                <component :is="activity.icon" />
              </n-icon>
            </n-avatar>
          </template>
          <n-thing>
            <template #header>{{ activity.title }}</template>
            <template #description>{{ activity.description }}</template>
            <template #footer>
              <n-time :time="new Date(activity.timestamp)" type="relative" />
            </template>
          </n-thing>
        </n-list-item>
      </n-list>

      <div v-if="recentActivities.length === 0" class="empty-state">
        <n-empty description="暂无活动记录" />
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, markRaw } from 'vue'
import {
  NCard,
  NIcon,
  NSelect,
  NButton,
  NList,
  NListItem,
  NThing,
  NTime,
  NAvatar,
  NEmpty,
} from 'naive-ui'
import {
  Key as KeyIcon,
  Mail as MailIcon,
  People as PeopleIcon,
  Analytics as AnalyticsIcon,
  ArrowUp as ArrowUpIcon,
  ArrowDown as ArrowDownIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
} from '@vicons/ionicons5'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'
import { systemApi } from '@/api/system'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
])

// 响应式数据
const timeRange = ref('7d')
const loading = ref(false)

// 时间范围选项
const timeRangeOptions = [
  { label: '最近7天', value: '7d' },
  { label: '最近30天', value: '30d' },
  { label: '最近90天', value: '90d' },
]

// 统计数据
const systemStats = reactive({
  totalActivationCodes: 0,
  usedActivationCodes: 0,
  activeMailboxes: 0,
  totalMails: 0,
})

// 统计卡片数据
const statsCards = computed(() => [
  {
    key: 'activation-codes',
    label: '激活码总数',
    value: systemStats.totalActivationCodes,
    icon: markRaw(KeyIcon),
    color: '#18a058',
    trend: 5.2,
  },
  {
    key: 'used-codes',
    label: '已使用激活码',
    value: systemStats.usedActivationCodes,
    icon: markRaw(PeopleIcon),
    color: '#2080f0',
    trend: 12.5,
  },
  {
    key: 'active-mailboxes',
    label: '活跃邮箱',
    value: systemStats.activeMailboxes,
    icon: markRaw(MailIcon),
    color: '#f0a020',
    trend: -2.1,
  },
  {
    key: 'total-mails',
    label: '邮件总数',
    value: systemStats.totalMails,
    icon: markRaw(AnalyticsIcon),
    color: '#d03050',
    trend: 8.7,
  },
])

// 邮箱趋势图表配置
const mailboxTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
  },
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '新增邮箱',
      type: 'line',
      data: [12, 19, 15, 22, 18, 25, 20],
      smooth: true,
      itemStyle: { color: '#18a058' },
    },
    {
      name: '活跃邮箱',
      type: 'line',
      data: [8, 15, 12, 18, 14, 20, 16],
      smooth: true,
      itemStyle: { color: '#2080f0' },
    },
  ],
}))

// 激活码饼图配置
const activationCodePieOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    left: 'left',
  },
  series: [
    {
      name: '激活码状态',
      type: 'pie',
      radius: '50%',
      data: [
        {
          value: systemStats.totalActivationCodes - systemStats.usedActivationCodes,
          name: '未使用',
          itemStyle: { color: '#18a058' },
        },
        { value: systemStats.usedActivationCodes, name: '已使用', itemStyle: { color: '#2080f0' } },
        { value: 5, name: '已过期', itemStyle: { color: '#d03050' } },
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
}))

// 最近活动数据
const recentActivities = ref([
  {
    id: '1',
    title: '创建了新的激活码',
    description: '批量创建了50个激活码',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
    icon: markRaw(AddIcon),
    color: '#18a058',
  },
  {
    id: '2',
    title: '用户使用激活码',
    description: '激活码 AC123456 被使用',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
    icon: markRaw(KeyIcon),
    color: '#2080f0',
  },
  {
    id: '3',
    title: '系统配置更新',
    description: '邮箱过期时间设置为30分钟',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
    icon: markRaw(SettingsIcon),
    color: '#f0a020',
  },
])

// 获取系统统计数据
const fetchSystemStats = async () => {
  try {
    loading.value = true
    const response = await systemApi.getSystemStats()
    if (response.success && response.data) {
      // 适配后端返回的数据结构
      const data = response.data
      systemStats.totalActivationCodes = data.activation_codes?.total_generated || 0
      systemStats.usedActivationCodes = data.activation_codes?.used || 0
      systemStats.activeMailboxes = data.mailboxes?.currently_active || 0
      systemStats.totalMails = data.mailboxes?.total_allocated || 0
    }
  } catch (error: any) {
    console.error('Failed to fetch system stats:', error)
    // 可以在这里添加用户友好的错误提示
  } finally {
    loading.value = false
  }
}

// 刷新活动记录
const refreshActivities = () => {
  // 这里可以调用API获取最新的活动记录
  console.log('Refreshing activities...')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSystemStats()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.dashboard-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--n-text-color-2);
}

.stat-trend {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.chart-card {
  min-height: 300px;
}

.chart-container {
  height: 250px;
}

.activity-card {
  margin-bottom: 24px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 200px;
  }
}
</style>
