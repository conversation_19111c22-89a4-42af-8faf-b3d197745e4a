# Go-Mail 前端管理后台

Go-Mail临时邮箱服务的前端管理后台，基于Vue3 + TypeScript + Naive UI构建。

## 🚀 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.3+
- **构建工具**: Vite 5.0+
- **UI组件库**: Naive UI 2.38+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **图表库**: ECharts 5.4+ / Vue-ECharts 6.6+
- **图标**: @vicons/ionicons5
- **代码规范**: ESLint + Prettier

## 📦 安装和设置

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 (或 yarn >= 1.22.0)

### 安装依赖

```bash
# 进入前端目录
cd mail-frontend

# 安装依赖
npm install

# 或使用yarn
yarn install
```

### 环境配置

项目包含两个环境配置文件：

- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

主要配置项：
```bash
# 应用标题
VITE_APP_TITLE=Go-Mail管理后台

# API基础URL
VITE_API_BASE_URL=http://localhost:8080/api

# 应用版本
VITE_APP_VERSION=1.0.0
```

## 🛠️ 开发工作流

### 启动开发服务器

```bash
npm run dev
```

开发服务器将在 `http://localhost:3000` 启动，支持热重载。

### 代码检查和格式化

```bash
# ESLint检查
npm run lint

# Prettier格式化
npm run format
```

### 构建项目

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📁 项目结构

```
mail-frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口定义
│   │   ├── auth.ts        # 认证相关API
│   │   ├── activation.ts  # 激活码管理API
│   │   └── system.ts      # 系统管理API
│   ├── components/        # 公共组件
│   ├── layouts/           # 布局组件
│   │   └── MainLayout.vue # 主布局
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── stores/            # 状态管理
│   │   ├── app.ts         # 应用状态
│   │   └── auth.ts        # 认证状态
│   ├── styles/            # 样式文件
│   │   └── main.css       # 全局样式
│   ├── types/             # TypeScript类型定义
│   │   ├── api.ts         # API类型
│   │   └── common.ts      # 通用类型
│   ├── utils/             # 工具函数
│   │   ├── request.ts     # HTTP请求封装
│   │   ├── device.ts      # 设备指纹生成
│   │   └── common.ts      # 通用工具函数
│   ├── views/             # 页面组件
│   │   ├── Login.vue      # 登录页
│   │   ├── Dashboard.vue  # 系统概览
│   │   ├── ActivationCodes.vue # 激活码管理
│   │   ├── SystemMonitor.vue   # 系统监控
│   │   ├── SystemConfig.vue    # 系统配置
│   │   ├── Profile.vue    # 个人设置
│   │   └── NotFound.vue   # 404页面
│   ├── App.vue            # 根组件
│   └── main.ts            # 应用入口
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── .eslintrc.cjs         # ESLint配置
├── .prettierrc           # Prettier配置
├── index.html            # HTML模板
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript配置
├── tsconfig.node.json    # Node.js TypeScript配置
└── vite.config.ts        # Vite配置
```

## 🎯 核心功能

### 1. 认证系统
- 用户登录/登出
- JWT令牌管理
- 设备指纹识别
- 自动令牌刷新

### 2. 激活码管理
- 批量创建激活码
- 激活码列表查看
- 状态筛选和搜索
- 批量删除操作

### 3. 系统监控
- 实时系统状态
- 定时任务监控
- 系统日志查看
- 性能指标展示

### 4. 系统配置
- 基础参数配置
- 数据清理操作
- 系统备份功能
- 版本信息查看

### 5. 个人设置
- 密码修改
- 主题切换
- 语言设置
- 设备信息查看

## 🚀 部署

### 构建生产版本

```bash
npm run build
```

构建产物将输出到 `dist/` 目录。

### 部署到Web服务器

1. **Nginx配置示例**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📄 许可证

本项目采用 MIT 许可证。