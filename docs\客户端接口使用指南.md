# Go-Mail 客户端接口使用指南

## 🔐 客户端认证机制

### 认证方式说明
客户端使用**激活码直接认证**方式，每次请求都需要携带：
1. **激活码**：放在 Authorization 请求头
2. **设备指纹**：加密后放在 X-Device-Fingerprint 请求头  
3. **MAC地址**：加密后放在 X-Mac-Address 请求头

### 请求头格式
```http
Authorization: Activation {activation_code}
X-Device-Fingerprint: {encrypted_device_info}
X-Mac-Address: {encrypted_mac_address}
Content-Type: application/json
```

## 🛠️ 设备信息收集

### 设备指纹数据结构
```json
{
  "system_uuid": "550e8400-e29b-41d4-a716-************",
  "username": "john_doe",
  "computer_name": "DESKTOP-ABC123",
  "platform": "windows",
  "mac_address": "00:11:22:33:44:55"
}
```

### 各平台设备信息获取方法

#### Windows平台
```go
// 获取系统UUID
func getSystemUUID() string {
    cmd := exec.Command("wmic", "csproduct", "get", "UUID", "/value")
    output, _ := cmd.Output()
    // 解析输出获取UUID
    return uuid
}

// 获取用户名
func getUsername() string {
    return os.Getenv("USERNAME")
}

// 获取计算机名
func getComputerName() string {
    return os.Getenv("COMPUTERNAME")
}

// 获取MAC地址
func getMacAddress() string {
    interfaces, _ := net.Interfaces()
    for _, iface := range interfaces {
        if iface.Flags&net.FlagUp != 0 && iface.Flags&net.FlagLoopback == 0 {
            return iface.HardwareAddr.String()
        }
    }
    return ""
}
```

#### macOS平台
```go
// 获取系统UUID
func getSystemUUID() string {
    cmd := exec.Command("system_profiler", "SPHardwareDataType")
    output, _ := cmd.Output()
    // 解析输出获取Hardware UUID
    return uuid
}

// 获取用户名
func getUsername() string {
    return os.Getenv("USER")
}

// 获取计算机名
func getComputerName() string {
    cmd := exec.Command("hostname")
    output, _ := cmd.Output()
    return strings.TrimSpace(string(output))
}
```

#### Linux平台
```go
// 获取系统UUID
func getSystemUUID() string {
    data, _ := ioutil.ReadFile("/sys/class/dmi/id/product_uuid")
    return strings.TrimSpace(string(data))
}

// 获取用户名
func getUsername() string {
    return os.Getenv("USER")
}

// 获取计算机名
func getComputerName() string {
    cmd := exec.Command("hostname")
    output, _ := cmd.Output()
    return strings.TrimSpace(string(output))
}
```

## 🔒 数据加密处理

### AES加密实现
```go
package crypto

import (
    "crypto/aes"
    "crypto/cipher"
    "crypto/rand"
    "encoding/base64"
    "encoding/json"
)

type ClientCrypto struct {
    key []byte
}

func NewClientCrypto(key string) *ClientCrypto {
    return &ClientCrypto{key: []byte(key)}
}

func (c *ClientCrypto) EncryptDeviceInfo(deviceInfo DeviceFingerprint) (string, error) {
    // 1. 序列化设备信息
    data, err := json.Marshal(deviceInfo)
    if err != nil {
        return "", err
    }
    
    // 2. AES-GCM加密
    block, err := aes.NewCipher(c.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    rand.Read(nonce)
    
    ciphertext := gcm.Seal(nonce, nonce, data, nil)
    
    // 3. Base64编码
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func (c *ClientCrypto) EncryptMacAddress(macAddress string) (string, error) {
    // 类似的加密处理
    return c.EncryptDeviceInfo(DeviceFingerprint{MacAddress: macAddress})
}
```

## 📧 完整使用示例

### 1. 初始化客户端
```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
)

type MailClient struct {
    baseURL        string
    activationCode string
    crypto         *ClientCrypto
    deviceInfo     DeviceFingerprint
}

func NewMailClient(baseURL, activationCode, aesKey string) *MailClient {
    return &MailClient{
        baseURL:        baseURL,
        activationCode: activationCode,
        crypto:         NewClientCrypto(aesKey),
        deviceInfo:     collectDeviceInfo(),
    }
}

func collectDeviceInfo() DeviceFingerprint {
    return DeviceFingerprint{
        SystemUUID:   getSystemUUID(),
        Username:     getUsername(),
        ComputerName: getComputerName(),
        Platform:     runtime.GOOS,
        MacAddress:   getMacAddress(),
    }
}
```

### 2. 分配临时邮箱
```go
func (c *MailClient) AllocateMailbox() (*MailboxResponse, error) {
    // 1. 准备请求数据
    requestData := map[string]interface{}{
        "preferences": map[string]interface{}{
            "domain_suffix":         "random",
            "auto_release_minutes":  3,
        },
    }
    
    // 2. 加密设备信息
    encryptedDevice, err := c.crypto.EncryptDeviceInfo(c.deviceInfo)
    if err != nil {
        return nil, err
    }
    
    encryptedMac, err := c.crypto.EncryptMacAddress(c.deviceInfo.MacAddress)
    if err != nil {
        return nil, err
    }
    
    // 3. 发送请求
    jsonData, _ := json.Marshal(requestData)
    req, _ := http.NewRequest("POST", c.baseURL+"/api/v1/client/mailbox/allocate", bytes.NewBuffer(jsonData))
    
    // 4. 设置请求头
    req.Header.Set("Authorization", "Activation "+c.activationCode)
    req.Header.Set("X-Device-Fingerprint", encryptedDevice)
    req.Header.Set("X-Mac-Address", encryptedMac)
    req.Header.Set("Content-Type", "application/json")
    
    // 5. 执行请求
    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    // 6. 解析响应
    var response MailboxResponse
    json.NewDecoder(resp.Body).Decode(&response)
    
    return &response, nil
}
```

### 3. 查询邮件
```go
func (c *MailClient) QueryMails(mailboxID int, since string) (*MailQueryResponse, error) {
    requestData := map[string]interface{}{
        "mailbox_id": mailboxID,
        "since":      since,
    }
    
    return c.makeRequest("POST", "/api/v1/client/mailbox/query", requestData)
}
```

### 4. 获取邮件详情
```go
func (c *MailClient) GetMailDetail(mailboxID int, mailID string) (*MailDetailResponse, error) {
    requestData := map[string]interface{}{
        "mailbox_id": mailboxID,
        "mail_id":    mailID,
    }
    
    return c.makeRequest("POST", "/api/v1/client/mailbox/mail/detail", requestData)
}
```

### 5. 释放邮箱
```go
func (c *MailClient) ReleaseMailbox(mailboxID int) error {
    requestData := map[string]interface{}{
        "mailbox_id": mailboxID,
        "reason":     "manual_release",
    }
    
    _, err := c.makeRequest("POST", "/api/v1/client/mailbox/release", requestData)
    return err
}
```

## 🔄 轮询机制实现

### 邮件监控循环
```go
func (c *MailClient) StartMailMonitoring(mailboxID int) {
    ticker := time.NewTicker(3 * time.Second)
    defer ticker.Stop()
    
    lastCheck := time.Now().Format(time.RFC3339)
    
    for {
        select {
        case <-ticker.C:
            // 查询新邮件
            response, err := c.QueryMails(mailboxID, lastCheck)
            if err != nil {
                fmt.Printf("查询邮件失败: %v\n", err)
                continue
            }
            
            // 处理新邮件
            if len(response.Data.Mails) > 0 {
                fmt.Printf("收到 %d 封新邮件\n", len(response.Data.Mails))
                for _, mail := range response.Data.Mails {
                    c.handleNewMail(mailboxID, mail)
                }
                
                // 可选：获取邮件后立即释放邮箱
                c.ReleaseMailbox(mailboxID)
                return
            }
            
            lastCheck = time.Now().Format(time.RFC3339)
            
        case <-time.After(3 * time.Minute):
            // 3分钟无新邮件，自动释放
            fmt.Println("3分钟无新邮件，自动释放邮箱")
            c.ReleaseMailbox(mailboxID)
            return
        }
    }
}
```

## ⚠️ 错误处理

### 常见错误码处理
```go
func (c *MailClient) handleAPIError(response *APIResponse) error {
    switch response.Code {
    case 1001:
        return fmt.Errorf("激活码无效，请检查激活码是否正确")
    case 1003:
        return fmt.Errorf("激活码已过期，请获取新的激活码")
    case 1004:
        return fmt.Errorf("设备指纹不匹配，请在绑定的设备上使用")
    case 2001:
        return fmt.Errorf("邮箱分配失败，请稍后重试")
    case 4001:
        return fmt.Errorf("设备信息加密失败，请检查加密配置")
    default:
        return fmt.Errorf("API错误: %s", response.Message)
    }
}
```

## 📝 使用注意事项

### 1. 安全注意事项
- 激活码和AES密钥需要安全存储
- 设备信息加密后传输
- 避免在日志中记录敏感信息

### 2. 性能优化
- 复用HTTP客户端连接
- 合理设置请求超时时间
- 避免频繁的设备信息收集

### 3. 错误重试
- 网络错误可以重试
- 认证错误不应重试
- 实现指数退避重试策略

---

**文档版本**：v1.0  
**创建时间**：2025-01-20  
**适用版本**：客户端 v1.0+  
**维护人员**：客户端开发团队
