package mailbox

import (
	"context"
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/mail/client"
	"go-mail/internal/manager"
	"go-mail/internal/types"
	"math/rand"
	"strings"
	"time"
)

// Service 临时邮箱服务
type Service struct {
	mailManager             *manager.MailManager
	db                      *database.Database
	enableDetailedFiltering bool // 是否启用详细的邮件过滤
}

// NewService 创建临时邮箱服务
func NewService(mailManager *manager.MailManager, db *database.Database) *Service {
	return &Service{
		mailManager:             mailManager,
		db:                      db,
		enableDetailedFiltering: false, // 默认关闭详细过滤以提高性能
	}
}

// AllocateMailbox 分配临时邮箱
func (s *Service) AllocateMailbox(ctx context.Context, activationCode string, deviceFingerprint string, preferences database.MailboxAllocationRequest) (*database.MailboxAllocationResponse, error) {
	// 1. 选择可用的Mail.com账户
	accounts := s.mailManager.ListAccounts()
	if len(accounts) == 0 {
		return nil, fmt.Errorf("没有可用的邮件账户")
	}

	// 选择状态为活跃的账户
	var selectedAccount *types.AccountInfo
	for _, account := range accounts {
		if account.Status == types.AccountStatusActive {
			selectedAccount = &account
			break
		}
	}

	if selectedAccount == nil {
		return nil, fmt.Errorf("没有活跃的邮件账户")
	}

	// 2. 生成随机别名
	aliasName := s.generateRandomAlias()
	mailboxAddress := fmt.Sprintf("%<EMAIL>", aliasName)

	// 3. 创建别名邮箱
	session, err := s.getAccountSession(ctx, selectedAccount.Username)
	if err != nil {
		return nil, fmt.Errorf("获取账户会话失败: %w", err)
	}

	// 创建别名请求
	aliasRequest := types.CreateAliasEmailRequest{
		AliasName:    aliasName,
		DomainSuffix: "mail.com",
	}

	err = s.createAliasEmail(ctx, session, aliasRequest)
	if err != nil {
		return nil, fmt.Errorf("创建别名邮箱失败: %w", err)
	}

	// 4. 记录分配信息
	autoReleaseMinutes := preferences.Preferences.AutoReleaseMinutes
	if autoReleaseMinutes <= 0 {
		autoReleaseMinutes = 3 // 默认3分钟
	}

	expiresAt := time.Now().Add(time.Duration(autoReleaseMinutes) * time.Minute)

	result, err := s.db.GetDB().Exec(`
		INSERT INTO temp_mailboxes (mailbox_address, account_email, device_fingerprint, activation_code, status, expires_at, auto_release)
		VALUES (?, ?, ?, ?, ?, ?, ?)`,
		mailboxAddress, selectedAccount.Username, deviceFingerprint, activationCode,
		database.TempMailboxStatusActive, expiresAt, true)
	if err != nil {
		// 如果数据库插入失败，尝试删除已创建的别名
		s.deleteAliasEmail(ctx, session, aliasName)
		return nil, fmt.Errorf("记录邮箱分配信息失败: %w", err)
	}

	mailboxID, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("获取邮箱ID失败: %w", err)
	}

	return &database.MailboxAllocationResponse{
		MailboxID:   int(mailboxID),
		Address:     mailboxAddress,
		Status:      database.TempMailboxStatusActive,
		AllocatedAt: time.Now(),
		ExpiresAt:   expiresAt,
		AutoRelease: true,
	}, nil
}

// QueryMails 查询邮件
func (s *Service) QueryMails(ctx context.Context, mailboxID int, since string) (*database.MailQueryResponse, error) {
	// 1. 查询邮箱信息
	var mailbox database.TempMailbox
	err := s.db.GetDB().QueryRow(`
		SELECT id, mailbox_address, account_email, status, allocated_at, expires_at
		FROM temp_mailboxes WHERE id = ? AND status = ?`,
		mailboxID, database.TempMailboxStatusActive).Scan(
		&mailbox.ID, &mailbox.MailboxAddress, &mailbox.AccountEmail,
		&mailbox.Status, &mailbox.AllocatedAt, &mailbox.ExpiresAt)

	if err != nil {
		return nil, fmt.Errorf("邮箱不存在或已释放")
	}

	// 2. 检查邮箱是否过期
	if time.Now().After(mailbox.ExpiresAt) {
		s.ReleaseMailbox(ctx, mailboxID, database.ReleaseReasonExpired)
		return nil, fmt.Errorf("邮箱已过期")
	}

	// 3. 获取账户会话
	session, err := s.getAccountSession(ctx, mailbox.AccountEmail)
	if err != nil {
		return nil, fmt.Errorf("获取账户会话失败: %w", err)
	}

	// 4. 获取邮件列表
	mailList, err := s.getMailList(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("获取邮件列表失败: %w", err)
	}

	// 5. 过滤发送到该别名的邮件
	var filteredMails []struct {
		ID         string    `json:"id"`
		Subject    string    `json:"subject"`
		Sender     string    `json:"sender"`
		ReceivedAt time.Time `json:"received_at"`
		Preview    string    `json:"preview"`
		HasContent bool      `json:"has_content"`
	}

	aliasName := strings.Split(mailbox.MailboxAddress, "@")[0]
	for _, mail := range mailList.Items {
		// 检查邮件是否发送到该别名
		if s.isMailForAlias(mail, aliasName) {
			// 检查是否是新邮件
			if since != "" {
				sinceTime, err := time.Parse(time.RFC3339, since)
				if err == nil {
					mailDate, parseErr := time.Parse("2006-01-02 15:04:05", mail.Date)
					if parseErr == nil && mailDate.Before(sinceTime) {
						continue
					}
				}
			}

			// 解析邮件日期
			mailDate, _ := time.Parse("2006-01-02 15:04:05", mail.Date)

			filteredMails = append(filteredMails, struct {
				ID         string    `json:"id"`
				Subject    string    `json:"subject"`
				Sender     string    `json:"sender"`
				ReceivedAt time.Time `json:"received_at"`
				Preview    string    `json:"preview"`
				HasContent bool      `json:"has_content"`
			}{
				ID:         mail.MailID,
				Subject:    mail.Subject,
				Sender:     mail.From,
				ReceivedAt: mailDate,
				Preview:    mail.Subject, // 使用主题作为预览
				HasContent: true,
			})

			// 记录邮件到数据库
			s.recordMail(mailboxID, mail)
		}
	}

	// 6. 更新最后检查时间
	s.db.GetDB().Exec("UPDATE temp_mailboxes SET last_check_at = CURRENT_TIMESTAMP WHERE id = ?", mailboxID)

	return &database.MailQueryResponse{
		MailboxID:  mailboxID,
		Address:    mailbox.MailboxAddress,
		Mails:      filteredMails,
		TotalCount: len(filteredMails),
		LastCheck:  time.Now(),
	}, nil
}

// GetMailDetail 获取邮件详情
func (s *Service) GetMailDetail(ctx context.Context, mailboxID int, mailID string) (*database.MailDetailResponse, error) {
	// 1. 验证邮箱
	var mailbox database.TempMailbox
	err := s.db.GetDB().QueryRow(`
		SELECT id, mailbox_address, account_email, status
		FROM temp_mailboxes WHERE id = ? AND status = ?`,
		mailboxID, database.TempMailboxStatusActive).Scan(
		&mailbox.ID, &mailbox.MailboxAddress, &mailbox.AccountEmail, &mailbox.Status)

	if err != nil {
		return nil, fmt.Errorf("邮箱不存在或已释放")
	}

	// 2. 获取账户会话
	session, err := s.getAccountSession(ctx, mailbox.AccountEmail)
	if err != nil {
		return nil, fmt.Errorf("获取账户会话失败: %w", err)
	}

	// 3. 获取邮件内容
	mailContent, err := s.getMailContent(ctx, session, mailID)
	if err != nil {
		return nil, fmt.Errorf("获取邮件内容失败: %w", err)
	}

	// 4. 更新邮件记录
	s.db.GetDB().Exec(`
		UPDATE mail_records
		SET content_fetched = TRUE, content_text = ?, content_html = ?
		WHERE mailbox_id = ? AND mail_id = ?`,
		mailContent.TextBody, mailContent.Body, mailboxID, mailID)

	// 解析邮件日期
	mailDate, _ := time.Parse("2006-01-02 15:04:05", mailContent.Date)

	return &database.MailDetailResponse{
		ID:         mailID,
		Subject:    mailContent.Subject,
		Sender:     mailContent.From,
		ReceivedAt: mailDate,
		Content: struct {
			Text string `json:"text"`
			HTML string `json:"html"`
		}{
			Text: mailContent.TextBody,
			HTML: mailContent.Body,
		},
		Attachments: []any{}, // TODO: 处理附件
	}, nil
}

// ReleaseMailbox 释放邮箱
func (s *Service) ReleaseMailbox(ctx context.Context, mailboxID int, reason string) error {
	// 1. 查询邮箱信息
	var mailbox database.TempMailbox
	err := s.db.GetDB().QueryRow(`
		SELECT id, mailbox_address, account_email, status
		FROM temp_mailboxes WHERE id = ?`,
		mailboxID).Scan(&mailbox.ID, &mailbox.MailboxAddress, &mailbox.AccountEmail, &mailbox.Status)

	if err != nil {
		return fmt.Errorf("邮箱不存在")
	}

	if mailbox.Status == database.TempMailboxStatusReleased {
		return nil // 已经释放
	}

	// 2. 删除别名邮箱
	session, err := s.getAccountSession(ctx, mailbox.AccountEmail)
	if err == nil {
		aliasName := strings.Split(mailbox.MailboxAddress, "@")[0]
		s.deleteAliasEmail(ctx, session, aliasName)
	}

	// 3. 更新数据库状态
	_, err = s.db.GetDB().Exec(`
		UPDATE temp_mailboxes 
		SET status = ?, release_reason = ?
		WHERE id = ?`,
		database.TempMailboxStatusReleased, reason, mailboxID)

	return err
}

// GetMailboxStatus 获取邮箱状态
func (s *Service) GetMailboxStatus(ctx context.Context, mailboxID int) (*database.MailboxStatusResponse, error) {
	var mailbox database.TempMailbox
	err := s.db.GetDB().QueryRow(`
		SELECT id, mailbox_address, status, allocated_at, expires_at, last_check_at
		FROM temp_mailboxes WHERE id = ?`,
		mailboxID).Scan(&mailbox.ID, &mailbox.MailboxAddress, &mailbox.Status,
		&mailbox.AllocatedAt, &mailbox.ExpiresAt, &mailbox.LastCheckAt)

	if err != nil {
		return nil, fmt.Errorf("邮箱不存在")
	}

	// 查询邮件数量
	var mailCount int
	s.db.GetDB().QueryRow("SELECT COUNT(*) FROM mail_records WHERE mailbox_id = ?", mailboxID).Scan(&mailCount)

	lastCheck := time.Now()
	if mailbox.LastCheckAt != nil {
		lastCheck = *mailbox.LastCheckAt
	}

	return &database.MailboxStatusResponse{
		MailboxID:   mailboxID,
		Address:     mailbox.MailboxAddress,
		Status:      mailbox.Status,
		AllocatedAt: mailbox.AllocatedAt,
		ExpiresAt:   mailbox.ExpiresAt,
		LastCheck:   lastCheck,
		MailCount:   mailCount,
	}, nil
}

// 辅助方法

// generateRandomAlias 生成随机别名
func (s *Service) generateRandomAlias() string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	const length = 8

	// 使用crypto/rand生成更安全的随机数
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}

	return "temp_" + string(result)
}

// getAccountSession 获取账户会话
func (s *Service) getAccountSession(ctx context.Context, accountEmail string) (*types.Session, error) {
	// 使用MailManager获取真实会话
	session, err := s.mailManager.GetSessionByAccount(accountEmail)
	if err != nil {
		// 如果没有现有会话，检查账户是否可用
		accounts := s.mailManager.ListAccounts()
		var targetAccount *types.AccountInfo

		for _, account := range accounts {
			if account.Username == accountEmail && account.Status == types.AccountStatusActive {
				targetAccount = &account
				break
			}
		}

		if targetAccount == nil {
			return nil, fmt.Errorf("账户不可用: %s", accountEmail)
		}

		// 账户可用但没有会话，返回错误提示需要先登录
		return nil, fmt.Errorf("账户 %s 没有活跃会话，请先登录", accountEmail)
	}

	// 检查会话是否有效
	if session.Status != types.SessionStatusActive {
		return nil, fmt.Errorf("会话状态无效: %s", session.Status)
	}

	return session, nil
}

// createAliasEmail 创建别名邮箱
func (s *Service) createAliasEmail(ctx context.Context, session *types.Session, request types.CreateAliasEmailRequest) error {
	// 获取MailManager配置
	config := s.mailManager.GetConfig()

	// 创建MailClient实例
	mailClient := client.NewMailClient(config)

	// 构建创建请求
	aliasRequest := &types.CreateAliasRequest{
		LocalPart:       request.AliasName,
		DomainSelection: "option1", // 默认选择mail.com域名
	}

	// 调用真实API
	response, err := mailClient.CreateAliasEmail(ctx, session, aliasRequest)
	if err != nil {
		return fmt.Errorf("创建别名失败: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("创建别名失败: %s", response.Message)
	}

	fmt.Printf("成功创建别名邮箱: %s@%s (会话: %s)\n",
		request.AliasName, request.DomainSuffix, session.ID)
	return nil
}

// deleteAliasEmail 删除别名邮箱
func (s *Service) deleteAliasEmail(ctx context.Context, session *types.Session, aliasName string) error {
	// 获取MailManager配置
	config := s.mailManager.GetConfig()

	// 创建MailClient实例
	mailClient := client.NewMailClient(config)

	// 首先获取别名列表，找到要删除的别名的RowID
	aliasListResponse, err := mailClient.GetAliasEmails(ctx, session)
	if err != nil {
		return fmt.Errorf("获取别名列表失败: %w", err)
	}

	// 查找目标别名
	var targetRowID string
	fullAliasName := aliasName + "@mail.com"
	for _, alias := range aliasListResponse.Aliases {
		if alias.Email == fullAliasName || alias.LocalPart == aliasName {
			targetRowID = alias.ID
			break
		}
	}

	if targetRowID == "" {
		// 别名不存在，可能已经被删除了
		fmt.Printf("别名 %s 不存在，可能已被删除\n", aliasName)
		return nil
	}

	// 构建删除请求
	deleteRequest := &types.DeleteAliasRequest{
		RowID: targetRowID,
	}

	// 调用删除API
	response, err := mailClient.DeleteAliasEmail(ctx, session, deleteRequest)
	if err != nil {
		return fmt.Errorf("删除别名失败: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("删除别名失败: %s", response.Message)
	}

	fmt.Printf("成功删除别名邮箱: %s (会话: %s)\n", aliasName, session.ID)
	return nil
}

// getMailList 获取邮件列表
func (s *Service) getMailList(ctx context.Context, session *types.Session) (*types.MailList, error) {
	// 获取MailManager配置
	config := s.mailManager.GetConfig()

	// 创建MailClient实例
	mailClient := client.NewMailClient(config)

	// 调用真实API获取邮件列表
	mailList, err := mailClient.GetMailList(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("获取邮件列表失败: %w", err)
	}

	fmt.Printf("成功获取邮件列表，共 %d 封邮件 (会话: %s)\n", len(mailList.Items), session.ID)
	return mailList, nil
}

// getMailContent 获取邮件内容
func (s *Service) getMailContent(ctx context.Context, session *types.Session, mailID string) (*types.MailContent, error) {
	// 获取MailManager配置
	config := s.mailManager.GetConfig()

	// 创建MailClient实例
	mailClient := client.NewMailClient(config)

	// 调用真实API获取邮件内容
	mailContent, err := mailClient.GetMailContent(ctx, session, mailID)
	if err != nil {
		return nil, fmt.Errorf("获取邮件内容失败: %w", err)
	}

	fmt.Printf("成功获取邮件内容，MailID: %s (会话: %s)\n", mailID, session.ID)
	return mailContent, nil
}

// isMailForAlias 检查邮件是否发送到指定别名
func (s *Service) isMailForAlias(mail types.MailItem, aliasName string) bool {
	// 构建完整的别名邮箱地址
	fullAlias := aliasName + "@mail.com"
	fullAliasLower := strings.ToLower(fullAlias)

	// 方法1：检查邮件主题中是否包含别名信息
	// 有些邮件系统会在主题中包含收件人信息
	if mail.Subject != "" {
		subjectLower := strings.ToLower(mail.Subject)
		if strings.Contains(subjectLower, aliasName) || strings.Contains(subjectLower, fullAliasLower) {
			return true
		}
	}

	// 方法2：检查FromTitle和SubjectTitle中的信息
	// 这些字段可能包含更详细的邮件信息
	if mail.FromTitle != "" {
		fromTitleLower := strings.ToLower(mail.FromTitle)
		if strings.Contains(fromTitleLower, aliasName) || strings.Contains(fromTitleLower, fullAliasLower) {
			return true
		}
	}

	if mail.SubjectTitle != "" {
		subjectTitleLower := strings.ToLower(mail.SubjectTitle)
		if strings.Contains(subjectTitleLower, aliasName) || strings.Contains(subjectTitleLower, fullAliasLower) {
			return true
		}
	}

	// 方法3：尝试获取邮件详细内容进行精确匹配
	// 这是最准确的方法，但会增加网络请求
	if s.enableDetailedFiltering {
		return s.isMailForAliasDetailed(mail, aliasName, fullAliasLower)
	}

	// 方法4：基于邮件ID和时间的启发式判断
	// 如果邮件是最近收到的，很可能是发送到这个别名的
	if mail.IsNew {
		// 新邮件更可能是发送到当前别名的
		return true
	}

	// 默认情况下，为了避免遗漏邮件，返回true
	// 这样可能会有一些误报，但不会遗漏真正的邮件
	return true
}

// isMailForAliasDetailed 通过获取邮件详细内容进行精确匹配
func (s *Service) isMailForAliasDetailed(mail types.MailItem, aliasName string, fullAliasLower string) bool {
	// 获取邮件详细内容
	ctx := context.Background()

	// 这里需要获取会话，但为了避免循环依赖，我们先简化实现
	// 在实际使用中，应该传入会话参数或者通过其他方式获取

	// 暂时使用简化的匹配逻辑
	// TODO: 实现真正的邮件内容获取和匹配
	_ = ctx

	return true
}

// SetDetailedFiltering 设置是否启用详细过滤
func (s *Service) SetDetailedFiltering(enabled bool) {
	s.enableDetailedFiltering = enabled
}

// recordMail 记录邮件到数据库
func (s *Service) recordMail(mailboxID int, mail types.MailItem) {
	s.db.GetDB().Exec(`
		INSERT OR IGNORE INTO mail_records (mailbox_id, mail_id, subject, sender, received_at)
		VALUES (?, ?, ?, ?, ?)`,
		mailboxID, mail.MailID, mail.Subject, mail.From, mail.Date)
}

// CleanupExpiredMailboxes 清理过期邮箱
func (s *Service) CleanupExpiredMailboxes(ctx context.Context) error {
	// 查询过期的邮箱
	rows, err := s.db.GetDB().Query(`
		SELECT id FROM temp_mailboxes
		WHERE status = ? AND expires_at < CURRENT_TIMESTAMP`,
		database.TempMailboxStatusActive)
	if err != nil {
		return err
	}
	defer rows.Close()

	var expiredIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			continue
		}
		expiredIDs = append(expiredIDs, id)
	}

	// 释放过期邮箱
	for _, id := range expiredIDs {
		s.ReleaseMailbox(ctx, id, database.ReleaseReasonExpired)
	}

	return nil
}
