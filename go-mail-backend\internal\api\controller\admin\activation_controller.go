package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// ActivationController 激活码管理控制器
type ActivationController struct {
	adminAPI *AdminAPI
}

// NewActivationController 创建激活码管理控制器
func NewActivationController(adminAPI *AdminAPI) *ActivationController {
	return &ActivationController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册激活码管理路由
func (c *ActivationController) RegisterRoutes(v1 *gin.RouterGroup) {
	activationGroup := v1.Group("/activation")
	activationGroup.Use(middleware.JWTAuth(c.adminAPI.auth))
	{
		activationHandler := handlers.NewActivationHandler(c.adminAPI.services.Activation)
		c.adminAPI.logger.Info("注册激活码管理路由", "prefix", "/api/v1/activation")
		
		activationGroup.POST("/generate", activationHandler.GenerateCodes)
		activationGroup.POST("/verify", activationHandler.VerifyCode)
		activationGroup.GET("/list", activationHandler.ListCodes)
		activationGroup.GET("/:id", activationHandler.GetCodeInfo)
		activationGroup.DELETE("/:id", activationHandler.DeleteCode)
		activationGroup.POST("/batch-delete", activationHandler.BatchDeleteCodes)
		activationGroup.PUT("/:id/expire", activationHandler.ExpireCode)
		activationGroup.GET("/export", activationHandler.ExportCodes)
		activationGroup.GET("/stats", activationHandler.GetStats)
		
		c.adminAPI.logger.Info("激活码管理路由注册完成", "routes", []string{
			"POST /api/v1/activation/generate",
			"POST /api/v1/activation/verify",
			"GET /api/v1/activation/list",
			"GET /api/v1/activation/:id",
			"DELETE /api/v1/activation/:id",
			"POST /api/v1/activation/batch-delete",
			"PUT /api/v1/activation/:id/expire",
			"GET /api/v1/activation/export",
			"GET /api/v1/activation/stats",
		})
	}
}
