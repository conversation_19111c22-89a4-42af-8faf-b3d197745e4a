package handlers

import (
	"go-mail/internal/database"
	"go-mail/internal/scheduler"
	"go-mail/internal/services/monitor"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// MonitorHandler 监控处理器
type MonitorHandler struct {
	monitorService *monitor.Service
	scheduler      *scheduler.Scheduler
}

// NewMonitorHandler 创建监控处理器
func NewMonitorHandler(monitorService *monitor.Service, scheduler *scheduler.Scheduler) *MonitorHandler {
	return &MonitorHandler{
		monitorService: monitorService,
		scheduler:      scheduler,
	}
}

// GetStatistics 获取系统统计信息
func (h *MonitorHandler) GetStatistics(c *gin.Context) {
	// 支持模式参数：core（核心指标）、database（数据库统计）、full（完整信息）
	mode := c.DefaultQuery("mode", "full")

	var stats interface{}
	var err error

	if mode == "full" {
		stats, err = h.monitorService.GetStatistics()
	} else {
		stats, err = h.monitorService.GetStatisticsWithMode(mode)
	}

	if err != nil {
		c.J<PERSON>N(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取系统统计信息失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      stats,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetAccountStatus 获取账户状态
func (h *MonitorHandler) GetAccountStatus(c *gin.Context) {
	accountStatus, err := h.monitorService.GetAccountStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取账户状态失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      accountStatus,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetSessionStatus 获取会话状态
func (h *MonitorHandler) GetSessionStatus(c *gin.Context) {
	sessionStatus, err := h.monitorService.GetSessionStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取会话状态失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      sessionStatus,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetLogs 获取系统日志
func (h *MonitorHandler) GetLogs(c *gin.Context) {
	// 解析查询参数
	level := c.Query("level")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")
	limitStr := c.DefaultQuery("limit", "100")

	// 解析时间参数
	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, database.APIResponse{
				Code:      400,
				Message:   "开始时间格式错误",
				Error:     err.Error(),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: c.GetString("request_id"),
			})
			return
		}
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, database.APIResponse{
				Code:      400,
				Message:   "结束时间格式错误",
				Error:     err.Error(),
				Timestamp: time.Now().Format(time.RFC3339),
				RequestID: c.GetString("request_id"),
			})
			return
		}
	}

	// 解析限制参数
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		limit = 100
	}

	// 构建查询参数
	params := monitor.LogQueryParams{
		Level:     level,
		StartTime: startTime,
		EndTime:   endTime,
		Limit:     limit,
	}

	// 获取日志
	logs, err := h.monitorService.GetLogs(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取系统日志失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "获取成功",
		Data: gin.H{
			"logs":  logs,
			"total": len(logs),
			"params": gin.H{
				"level":      level,
				"start_time": startTimeStr,
				"end_time":   endTimeStr,
				"limit":      limit,
			},
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetSystemHealth 获取系统健康状态
func (h *MonitorHandler) GetSystemHealth(c *gin.Context) {
	// 获取系统统计信息
	stats, err := h.monitorService.GetStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取系统健康状态失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 计算健康状态
	health := gin.H{
		"status": "healthy",
		"checks": gin.H{
			"database": gin.H{
				"status":  "healthy",
				"message": "数据库连接正常",
			},
			"accounts": gin.H{
				"status":       getAccountHealthStatus(stats.Accounts.Active, stats.Accounts.Total),
				"message":      getAccountHealthMessage(stats.Accounts.Active, stats.Accounts.Total),
				"active_count": stats.Accounts.Active,
				"total_count":  stats.Accounts.Total,
			},
			"mailboxes": gin.H{
				"status":          "healthy",
				"message":         "邮箱服务正常",
				"active_count":    stats.Mailboxes.CurrentlyActive,
				"total_allocated": stats.Mailboxes.TotalAllocated,
			},
			"system": gin.H{
				"status":       "healthy",
				"message":      "系统运行正常",
				"uptime":       stats.System.Uptime,
				"memory_usage": stats.System.MemoryUsage,
				"goroutines":   stats.System.GoRoutines,
			},
		},
		"timestamp": time.Now().Format(time.RFC3339),
	}

	// 如果有任何检查失败，设置整体状态为不健康
	overallStatus := "healthy"
	for _, check := range health["checks"].(gin.H) {
		if checkMap, ok := check.(gin.H); ok {
			if status, exists := checkMap["status"]; exists && status != "healthy" {
				overallStatus = "unhealthy"
				break
			}
		}
	}
	health["status"] = overallStatus

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      health,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetMetrics 获取系统指标（Prometheus格式）
func (h *MonitorHandler) GetMetrics(c *gin.Context) {
	stats, err := h.monitorService.GetStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取系统指标失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 构建Prometheus格式的指标
	metrics := gin.H{
		"accounts_total":             stats.Accounts.Total,
		"accounts_active":            stats.Accounts.Active,
		"accounts_inactive":          stats.Accounts.Inactive,
		"mailboxes_total_allocated":  stats.Mailboxes.TotalAllocated,
		"mailboxes_currently_active": stats.Mailboxes.CurrentlyActive,
		"mailboxes_today_allocated":  stats.Mailboxes.TodayAllocated,
		"mailboxes_today_released":   stats.Mailboxes.TodayReleased,
		"activation_codes_total":     stats.ActivationCodes.TotalGenerated,
		"activation_codes_used":      stats.ActivationCodes.Used,
		"activation_codes_unused":    stats.ActivationCodes.Unused,
		"activation_codes_expired":   stats.ActivationCodes.Expired,
		"system_goroutines":          stats.System.GoRoutines,
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      metrics,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// 辅助函数

// getAccountHealthStatus 获取账户健康状态
func getAccountHealthStatus(active, total int) string {
	if total == 0 {
		return "critical"
	}

	ratio := float64(active) / float64(total)
	if ratio >= 0.8 {
		return "healthy"
	} else if ratio >= 0.5 {
		return "warning"
	} else {
		return "critical"
	}
}

// getAccountHealthMessage 获取账户健康消息
func getAccountHealthMessage(active, total int) string {
	if total == 0 {
		return "没有可用的邮件账户"
	}

	ratio := float64(active) / float64(total)
	if ratio >= 0.8 {
		return "账户状态良好"
	} else if ratio >= 0.5 {
		return "部分账户不可用"
	} else {
		return "大部分账户不可用"
	}
}

// GetTaskStatus 获取定时任务状态
func (h *MonitorHandler) GetTaskStatus(c *gin.Context) {
	if h.scheduler == nil {
		c.JSON(http.StatusServiceUnavailable, database.APIResponse{
			Code:      503,
			Message:   "任务调度器未启用",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	taskStatuses := h.scheduler.GetAllTaskStatusArray()

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      taskStatuses,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ControlTask 控制定时任务
func (h *MonitorHandler) ControlTask(c *gin.Context) {
	if h.scheduler == nil {
		c.JSON(http.StatusServiceUnavailable, database.APIResponse{
			Code:      503,
			Message:   "任务调度器未启用",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	taskName := c.Param("task")
	action := c.Param("action")

	if taskName == "" || action == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "任务名称和操作不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	var err error
	switch action {
	case "enable":
		err = h.scheduler.EnableTask(taskName)
	case "disable":
		err = h.scheduler.DisableTask(taskName)
	case "run":
		err = h.scheduler.RunTask(taskName)
	default:
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的操作，支持：enable, disable, run",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "操作失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "操作成功",
		Data: gin.H{
			"task":   taskName,
			"action": action,
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}
