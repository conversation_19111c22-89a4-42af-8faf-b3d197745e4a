<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <n-icon size="48" color="#18a058">
            <MailIcon />
          </n-icon>
          <h1>Go-Mail</h1>
        </div>
        <p class="subtitle">临时邮箱服务管理后台</p>
      </div>

      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        size="large"
        @submit.prevent="handleLogin"
      >
        <n-form-item path="username" label="用户名">
          <n-input
            v-model:value="formData.username"
            placeholder="请输入用户名"
            :disabled="isLoading"
            @keydown.enter="handleLogin"
          >
            <template #prefix>
              <n-icon>
                <PersonIcon />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="password" label="密码">
          <n-input
            v-model:value="formData.password"
            type="password"
            placeholder="请输入密码"
            show-password-on="mousedown"
            :disabled="isLoading"
            @keydown.enter="handleLogin"
          >
            <template #prefix>
              <n-icon>
                <LockClosedIcon />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item>
          <n-checkbox v-model:checked="formData.rememberMe" :disabled="isLoading">
            记住我
          </n-checkbox>
        </n-form-item>

        <n-form-item>
          <n-button
            type="primary"
            size="large"
            :loading="isLoading"
            :disabled="!formData.username || !formData.password"
            block
            @click="handleLogin"
          >
            登录
          </n-button>
        </n-form-item>
      </n-form>

      <div class="login-footer">
        <div class="device-info">
          <n-icon size="14">
            <DeviceIcon />
          </n-icon>
          <span>{{ deviceInfo.browser }} · {{ deviceInfo.platform }}</span>
        </div>
        <div class="version-info">
          v{{ version }}
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NCheckbox,
  NIcon,
  useMessage,
  type FormInst,
  type FormRules,
} from 'naive-ui'
import {
  Mail as MailIcon,
  Person as PersonIcon,
  LockClosed as LockClosedIcon,
  Desktop as DeviceIcon,
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import { getDeviceInfoSummary } from '@/utils/device'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const authStore = useAuthStore()

// 表单引用
const formRef = ref<FormInst | null>(null)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  rememberMe: false,
})

// 表单验证规则
const formRules: FormRules = {
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: ['input', 'blur'],
    },
    {
      min: 3,
      max: 20,
      message: '用户名长度应为3-20个字符',
      trigger: ['input', 'blur'],
    },
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur'],
    },
    {
      min: 6,
      message: '密码长度不能少于6个字符',
      trigger: ['input', 'blur'],
    },
  ],
}

// 加载状态
const isLoading = ref(false)

// 设备信息
const deviceInfo = ref(getDeviceInfoSummary())

// 版本信息
const version = import.meta.env.VITE_APP_VERSION || '1.0.0'

// 处理登录
const handleLogin = async () => {
  if (!formRef.value) return

  try {
    // 验证表单
    await formRef.value.validate()
    
    isLoading.value = true

    // 调用登录API
    const result = await authStore.login({
      username: formData.username,
      password: formData.password,
    })

    if (result.success) {
      message.success('登录成功')
      
      // 获取重定向路径
      const redirectPath = (route.query.redirect as string) || '/'
      
      // 跳转到目标页面
      await router.push(redirectPath)
    } else {
      message.error(result.message)
    }
  } catch (error: any) {
    console.error('Login error:', error)
    if (error.message) {
      message.error(error.message)
    } else {
      message.error('登录失败，请检查用户名和密码')
    }
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    const redirectPath = (route.query.redirect as string) || '/'
    await router.push(redirectPath)
    return
  }

  // 初始化设备指纹
  await authStore.initializeDeviceFingerprint()

  // 开发环境下自动填充默认账号
  if (import.meta.env.DEV) {
    formData.username = 'admin'
    formData.password = 'admin123'
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.logo h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-footer {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    margin: 20px;
    padding: 24px;
  }
  
  .logo h1 {
    font-size: 24px;
  }
}

/* 暗色主题适配 */
:global(.dark) .login-card {
  background: rgba(24, 24, 28, 0.95);
  color: #fff;
}

:global(.dark) .logo h1 {
  color: #fff;
}

:global(.dark) .subtitle {
  color: #ccc;
}
</style>
