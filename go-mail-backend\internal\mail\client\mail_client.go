package client

import (
	"compress/gzip"
	"context"
	"fmt"
	"go-mail/internal/errors"
	"go-mail/internal/types"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"golang.org/x/net/html"
)

// MailClient 邮件操作客户端
type MailClient struct {
	config *types.ManagerConfig
}

// NewMailClient 创建新的邮件客户端
func NewMailClient(config *types.ManagerConfig) *MailClient {
	return &MailClient{
		config: config,
	}
}

// GetMailList 获取邮件列表
func (c *MailClient) GetMailList(ctx context.Context, session *types.Session) (*types.MailList, error) {
	if session == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "会话信息为空", nil)
	}

	if session.Client == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "HTTP客户端为空", nil)
	}

	// 使用登录成功后存储的finalURL
	finalURL := session.FinalURL
	if finalURL == "" {
		// 如果没有存储finalURL，则构建默认的folder页面URL
		finalURL = fmt.Sprintf("https://3c-lxa.mail.com/mail/client/folder;jsessionid=%s?navsid=%s",
			session.JSessionID, url.QueryEscape(session.NavigatorSID))
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", finalURL, nil)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建邮件列表请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")

	// 调试：输出请求信息
	c.debugMailRequest(req, session.Client, finalURL)

	// 发送请求
	resp, err := session.Client.Do(req)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "获取邮件列表请求失败", err)
	}
	defer resp.Body.Close()

	// 处理302重定向（邮件文件夹为空的情况）
	if resp.StatusCode == http.StatusFound || resp.StatusCode == http.StatusSeeOther {
		fmt.Printf("调试：收到302重定向，Location: %s\n", resp.Header.Get("Location"))

		// 跟随重定向
		redirectResp, err := c.followRedirect(ctx, session, resp, finalURL)
		if err != nil {
			return nil, err
		}
		defer redirectResp.Body.Close()
		resp = redirectResp // 使用重定向后的响应
	}

	// 检查最终响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("获取邮件列表返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 读取响应体
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "解压gzip响应失败", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "读取邮件列表响应失败", err)
	}

	// 解析HTML获取邮件列表
	mailList, err := c.parseMailList(string(body))
	if err != nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "解析邮件列表失败", err)
	}

	return mailList, nil
}

// GetMailContent 获取邮件内容
func (c *MailClient) GetMailContent(ctx context.Context, session *types.Session, mailID string) (*types.MailContent, error) {
	return c.GetMailContentWithItem(ctx, session, mailID, nil)
}

// GetMailContentWithItem 获取邮件内容（带邮件列表项信息）
func (c *MailClient) GetMailContentWithItem(ctx context.Context, session *types.Session, mailID string, mailItem *types.MailItem) (*types.MailContent, error) {
	if session == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "会话信息为空", nil)
	}

	if session.Client == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "HTTP客户端为空", nil)
	}

	if mailID == "" {
		return nil, errors.NewValidationError(errors.ErrCodeValidationRequired, "邮件ID不能为空", nil)
	}

	// 构建邮件内容请求URL
	// 格式：/mail/client/mailbody/{mailID}/true;jsessionid={jsessionID}
	reqURL := fmt.Sprintf("https://3c-lxa.mail.com/mail/client/mailbody/%s/true;jsessionid=%s",
		mailID, session.JSessionID)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建邮件内容请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")

	// 发送请求
	resp, err := session.Client.Do(req)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "获取邮件内容请求失败", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("获取邮件内容返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 读取响应体
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "解压gzip响应失败", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "读取邮件内容响应失败", err)
	}

	// 解析HTML获取邮件内容
	mailContent, err := c.parseMailContent(string(body), mailID)
	if err != nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "解析邮件内容失败", err)
	}

	// 如果提供了邮件列表项信息，使用其中的头信息
	if mailItem != nil {
		mailContent.From = mailItem.From
		mailContent.Subject = mailItem.Subject
		mailContent.Date = mailItem.Date

		// 如果有完整的title信息，优先使用
		if mailItem.FromTitle != "" {
			mailContent.From = mailItem.FromTitle
		}
		if mailItem.SubjectTitle != "" {
			mailContent.Subject = mailItem.SubjectTitle
		}

		// 设置收件人信息（通常是当前登录用户）
		if session.Account.Username != "" {
			mailContent.To = session.Account.Username
		}
	}

	return mailContent, nil
}

// setCommonHeaders 设置通用请求头
func (c *MailClient) setCommonHeaders(req *http.Request) {
	req.Header.Set("User-Agent", c.config.UserAgent)
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
}

// parseMailList 解析邮件列表HTML
func (c *MailClient) parseMailList(htmlContent string) (*types.MailList, error) {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	mailList := &types.MailList{
		Items: make([]types.MailItem, 0),
		Page:  0, // 默认第一页
	}

	// 首先检查是否包含"Folder does not contain any messages"消息
	if c.checkEmptyFolderMessage(htmlContent) {
		fmt.Printf("调试：检测到空邮件文件夹消息\n")
		return mailList, nil // 返回空列表，这是正常情况
	}

	// 查找 <tbody data-oao-page="0"> 容器
	tbody := c.findTbodyWithPage(doc, "0")
	if tbody == nil {
		fmt.Printf("调试：未找到邮件列表容器，返回空列表\n")
		return mailList, nil // 返回空列表而不是错误，可能是没有邮件
	}

	// 解析每个 <tr> 元素
	c.parseTrElements(tbody, mailList)

	return mailList, nil
}

// findTbodyWithPage 查找指定页码的tbody元素
func (c *MailClient) findTbodyWithPage(n *html.Node, page string) *html.Node {
	if n.Type == html.ElementNode && n.Data == "tbody" {
		for _, attr := range n.Attr {
			if attr.Key == "data-oao-page" && attr.Val == page {
				return n
			}
		}
	}

	for child := n.FirstChild; child != nil; child = child.NextSibling {
		if result := c.findTbodyWithPage(child, page); result != nil {
			return result
		}
	}

	return nil
}

// parseTrElements 解析tr元素获取邮件信息
func (c *MailClient) parseTrElements(tbody *html.Node, mailList *types.MailList) {
	for tr := tbody.FirstChild; tr != nil; tr = tr.NextSibling {
		if tr.Type == html.ElementNode && tr.Data == "tr" {
			mailItem := c.parseSingleTr(tr)
			if mailItem != nil && mailItem.MailID != "" {
				// 验证邮件项的完整性
				if c.validateMailItem(mailItem) {
					mailList.Items = append(mailList.Items, *mailItem)
				} else {
					fmt.Printf("调试：邮件项验证失败，MailID: %s\n", mailItem.MailID)
				}
			}
		}
	}

	fmt.Printf("调试：成功解析 %d 个邮件项\n", len(mailList.Items))
}

// parseSingleTr 解析单个tr元素
func (c *MailClient) parseSingleTr(tr *html.Node) *types.MailItem {
	mailItem := &types.MailItem{}

	// 提取 data-oao-mailid 和 data-folderid
	for _, attr := range tr.Attr {
		switch attr.Key {
		case "data-oao-mailid":
			mailItem.MailID = attr.Val
		case "data-folderid":
			mailItem.FolderID = attr.Val
		case "class":
			// 检查是否包含 "new" 类名
			if strings.Contains(attr.Val, "new") {
				mailItem.IsNew = true
			}
			// 检查是否包含 "starred" 类名
			if strings.Contains(attr.Val, "starred") || strings.Contains(attr.Val, "star") {
				mailItem.IsStarred = true
			}
		}
	}

	// 如果没有邮件ID，跳过这个tr
	if mailItem.MailID == "" {
		fmt.Printf("调试：跳过无MailID的tr元素\n")
		return nil
	}

	// 解析tr内部的元素
	c.parseTrContent(tr, mailItem)

	// 调试输出解析结果
	fmt.Printf("调试：解析邮件项 - ID: %s, From: %s, Subject: %s, Date: %s\n",
		mailItem.MailID, mailItem.From, mailItem.Subject, mailItem.Date)

	return mailItem
}

// parseTrContent 解析tr内部内容
func (c *MailClient) parseTrContent(tr *html.Node, mailItem *types.MailItem) {
	// 查找 .name、.date、.subject 元素
	c.findMailElements(tr, mailItem)
}

// findMailElements 查找邮件相关元素
func (c *MailClient) findMailElements(n *html.Node, mailItem *types.MailItem) {
	if n.Type == html.ElementNode {
		// 检查class属性
		for _, attr := range n.Attr {
			if attr.Key == "class" {
				className := strings.TrimSpace(attr.Val)

				// 处理div元素
				if n.Data == "div" {
					switch className {
					case "name", "sender", "from":
						// 提取发件人信息
						if mailItem.From == "" {
							mailItem.From = c.getTextContent(n)
							mailItem.FromTitle = c.getAttributeValue(n, "title")
						}
					case "date", "time", "timestamp":
						// 提取日期信息
						if mailItem.Date == "" {
							rawDate := c.getTextContent(n)
							mailItem.Date = c.formatMailDate(rawDate)
						}
					}
				}

				// 处理span元素
				if n.Data == "span" {
					switch className {
					case "subject", "title":
						// 提取主题信息
						if mailItem.Subject == "" {
							mailItem.Subject = c.getTextContent(n)
							mailItem.SubjectTitle = c.getAttributeValue(n, "title")
						}
					case "name", "sender", "from":
						// 有些情况下发件人可能在span中
						if mailItem.From == "" {
							mailItem.From = c.getTextContent(n)
							mailItem.FromTitle = c.getAttributeValue(n, "title")
						}
					}
				}

				// 处理包含多个class的情况
				if strings.Contains(className, "name") || strings.Contains(className, "sender") {
					if mailItem.From == "" {
						mailItem.From = c.getTextContent(n)
						mailItem.FromTitle = c.getAttributeValue(n, "title")
					}
				}
				if strings.Contains(className, "subject") || strings.Contains(className, "title") {
					if mailItem.Subject == "" {
						mailItem.Subject = c.getTextContent(n)
						mailItem.SubjectTitle = c.getAttributeValue(n, "title")
					}
				}
				if strings.Contains(className, "date") || strings.Contains(className, "time") {
					if mailItem.Date == "" {
						rawDate := c.getTextContent(n)
						mailItem.Date = c.formatMailDate(rawDate)
					}
				}
			}
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		c.findMailElements(child, mailItem)
	}
}

// getTextContent 获取节点的文本内容
func (c *MailClient) getTextContent(n *html.Node) string {
	if n.Type == html.TextNode {
		return strings.TrimSpace(n.Data)
	}

	var text strings.Builder
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		text.WriteString(c.getTextContent(child))
	}

	return strings.TrimSpace(text.String())
}

// getAttributeValue 获取节点的指定属性值
func (c *MailClient) getAttributeValue(n *html.Node, attrName string) string {
	for _, attr := range n.Attr {
		if attr.Key == attrName {
			return attr.Val
		}
	}
	return ""
}

// parseMailContent 解析邮件内容HTML
func (c *MailClient) parseMailContent(htmlContent string, mailID string) (*types.MailContent, error) {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("解析邮件内容HTML失败: %w", err)
	}

	mailContent := &types.MailContent{
		MailID:      mailID,
		Headers:     make(map[string]string),
		Attachments: make([]types.MailAttachment, 0),
	}

	// 解析邮件头信息和正文
	c.parseMailHeaders(doc, mailContent)
	c.parseMailBody(doc, mailContent)
	c.parseMailAttachments(doc, mailContent)

	return mailContent, nil
}

// parseMailHeaders 解析邮件头信息
func (c *MailClient) parseMailHeaders(doc *html.Node, mailContent *types.MailContent) {
	// 查找邮件头信息，通常在特定的div或table中
	// 这里需要根据实际的HTML结构来调整
	c.findMailHeaderElements(doc, mailContent)
}

// findMailHeaderElements 查找邮件头元素
func (c *MailClient) findMailHeaderElements(n *html.Node, mailContent *types.MailContent) {
	// 查找发件人、收件人、主题、日期等信息
	// 这些信息通常在邮件头部的特定元素中
	if n.Type == html.ElementNode {
		switch n.Data {
		case "div", "span", "td":
			// 检查是否包含邮件头信息
			c.extractHeaderInfo(n, mailContent)
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		c.findMailHeaderElements(child, mailContent)
	}
}

// extractHeaderInfo 提取邮件头信息
func (c *MailClient) extractHeaderInfo(n *html.Node, mailContent *types.MailContent) {
	// 根据class或id属性提取相应的信息
	for _, attr := range n.Attr {
		if attr.Key == "class" || attr.Key == "id" {
			value := strings.ToLower(attr.Val)
			text := strings.TrimSpace(c.getTextContent(n))

			if text == "" {
				continue
			}

			// 根据class名称判断是什么信息
			if strings.Contains(value, "from") || strings.Contains(value, "sender") {
				if mailContent.From == "" {
					mailContent.From = text
					// 同时保存到Headers中
					mailContent.Headers["From"] = text
				}
			} else if strings.Contains(value, "to") || strings.Contains(value, "recipient") {
				if mailContent.To == "" {
					mailContent.To = text
					mailContent.Headers["To"] = text
				}
			} else if strings.Contains(value, "subject") || strings.Contains(value, "title") {
				if mailContent.Subject == "" {
					mailContent.Subject = text
					mailContent.Headers["Subject"] = text
				}
			} else if strings.Contains(value, "date") || strings.Contains(value, "time") {
				if mailContent.Date == "" {
					mailContent.Date = text
					mailContent.Headers["Date"] = text
				}
			} else if strings.Contains(value, "cc") {
				mailContent.Headers["Cc"] = text
			} else if strings.Contains(value, "bcc") {
				mailContent.Headers["Bcc"] = text
			} else if strings.Contains(value, "reply") {
				mailContent.Headers["Reply-To"] = text
			}
		}
	}

	// 检查title属性，有时重要信息在title中
	if titleAttr := c.getAttributeValue(n, "title"); titleAttr != "" {
		titleText := strings.TrimSpace(titleAttr)
		if titleText != "" {
			// 尝试从title中提取邮箱地址
			if strings.Contains(titleText, "@") {
				if mailContent.From == "" && (strings.Contains(strings.ToLower(titleText), "from") || strings.Contains(strings.ToLower(titleText), "sender")) {
					mailContent.From = titleText
					mailContent.Headers["From"] = titleText
				} else if mailContent.To == "" && (strings.Contains(strings.ToLower(titleText), "to") || strings.Contains(strings.ToLower(titleText), "recipient")) {
					mailContent.To = titleText
					mailContent.Headers["To"] = titleText
				}
			}
		}
	}
}

// parseMailBody 解析邮件正文
func (c *MailClient) parseMailBody(doc *html.Node, mailContent *types.MailContent) {
	// 查找邮件正文内容
	bodyNode := c.findMailBodyNode(doc)
	if bodyNode != nil {
		// 获取HTML格式的正文
		mailContent.Body = c.getInnerHTML(bodyNode)
		// 获取纯文本格式的正文
		mailContent.TextBody = c.getTextContent(bodyNode)
	}
}

// findMailBodyNode 查找邮件正文节点
func (c *MailClient) findMailBodyNode(n *html.Node) *html.Node {
	// 查找包含邮件正文的节点，通常有特定的class或id
	if n.Type == html.ElementNode {
		for _, attr := range n.Attr {
			if attr.Key == "class" || attr.Key == "id" {
				value := strings.ToLower(attr.Val)
				// 常见的邮件正文class名称
				if strings.Contains(value, "mail-body") ||
					strings.Contains(value, "message-body") ||
					strings.Contains(value, "message-content") ||
					strings.Contains(value, "email-body") ||
					strings.Contains(value, "email-content") ||
					strings.Contains(value, "content") ||
					strings.Contains(value, "body-content") ||
					strings.Contains(value, "text-content") ||
					strings.Contains(value, "main-content") ||
					value == "body" {

					// 验证这个节点确实包含文本内容
					textContent := strings.TrimSpace(c.getTextContent(n))
					if len(textContent) > 10 { // 至少要有一些文本内容
						return n
					}
				}
			}
		}

		// 如果没有找到特定的class，尝试查找包含大量文本的div或p元素
		if n.Data == "div" || n.Data == "p" || n.Data == "article" || n.Data == "section" {
			textContent := strings.TrimSpace(c.getTextContent(n))
			// 如果这个元素包含较多文本内容，可能是邮件正文
			if len(textContent) > 50 {
				// 检查是否包含邮件正文的特征
				if !strings.Contains(strings.ToLower(textContent), "header") &&
					!strings.Contains(strings.ToLower(textContent), "footer") &&
					!strings.Contains(strings.ToLower(textContent), "navigation") {
					return n
				}
			}
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		if result := c.findMailBodyNode(child); result != nil {
			return result
		}
	}

	return nil
}

// getInnerHTML 获取节点的内部HTML
func (c *MailClient) getInnerHTML(n *html.Node) string {
	var html strings.Builder
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		c.renderNode(child, &html)
	}
	return html.String()
}

// renderNode 渲染HTML节点
func (c *MailClient) renderNode(n *html.Node, htmlBuilder *strings.Builder) {
	switch n.Type {
	case html.ElementNode:
		htmlBuilder.WriteString("<")
		htmlBuilder.WriteString(n.Data)
		for _, attr := range n.Attr {
			htmlBuilder.WriteString(" ")
			htmlBuilder.WriteString(attr.Key)
			htmlBuilder.WriteString("=\"")
			htmlBuilder.WriteString(attr.Val)
			htmlBuilder.WriteString("\"")
		}
		htmlBuilder.WriteString(">")

		for child := n.FirstChild; child != nil; child = child.NextSibling {
			c.renderNode(child, htmlBuilder)
		}

		htmlBuilder.WriteString("</")
		htmlBuilder.WriteString(n.Data)
		htmlBuilder.WriteString(">")
	case html.TextNode:
		htmlBuilder.WriteString(n.Data)
	}
}

// parseMailAttachments 解析邮件附件
func (c *MailClient) parseMailAttachments(doc *html.Node, mailContent *types.MailContent) {
	// 查找附件信息，通常在特定的div或列表中
	c.findAttachmentElements(doc, mailContent)
}

// findAttachmentElements 查找附件元素
func (c *MailClient) findAttachmentElements(n *html.Node, mailContent *types.MailContent) {
	if n.Type == html.ElementNode {
		// 查找包含附件信息的元素
		for _, attr := range n.Attr {
			if attr.Key == "class" || attr.Key == "id" {
				value := strings.ToLower(attr.Val)
				if strings.Contains(value, "attachment") || strings.Contains(value, "file") {
					attachment := c.parseAttachment(n)
					if attachment != nil {
						mailContent.Attachments = append(mailContent.Attachments, *attachment)
					}
				}
			}
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		c.findAttachmentElements(child, mailContent)
	}
}

// parseAttachment 解析单个附件
func (c *MailClient) parseAttachment(n *html.Node) *types.MailAttachment {
	attachment := &types.MailAttachment{}

	// 提取附件名称
	attachment.Name = strings.TrimSpace(c.getTextContent(n))

	// 查找下载链接
	if link := c.findDownloadLink(n); link != "" {
		attachment.URL = link
	}

	// 尝试从属性中提取附件信息
	for _, attr := range n.Attr {
		switch attr.Key {
		case "data-filename", "data-name":
			if attachment.Name == "" {
				attachment.Name = attr.Val
			}
		case "data-size":
			if size, err := strconv.ParseInt(attr.Val, 10, 64); err == nil {
				attachment.Size = size
			}
		case "data-type", "data-mimetype":
			attachment.MimeType = attr.Val
		case "data-id", "data-attachment-id":
			attachment.ID = attr.Val
		}
	}

	// 尝试从文件名推断MIME类型
	if attachment.MimeType == "" && attachment.Name != "" {
		attachment.MimeType = c.guessMimeType(attachment.Name)
	}

	// 如果没有找到有效的附件信息，返回nil
	if attachment.Name == "" && attachment.URL == "" {
		return nil
	}

	return attachment
}

// guessMimeType 根据文件扩展名推断MIME类型
func (c *MailClient) guessMimeType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".pdf":
		return "application/pdf"
	case ".doc":
		return "application/msword"
	case ".docx":
		return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	case ".xls":
		return "application/vnd.ms-excel"
	case ".xlsx":
		return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".txt":
		return "text/plain"
	case ".zip":
		return "application/zip"
	case ".rar":
		return "application/x-rar-compressed"
	default:
		return "application/octet-stream"
	}
}

// findDownloadLink 查找下载链接
func (c *MailClient) findDownloadLink(n *html.Node) string {
	if n.Type == html.ElementNode && n.Data == "a" {
		for _, attr := range n.Attr {
			if attr.Key == "href" {
				return attr.Val
			}
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		if link := c.findDownloadLink(child); link != "" {
			return link
		}
	}

	return ""
}

// debugMailRequest 调试输出邮件请求信息
func (c *MailClient) debugMailRequest(req *http.Request, client *http.Client, requestURL string) {
	/* fmt.Printf("\n=== 调试：邮件请求信息 ===\n")
	fmt.Printf("请求URL: %s\n", requestURL)
	fmt.Printf("请求方法: %s\n", req.Method)

	// 输出请求头
	fmt.Printf("\n请求头:\n")
	for name, values := range req.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", name, value)
		}
	}

	// 检查客户端的cookies
	if u, err := url.Parse(requestURL); err == nil {
		cookies := client.Jar.Cookies(u)
		fmt.Printf("\n该请求将携带的Cookies (%d个):\n", len(cookies))
		if len(cookies) == 0 {
			fmt.Printf("  ❌ 警告：没有cookies将被发送！\n")
		} else {
			for _, cookie := range cookies {
				fmt.Printf("  %s=%s\n", cookie.Name, cookie.Value)
			}
		}
	}

	fmt.Printf("=== 邮件请求调试结束 ===\n\n") */
}

// followRedirect 跟随302重定向请求
func (c *MailClient) followRedirect(ctx context.Context, session *types.Session, originalResp *http.Response, baseURL string) (*http.Response, error) {
	// 获取重定向Location
	location := originalResp.Header.Get("Location")
	if location == "" {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "302重定向响应缺少Location头", nil)
	}

	// 处理相对URL
	var redirectURL string
	if strings.HasPrefix(location, "./") {
		// 相对路径，需要基于原始URL构建完整URL
		if u, err := url.Parse(baseURL); err == nil {
			// 移除原始URL的路径部分，保留到client/
			basePath := "/mail/client/"
			redirectPath := strings.TrimPrefix(location, "./")

			// Location已经包含了完整的路径和参数，直接拼接即可
			redirectURL = fmt.Sprintf("%s://%s%s%s", u.Scheme, u.Host, basePath, redirectPath)
		} else {
			return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "解析基础URL失败", err)
		}
	} else if strings.HasPrefix(location, "/") {
		// 绝对路径
		if u, err := url.Parse(baseURL); err == nil {
			redirectURL = fmt.Sprintf("%s://%s%s", u.Scheme, u.Host, location)
		} else {
			return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "解析基础URL失败", err)
		}
	} else {
		// 完整URL
		redirectURL = location
	}

	fmt.Printf("调试：重定向到URL: %s\n", redirectURL)

	// 创建重定向请求
	redirectReq, err := http.NewRequestWithContext(ctx, "GET", redirectURL, nil)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建重定向请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(redirectReq)
	redirectReq.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8")
	redirectReq.Header.Set("Accept-Encoding", "gzip, deflate, br")
	redirectReq.Header.Set("Referer", baseURL) // 设置Referer为原始URL

	// 发送重定向请求
	redirectResp, err := session.Client.Do(redirectReq)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "重定向请求失败", err)
	}

	fmt.Printf("调试：重定向响应状态码: %d\n", redirectResp.StatusCode)
	return redirectResp, nil
}

// checkEmptyFolderMessage 检查HTML内容是否包含空文件夹消息
func (c *MailClient) checkEmptyFolderMessage(htmlContent string) bool {
	// 检查常见的空文件夹消息
	emptyMessages := []string{
		"Folder does not contain any messages",
		"文件夹不包含任何邮件",
		"该文件夹为空",
		"No messages in this folder",
		"Empty folder",
	}

	// 转换为小写进行不区分大小写的匹配
	lowerContent := strings.ToLower(htmlContent)

	for _, message := range emptyMessages {
		if strings.Contains(lowerContent, strings.ToLower(message)) {
			fmt.Printf("调试：找到空文件夹消息: %s\n", message)
			return true
		}
	}

	return false
}

// formatMailDate 格式化邮件日期
func (c *MailClient) formatMailDate(rawDate string) string {
	if rawDate == "" {
		return ""
	}

	// 去除多余的空格
	rawDate = strings.TrimSpace(rawDate)

	// 原始日期字符串处理

	// 获取中国时区
	chinaLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		// 如果加载时区失败，使用UTC+8
		chinaLocation = time.FixedZone("CST", 8*3600)
	}

	// 获取当前时间（中国时区）
	now := time.Now().In(chinaLocation)
	currentDate := now.Format("2006-01-02")

	// 尝试解析不同的时间格式
	timeFormats := []string{
		"3:04 PM",    // 12小时制，如 "10:47 AM"
		"15:04",      // 24小时制，如 "10:47"
		"3:04:05 PM", // 12小时制带秒
		"15:04:05",   // 24小时制带秒
	}

	var parsedTime time.Time
	var parseErr error

	// 尝试解析时间部分
	for _, format := range timeFormats {
		parsedTime, parseErr = time.Parse(format, rawDate)
		if parseErr == nil {
			// 解析成功，但需要确定原始时间的时区
			// 根据邮件服务器的特点，原始时间可能是不同的时区

			year, month, day := now.Date()

			// 根据实际测试，确定正确的时区转换
			// 如果原始时间是10:47 AM，实际发送时间应该是16:47（下午4:47），
			// 那么原始时间应该是UTC+2时区（10:47 + 6小时 = 16:47）

			// 假设原始时间是UTC+2时区（可能是邮件服务器所在的欧洲时区）
			serverTimezone := time.FixedZone("ServerTZ", 2*3600) // UTC+2
			serverTime := time.Date(year, month, day, parsedTime.Hour(), parsedTime.Minute(), parsedTime.Second(), 0, serverTimezone)
			chinaTimeFromServer := serverTime.In(chinaLocation)

			// 时区转换：UTC+2 -> UTC+8（中国时区）

			parsedTime = chinaTimeFromServer

			break
		}
	}

	// 如果解析失败，尝试解析完整的日期时间格式
	if parseErr != nil {
		fullFormats := []string{
			"2006-01-02 15:04:05",
			"2006/01/02 15:04:05",
			"Jan 2, 2006 3:04 PM",
			"January 2, 2006 3:04 PM",
			"2006-01-02 3:04 PM",
			"2006/01/02 3:04 PM",
		}

		for _, format := range fullFormats {
			parsedTime, parseErr = time.Parse(format, rawDate)
			if parseErr == nil {
				// 转换到中国时区
				parsedTime = parsedTime.In(chinaLocation)
				break
			}
		}
	}

	// 如果仍然解析失败，返回原始字符串加上当前日期
	if parseErr != nil {
		return fmt.Sprintf("%s %s", currentDate, rawDate)
	}

	// 格式化为期望的格式：2025-07-20 16:47
	return parsedTime.Format("2006-01-02 15:04")
}

// validateMailItem 验证邮件项的完整性
func (c *MailClient) validateMailItem(mailItem *types.MailItem) bool {
	if mailItem == nil {
		return false
	}

	// 必须有邮件ID
	if mailItem.MailID == "" {
		return false
	}

	// 至少要有发件人或主题之一
	if mailItem.From == "" && mailItem.Subject == "" {
		return false
	}

	return true
}
