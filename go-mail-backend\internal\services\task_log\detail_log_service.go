package task_log

import (
	"encoding/json"
	"fmt"
	"go-mail/internal/database"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// DetailLogService 详细日志文件管理服务
type DetailLogService struct {
	baseDir string // 基础目录，例如 "temp/mail-logs"
}

// NewDetailLogService 创建详细日志服务
func NewDetailLogService(baseDir string) *DetailLogService {
	// 如果传入空字符串或相对路径，使用系统临时目录
	if baseDir == "" || !filepath.IsAbs(baseDir) {
		baseDir = filepath.Join(os.TempDir(), "go-mail-logs")
	}

	service := &DetailLogService{
		baseDir: baseDir,
	}

	// 确保基础目录存在
	if err := service.ensureBaseDir(); err != nil {
		// 如果创建失败，回退到系统临时目录
		fallbackDir := filepath.Join(os.TempDir(), "go-mail-logs-fallback")
		service.baseDir = fallbackDir
		service.ensureBaseDir() // 忽略错误，让后续操作处理
	}

	return service
}

// CreateLogFile 创建详细日志文件
func (s *DetailLogService) CreateLogFile(taskID, email string) (string, error) {
	// 生成文件路径：{系统临时目录}/go-mail-logs/YYYY-MM-DD/email_timestamp.json
	now := time.Now()
	dateDir := now.Format("2006-01-02")
	timestamp := now.Format("20060102_150405")

	// 清理邮箱地址中的特殊字符，确保文件名在所有操作系统上都有效
	cleanEmail := strings.ReplaceAll(email, "@", "_at_")
	cleanEmail = strings.ReplaceAll(cleanEmail, ".", "_")
	cleanEmail = strings.ReplaceAll(cleanEmail, "+", "_plus_")
	cleanEmail = strings.ReplaceAll(cleanEmail, "-", "_")
	// 移除其他可能有问题的字符
	cleanEmail = strings.ReplaceAll(cleanEmail, " ", "_")
	cleanEmail = strings.ReplaceAll(cleanEmail, ":", "_")
	cleanEmail = strings.ReplaceAll(cleanEmail, "/", "_")
	cleanEmail = strings.ReplaceAll(cleanEmail, "\\", "_")

	fileName := fmt.Sprintf("%s_%s.json", cleanEmail, timestamp)
	filePath := filepath.Join(s.baseDir, dateDir, fileName)

	// 确保目录存在，使用跨平台兼容的权限
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("创建日志目录失败 %s: %w", dir, err)
	}

	// 创建初始日志结构
	initialLog := database.TaskLogDetail{
		TaskID:      taskID,
		Email:       email,
		StartTime:   now,
		Steps:       []database.TaskLogStep{},
		FinalStatus: "running",
		Metadata:    make(map[string]interface{}),
	}

	// 写入初始内容
	if err := s.writeLogFile(filePath, initialLog); err != nil {
		return "", fmt.Errorf("写入初始日志失败: %w", err)
	}

	return filePath, nil
}

// AddStep 添加执行步骤到日志文件
func (s *DetailLogService) AddStep(filePath string, step database.TaskLogStep) error {
	// 读取现有日志
	logDetail, err := s.ReadLogFile(filePath)
	if err != nil {
		return fmt.Errorf("读取日志文件失败: %w", err)
	}

	// 添加新步骤
	logDetail.Steps = append(logDetail.Steps, step)

	// 写回文件
	return s.writeLogFile(filePath, *logDetail)
}

// UpdateStatus 更新日志状态
func (s *DetailLogService) UpdateStatus(filePath, status string, errorDetails *string) error {
	// 读取现有日志
	logDetail, err := s.ReadLogFile(filePath)
	if err != nil {
		return fmt.Errorf("读取日志文件失败: %w", err)
	}

	// 更新状态
	logDetail.FinalStatus = status
	logDetail.ErrorDetails = errorDetails

	// 如果是完成状态，设置结束时间
	if status == "success" || status == "failed" {
		now := time.Now()
		logDetail.EndTime = &now
	}

	// 写回文件
	return s.writeLogFile(filePath, *logDetail)
}

// ReadLogFile 读取日志文件
func (s *DetailLogService) ReadLogFile(filePath string) (*database.TaskLogDetail, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("日志文件不存在: %s", filePath)
	}

	// 读取文件内容
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// 解析JSON
	var logDetail database.TaskLogDetail
	if err := json.Unmarshal(data, &logDetail); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}

	return &logDetail, nil
}

// writeLogFile 写入日志文件
func (s *DetailLogService) writeLogFile(filePath string, logDetail database.TaskLogDetail) error {
	// 序列化为JSON
	data, err := json.MarshalIndent(logDetail, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}

// CleanupOldLogs 清理旧的日志文件
func (s *DetailLogService) CleanupOldLogs(daysToKeep int) error {
	cutoffDate := time.Now().AddDate(0, 0, -daysToKeep)

	// 遍历基础目录
	return filepath.Walk(s.baseDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件修改时间
		if info.ModTime().Before(cutoffDate) {
			if err := os.Remove(path); err != nil {
				return fmt.Errorf("删除旧日志文件失败 %s: %w", path, err)
			}
		}

		return nil
	})
}

// GetLogFileSize 获取日志文件大小
func (s *DetailLogService) GetLogFileSize(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// ensureBaseDir 确保基础目录存在
func (s *DetailLogService) ensureBaseDir() error {
	// 检查目录是否存在
	if _, err := os.Stat(s.baseDir); os.IsNotExist(err) {
		// 创建目录，使用跨平台兼容的权限设置
		if err := os.MkdirAll(s.baseDir, 0755); err != nil {
			return fmt.Errorf("创建日志基础目录失败 %s: %w", s.baseDir, err)
		}
	}
	return nil
}

// GetBaseDir 获取基础目录路径
func (s *DetailLogService) GetBaseDir() string {
	return s.baseDir
}

// ValidateLogFile 验证日志文件格式
func (s *DetailLogService) ValidateLogFile(filePath string) error {
	_, err := s.ReadLogFile(filePath)
	return err
}
