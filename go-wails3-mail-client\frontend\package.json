{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vue-tsc && vite build --minify false --mode development", "build": "vue-tsc && vite build --mode production", "preview": "vite preview"}, "dependencies": {"vue": "^3.2.45"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "typescript": "^4.9.3", "vite": "^5.0.0", "@wailsio/runtime": "latest", "vue-tsc": "^1.0.11"}}