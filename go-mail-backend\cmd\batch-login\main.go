package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"go-mail/internal/manager"
	"go-mail/internal/types"
	"log"
	"os"
	"strings"
	"time"
)

// AccountFile 账户文件格式
type AccountFile struct {
	Accounts []AccountEntry `json:"accounts"`
}

// AccountEntry 账户条目
type AccountEntry struct {
	Username string            `json:"username"`
	Password string            `json:"password"`
	Metadata map[string]string `json:"metadata,omitempty"`
}

func main() {
	// 命令行参数
	var (
		accountFile = flag.String("file", "", "账户文件路径 (JSON格式)")
		accountList = flag.String("accounts", "", "账户列表 (格式: user1:pass1,user2:pass2)")
		concurrent  = flag.Int("concurrent", 5, "并发登录数量")
		timeout     = flag.Duration("timeout", 60*time.Second, "总超时时间")
		proxyFile   = flag.String("proxy-file", "", "代理文件路径 (每行一个代理)")
		output      = flag.String("output", "", "结果输出文件路径")
		verbose     = flag.Bool("verbose", false, "详细输出")
		dryRun      = flag.Bool("dry-run", false, "仅验证配置，不执行登录")
	)
	flag.Parse()

	fmt.Printf("=== 批量登录测试 ===\n")

	// 加载账户
	accounts, err := loadAccounts(*accountFile, *accountList)
	if err != nil {
		log.Fatalf("加载账户失败: %v", err)
	}

	if len(accounts) == 0 {
		fmt.Println("使用方法:")
		fmt.Println("1. 使用账户文件:")
		fmt.Println("   go run main.go -file=accounts.json")
		fmt.Println("2. 使用命令行参数:")
		fmt.Println("   go run main.go -accounts=<EMAIL>:pass1,<EMAIL>:pass2")
		fmt.Println("3. 完整示例:")
		fmt.Println("   go run main.go -file=accounts.json -concurrent=10 -proxy-file=proxies.txt -output=result.json")
		flag.PrintDefaults()
		return
	}

	fmt.Printf("加载账户数量: %d\n", len(accounts))
	fmt.Printf("并发数量: %d\n", *concurrent)
	fmt.Printf("超时时间: %v\n", *timeout)

	// 加载代理（如果提供）
	var proxies []types.ProxyConfig
	if *proxyFile != "" {
		proxies, err = loadProxies(*proxyFile)
		if err != nil {
			log.Fatalf("加载代理失败: %v", err)
		}
		fmt.Printf("加载代理数量: %d\n", len(proxies))
	}

	if *dryRun {
		fmt.Printf("\n=== 配置验证 (Dry Run) ===\n")
		fmt.Printf("✓ 账户配置正确\n")
		if len(proxies) > 0 {
			fmt.Printf("✓ 代理配置正确\n")
		}
		fmt.Printf("✓ 所有配置验证通过\n")
		return
	}

	fmt.Println()

	// 创建配置
	config := &types.ManagerConfig{
		MaxConcurrent:       *concurrent,
		RequestTimeout:      30 * time.Second,
		SessionTimeout:      24 * time.Hour,
		RetryAttempts:       3,
		RetryDelay:          2 * time.Second,
		ProxyRotation:       len(proxies) > 0,
		HealthCheckInterval: 5 * time.Minute,
		LogLevel:            "info",
		UserAgent:           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
	}

	// 创建管理器
	mailManager := manager.NewMailManager(config)

	// 启动管理器
	ctx := context.Background()
	if err := mailManager.Start(ctx); err != nil {
		log.Fatalf("启动管理器失败: %v", err)
	}
	defer func() {
		if err := mailManager.Stop(ctx); err != nil {
			log.Printf("停止管理器失败: %v", err)
		}
	}()

	// 配置代理
	if len(proxies) > 0 {
		if err := mailManager.SetProxyPool(proxies); err != nil {
			log.Fatalf("设置代理池失败: %v", err)
		}
		fmt.Printf("✓ 代理池配置成功 (%d个代理)\n", len(proxies))
	}

	// 添加账户
	fmt.Printf("--- 添加账户 ---\n")
	for i, account := range accounts {
		if err := mailManager.AddAccount(account); err != nil {
			log.Printf("添加账户 %s 失败: %v", account.Username, err)
		} else if *verbose {
			fmt.Printf("✓ [%d/%d] %s\n", i+1, len(accounts), account.Username)
		}
	}

	if !*verbose {
		fmt.Printf("✓ 成功添加 %d 个账户\n", len(accounts))
	}

	// 执行批量登录
	fmt.Printf("\n--- 开始批量登录 ---\n")
	startTime := time.Now()

	loginCtx, cancel := context.WithTimeout(ctx, *timeout)
	defer cancel()

	// 显示进度（如果不是详细模式）
	if !*verbose {
		go showProgress(mailManager, len(accounts))
	}

	result, err := mailManager.BatchLogin(loginCtx, accounts)
	if err != nil {
		log.Fatalf("批量登录失败: %v", err)
	}

	totalDuration := time.Since(startTime)
	fmt.Printf("\n--- 批量登录完成 (总耗时: %v) ---\n\n", totalDuration)

	// 显示结果统计
	fmt.Printf("=== 登录结果统计 ===\n")
	fmt.Printf("总账户数: %d\n", result.Total)
	fmt.Printf("成功登录: %d (%.1f%%)\n", result.Success, float64(result.Success)/float64(result.Total)*100)
	fmt.Printf("登录失败: %d (%.1f%%)\n", result.Failed, float64(result.Failed)/float64(result.Total)*100)
	fmt.Printf("批量耗时: %v\n", result.Duration)
	fmt.Printf("总体耗时: %v\n", totalDuration)
	if result.Success > 0 {
		avgTime := result.Duration / time.Duration(result.Success)
		fmt.Printf("平均登录时间: %v\n", avgTime)
	}
	fmt.Println()

	// 显示详细结果
	if *verbose {
		fmt.Printf("=== 详细结果 ===\n")
		
		// 成功的登录
		if len(result.Results) > 0 {
			fmt.Printf("成功登录:\n")
			for username, loginResult := range result.Results {
				fmt.Printf("  ✓ %s\n", username)
				fmt.Printf("    会话ID: %s\n", loginResult.SessionID)
				fmt.Printf("    JSESSIONID: %s\n", loginResult.JSessionID)
				fmt.Printf("    登录时间: %v\n", loginResult.LoginTime)
			}
			fmt.Println()
		}

		// 失败的登录
		if len(result.Errors) > 0 {
			fmt.Printf("失败登录:\n")
			for username, err := range result.Errors {
				fmt.Printf("  ✗ %s: %v\n", username, err)
			}
			fmt.Println()
		}
	}

	// 系统统计
	stats := mailManager.GetStatistics()
	fmt.Printf("=== 系统统计 ===\n")
	fmt.Printf("总账户数: %d\n", stats.TotalAccounts)
	fmt.Printf("活跃会话: %d\n", stats.ActiveSessions)
	fmt.Printf("总登录次数: %d\n", stats.TotalLogins)
	fmt.Printf("成功登录: %d\n", stats.SuccessfulLogins)
	fmt.Printf("失败登录: %d\n", stats.FailedLogins)
	fmt.Printf("平均登录时间: %v\n", stats.AverageLoginTime)
	fmt.Printf("系统运行时间: %v\n", stats.Uptime)

	// 代理统计
	if len(proxies) > 0 {
		fmt.Printf("\n=== 代理统计 ===\n")
		proxyStatuses := mailManager.GetProxyStatus()
		enabledCount := 0
		for _, status := range proxyStatuses {
			if status.Enabled {
				enabledCount++
			}
			if *verbose {
				fmt.Printf("  %s (%s:%d): 启用=%t, 响应时间=%dms, 错误=%d\n",
					status.ID, status.Host, status.Port, status.Enabled, 
					status.ResponseTime, status.ErrorCount)
			}
		}
		if !*verbose {
			fmt.Printf("总代理数: %d, 可用代理: %d\n", len(proxyStatuses), enabledCount)
		}
	}

	// 保存结果
	if *output != "" {
		if err := saveResults(*output, result, stats); err != nil {
			log.Printf("保存结果失败: %v", err)
		} else {
			fmt.Printf("\n✓ 结果已保存到: %s\n", *output)
		}
	}

	fmt.Printf("\n=== 测试完成 ===\n")
}

// loadAccounts 加载账户
func loadAccounts(accountFile, accountList string) ([]types.Account, error) {
	var accounts []types.Account

	// 从文件加载
	if accountFile != "" {
		data, err := os.ReadFile(accountFile)
		if err != nil {
			return nil, fmt.Errorf("读取账户文件失败: %v", err)
		}

		var accountFileData AccountFile
		if err := json.Unmarshal(data, &accountFileData); err != nil {
			return nil, fmt.Errorf("解析账户文件失败: %v", err)
		}

		for _, entry := range accountFileData.Accounts {
			account := types.Account{
				Username: entry.Username,
				Password: entry.Password,
				Status:   types.AccountStatusInactive,
				Metadata: entry.Metadata,
			}
			if account.Metadata == nil {
				account.Metadata = make(map[string]string)
			}
			account.Metadata["source"] = "file"
			account.Metadata["timestamp"] = time.Now().Format(time.RFC3339)
			accounts = append(accounts, account)
		}
	}

	// 从命令行参数加载
	if accountList != "" {
		pairs := strings.Split(accountList, ",")
		for i, pair := range pairs {
			parts := strings.Split(strings.TrimSpace(pair), ":")
			if len(parts) != 2 {
				return nil, fmt.Errorf("账户格式错误: %s (应为 username:password)", pair)
			}

			account := types.Account{
				Username: strings.TrimSpace(parts[0]),
				Password: strings.TrimSpace(parts[1]),
				Status:   types.AccountStatusInactive,
				Metadata: map[string]string{
					"source":    "cmdline",
					"index":     fmt.Sprintf("%d", i),
					"timestamp": time.Now().Format(time.RFC3339),
				},
			}
			accounts = append(accounts, account)
		}
	}

	return accounts, nil
}

// loadProxies 加载代理
func loadProxies(proxyFile string) ([]types.ProxyConfig, error) {
	file, err := os.Open(proxyFile)
	if err != nil {
		return nil, fmt.Errorf("打开代理文件失败: %v", err)
	}
	defer file.Close()

	var proxies []types.ProxyConfig
	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 简化的代理解析
		parts := strings.Split(line, "://")
		if len(parts) != 2 {
			return nil, fmt.Errorf("代理格式错误 (行 %d): %s", lineNum, line)
		}

		proxyType := types.ProxyTypeHTTP
		if parts[0] == "socks5" {
			proxyType = types.ProxyTypeSOCKS5
		}

		// 解析主机和端口
		hostPort := parts[1]
		var host string
		var port int
		if _, err := fmt.Sscanf(hostPort, "%s:%d", &host, &port); err != nil {
			return nil, fmt.Errorf("解析代理地址失败 (行 %d): %v", lineNum, err)
		}

		proxy := types.ProxyConfig{
			ID:      fmt.Sprintf("proxy_%d", lineNum),
			Type:    proxyType,
			Host:    host,
			Port:    port,
			Enabled: true,
		}
		proxies = append(proxies, proxy)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取代理文件失败: %v", err)
	}

	return proxies, nil
}

// showProgress 显示进度
func showProgress(mailManager *manager.MailManager, total int) {
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			stats := mailManager.GetStatistics()
			completed := stats.SuccessfulLogins + stats.FailedLogins
			if completed >= total {
				return
			}
			fmt.Printf("\r进度: %d/%d (%.1f%%) - 成功: %d, 失败: %d", 
				completed, total, float64(completed)/float64(total)*100,
				stats.SuccessfulLogins, stats.FailedLogins)
		}
	}
}

// saveResults 保存结果
func saveResults(filename string, result *types.BatchResult, stats *types.Statistics) error {
	output := map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"batch_result": result,
		"statistics": stats,
	}

	data, err := json.MarshalIndent(output, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化结果失败: %v", err)
	}

	return os.WriteFile(filename, data, 0644)
}
