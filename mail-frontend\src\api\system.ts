import { api } from '@/utils/request'
import type {
  ApiResponse,
  SystemStats,
  TaskInfo,
  SystemConfig,
  ConfigUpdateRequest,
} from '@/types/api'

// 系统管理API
export const systemApi = {
  // 获取系统统计信息
  getSystemStats: (): Promise<ApiResponse<SystemStats>> => {
    return api.get('/monitor/statistics')
  },

  // 获取系统统计信息（支持模式参数）
  getSystemStatsWithMode: (
    mode: 'core' | 'database' | 'full' = 'full'
  ): Promise<ApiResponse<SystemStats>> => {
    return api.get('/monitor/statistics', {
      params: { mode },
    })
  },

  // 获取系统健康状态
  getHealthStatus: (): Promise<ApiResponse<{ status: string; timestamp: string }>> => {
    return api.get('/health')
  },

  // 获取定时任务列表
  getTasks: (): Promise<ApiResponse<TaskInfo[]>> => {
    return api.get('/monitor/tasks')
  },

  // 启用定时任务
  enableTask: (taskId: string): Promise<ApiResponse> => {
    return api.post(`/monitor/tasks/${taskId}/enable`)
  },

  // 禁用定时任务
  disableTask: (taskId: string): Promise<ApiResponse> => {
    return api.post(`/monitor/tasks/${taskId}/disable`)
  },

  // 手动执行定时任务
  runTask: (taskId: string): Promise<ApiResponse> => {
    return api.post(`/monitor/tasks/${taskId}/run`)
  },

  // 获取系统配置
  getSystemConfig: (): Promise<ApiResponse<SystemConfig>> => {
    return api.get('/config')
  },

  // 更新系统配置
  updateSystemConfig: (data: ConfigUpdateRequest): Promise<ApiResponse> => {
    return api.put('/config', data)
  },

  // 重置系统配置
  resetSystemConfig: (): Promise<ApiResponse> => {
    return api.post('/config/reset')
  },

  // 获取系统日志
  getSystemLogs: (params: {
    level?: string
    startTime?: string
    endTime?: string
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<any>> => {
    return api.get('/monitor/logs', { params })
  },

  // 清理系统数据
  cleanupSystem: (options: {
    cleanExpiredMailboxes?: boolean
    cleanOldLogs?: boolean
    cleanTempFiles?: boolean
  }): Promise<ApiResponse> => {
    return api.post('/system/cleanup', options)
  },

  // 备份系统数据
  backupSystem: (): Promise<ApiResponse<{ backupId: string; downloadUrl: string }>> => {
    return api.post('/system/backup')
  },

  // 获取系统版本信息
  getVersionInfo: (): Promise<
    ApiResponse<{
      version: string
      buildTime: string
      gitCommit: string
      goVersion: string
    }>
  > => {
    return api.get('/system/version')
  },
}
