package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// ConfigController 系统配置控制器
type ConfigController struct {
	adminAPI *AdminAPI
}

// NewConfigController 创建系统配置控制器
func NewConfigController(adminAPI *AdminAPI) *ConfigController {
	return &ConfigController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册系统配置路由
func (c *ConfigController) RegisterRoutes(v1 *gin.RouterGroup) {
	configGroup := v1.Group("/config")
	configGroup.Use(middleware.JWTAuth(c.adminAPI.auth))
	{
		configHandler := handlers.NewConfigHandler(c.adminAPI.services.Config)
		c.adminAPI.logger.Info("注册系统配置路由", "prefix", "/api/v1/config")
		
		configGroup.GET("", configHandler.GetConfig)
		configGroup.PUT("", configHandler.UpdateConfig)
		
		c.adminAPI.logger.Info("系统配置路由注册完成", "routes", []string{
			"GET /api/v1/config",
			"PUT /api/v1/config",
		})
	}
}
