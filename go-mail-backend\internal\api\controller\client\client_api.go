package client

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/services"
	"log/slog"

	"github.com/gin-gonic/gin"
)

// ClientAPI 客户端API控制器
type ClientAPI struct {
	services *services.Services
	auth     *auth.Auth
	database *database.Database
	logger   *slog.Logger
}

// NewClientAPI 创建客户端API控制器
func NewClientAPI(services *services.Services, auth *auth.Auth, database *database.Database, logger *slog.Logger) *ClientAPI {
	return &ClientAPI{
		services: services,
		auth:     auth,
		database: database,
		logger:   logger,
	}
}

// RegisterRoutes 注册客户端路由
func (c *ClientAPI) RegisterRoutes(v1 *gin.RouterGroup) {
	// 客户端路由组
	clientGroup := v1.Group("/client")
	clientGroup.Use(middleware.ClientAuth(c.auth, c.database))
	{
		// 客户端激活码验证
		c.registerActivationRoutes(clientGroup)
		
		// 客户端邮箱管理
		c.registerMailboxRoutes(clientGroup)
	}
}

// registerActivationRoutes 注册客户端激活码验证路由
func (c *ClientAPI) registerActivationRoutes(clientGroup *gin.RouterGroup) {
	clientActivationGroup := clientGroup.Group("/activation")
	{
		// 验证激活码
		clientActivationHandler := handlers.NewClientActivationHandler(c.services.Activation)
		clientActivationGroup.POST("/verify", clientActivationHandler.VerifyActivationCode)
	}
}

// registerMailboxRoutes 注册客户端邮箱管理路由
func (c *ClientAPI) registerMailboxRoutes(clientGroup *gin.RouterGroup) {
	clientMailboxGroup := clientGroup.Group("/mailbox")
	{
		// 客户端邮箱管理
		clientMailboxHandler := handlers.NewClientMailboxHandler(c.services.Mailbox)
		// 分配邮箱
		clientMailboxGroup.POST("/allocate", clientMailboxHandler.AllocateMailbox)
		// 查询邮箱
		clientMailboxGroup.POST("/query", clientMailboxHandler.QueryMails)
		// 获取邮箱详情
		clientMailboxGroup.POST("/mail/detail", clientMailboxHandler.GetMailDetail)
		// 释放邮箱
		clientMailboxGroup.POST("/release", clientMailboxHandler.ReleaseMailbox)
		// 获取邮箱状态
		clientMailboxGroup.POST("/status", clientMailboxHandler.GetMailboxStatus)
	}
}
