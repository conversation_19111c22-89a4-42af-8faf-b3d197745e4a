package database

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// Migration 数据库迁移结构
type Migration struct {
	Version     string
	Description string
	SQL         string
}

// MigrationManager 迁移管理器
type MigrationManager struct {
	db *Database
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *Database) *MigrationManager {
	return &MigrationManager{db: db}
}

// RunMigrations 执行数据库迁移
func (m *MigrationManager) RunMigrations(migrationsDir string) error {
	// 创建迁移记录表
	if err := m.createMigrationTable(); err != nil {
		return fmt.Errorf("创建迁移表失败: %w", err)
	}

	// 读取迁移文件
	migrations, err := m.loadMigrations(migrationsDir)
	if err != nil {
		return fmt.Errorf("加载迁移文件失败: %w", err)
	}

	// 执行迁移
	for _, migration := range migrations {
		if err := m.runMigration(migration); err != nil {
			return fmt.Errorf("执行迁移 %s 失败: %w", migration.Version, err)
		}
	}

	return nil
}

// createMigrationTable 创建迁移记录表
func (m *MigrationManager) createMigrationTable() error {
	sql := `
	CREATE TABLE IF NOT EXISTS schema_migrations (
		version TEXT PRIMARY KEY,
		description TEXT,
		applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	_, err := m.db.GetDB().Exec(sql)
	return err
}

// loadMigrations 加载迁移文件
func (m *MigrationManager) loadMigrations(migrationsDir string) ([]Migration, error) {
	files, err := os.ReadDir(migrationsDir)
	if err != nil {
		return nil, err
	}

	var migrations []Migration
	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".sql") {
			continue
		}

		// 解析文件名获取版本号
		version := strings.TrimSuffix(file.Name(), ".sql")

		// 读取文件内容
		content, err := os.ReadFile(filepath.Join(migrationsDir, file.Name()))
		if err != nil {
			return nil, err
		}

		// 解析描述（从注释中提取）
		description := m.extractDescription(string(content))

		migrations = append(migrations, Migration{
			Version:     version,
			Description: description,
			SQL:         string(content),
		})
	}

	return migrations, nil
}

// extractDescription 从SQL注释中提取描述
func (m *MigrationManager) extractDescription(sql string) string {
	lines := strings.Split(sql, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "-- 描述:") {
			return strings.TrimSpace(strings.TrimPrefix(line, "-- 描述:"))
		}
	}
	return ""
}

// runMigration 执行单个迁移
func (m *MigrationManager) runMigration(migration Migration) error {
	// 检查是否已经执行过
	var count int
	err := m.db.GetDB().QueryRow("SELECT COUNT(*) FROM schema_migrations WHERE version = ?", migration.Version).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// 已经执行过，跳过
		return nil
	}

	// 开始事务
	tx, err := m.db.GetDB().Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 执行迁移SQL
	statements := m.splitSQL(migration.SQL)
	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		if _, err := tx.Exec(stmt); err != nil {
			return fmt.Errorf("执行SQL语句失败: %s, 错误: %w", stmt, err)
		}
	}

	// 记录迁移
	_, err = tx.Exec("INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
		migration.Version, migration.Description)
	if err != nil {
		return err
	}

	// 提交事务
	return tx.Commit()
}

// splitSQL 分割SQL语句
func (m *MigrationManager) splitSQL(sql string) []string {
	// 简单的SQL分割，按分号分割
	statements := strings.Split(sql, ";")
	var result []string

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt != "" {
			result = append(result, stmt)
		}
	}

	return result
}

// GetAppliedMigrations 获取已应用的迁移
func (m *MigrationManager) GetAppliedMigrations() ([]Migration, error) {
	rows, err := m.db.GetDB().Query("SELECT version, description, applied_at FROM schema_migrations ORDER BY version")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var migrations []Migration
	for rows.Next() {
		var migration Migration
		var appliedAt string

		err := rows.Scan(&migration.Version, &migration.Description, &appliedAt)
		if err != nil {
			return nil, err
		}

		migrations = append(migrations, migration)
	}

	return migrations, nil
}

// ApplyMailboxManagementMigration 应用邮箱管理扩展迁移
func (d *Database) ApplyMailboxManagementMigration() error {
	migrationManager := NewMigrationManager(d)

	// 直接执行邮箱管理扩展的SQL（分别执行每个ALTER语句）

	// 分别执行每个ALTER语句，忽略已存在字段的错误
	alterStatements := []string{
		"ALTER TABLE accounts ADD COLUMN batch_import_id TEXT",
		"ALTER TABLE accounts ADD COLUMN verification_status TEXT DEFAULT 'unverified'",
		"ALTER TABLE accounts ADD COLUMN last_verification_time DATETIME",
		"ALTER TABLE accounts ADD COLUMN verification_error TEXT",
		"ALTER TABLE accounts ADD COLUMN is_disabled BOOLEAN DEFAULT FALSE",
		"ALTER TABLE accounts ADD COLUMN import_source TEXT DEFAULT 'manual'",
		"ALTER TABLE accounts ADD COLUMN tags TEXT",
	}

	for _, stmt := range alterStatements {
		// 执行ALTER语句，忽略"duplicate column name"错误
		_, err := d.db.Exec(stmt)
		if err != nil && !strings.Contains(err.Error(), "duplicate column name") {
			return fmt.Errorf("执行ALTER语句失败: %s, 错误: %w", stmt, err)
		}
	}

	// 执行完整的迁移脚本（创建新表等）
	return migrationManager.runMigrationFromFile("scripts/migrations/001_mailbox_management_extension.sql")
}

// runMigrationFromFile 从文件执行迁移
func (m *MigrationManager) runMigrationFromFile(filePath string) error {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取迁移文件失败: %w", err)
	}

	migration := Migration{
		Version:     "001_mailbox_management_extension",
		Description: "邮箱管理页面数据库扩展",
		SQL:         string(content),
	}

	return m.runMigration(migration)
}
