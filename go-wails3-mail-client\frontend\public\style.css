:root {
    font-family: "<PERSON>", -apple-system, BlinkMacSystemFont, "Segoe UI", "Robot<PERSON>",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);
    background-color: rgba(27, 38, 54, 1);
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
}

* {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

@font-face {
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    src: local(""),
    url("./Inter-Medium.ttf") format("truetype");
}

h3 {
    font-size: 3em;
    line-height: 1.1;
}

a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}

a:hover {
    color: #535bf2;
}

button {
    width: 60px;
    height: 30px;
    line-height: 30px;
    border-radius: 3px;
    border: none;
    margin: 0 0 0 20px;
    padding: 0 8px;
    cursor: pointer;
}

.result {
    height: 20px;
    line-height: 20px;
}

body {
    margin: 0;
    display: flex;
    place-items: center;
    place-content: center;
    min-width: 320px;
    min-height: 100vh;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

#app {
    max-width: 1280px;
    margin: 0 auto;
    text-align: center;
}

.result {
    height: 20px;
    line-height: 20px;
    margin: 1.5rem auto;
    text-align: center;
}

.footer {
    margin-top: 1rem;
    align-content: center;
    text-align: center;
    color: rgba(255, 255, 255, 0.67);
}

@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }

    a:hover {
        color: #747bff;
    }

    button {
        background-color: #f9f9f9;
    }
}


.input-box .btn:hover {
    background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
    color: #333333;
}

.input-box .input {
    border: none;
    border-radius: 3px;
    outline: none;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    color: black;
    background-color: rgba(240, 240, 240, 1);
    -webkit-font-smoothing: antialiased;
}

.input-box .input:hover {
    border: none;
    background-color: rgba(255, 255, 255, 1);
}

.input-box .input:focus {
    border: none;
    background-color: rgba(255, 255, 255, 1);
}