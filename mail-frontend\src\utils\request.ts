import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { useAuthStore } from '@/stores/auth'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const authStore = useAuthStore()

    // 添加认证token
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    // 添加设备指纹
    if (authStore.deviceFingerprint) {
      config.headers['X-Device-Fingerprint'] = authStore.deviceFingerprint
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data)
    return config
  },
  error => {
    console.error('[API Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`[API Response] ${response.config.url}`, JSON.stringify(response.data))

    // 处理不同的后端响应格式
    if (response.data) {
      // 格式1: 带有 code 字段的响应 (旧格式)
      if (typeof response.data.code !== 'undefined') {
        console.log(`[API Response] code ${response.config.url}`, JSON.stringify(response.data))
        const backendResponse = response.data
        const success = backendResponse.code >= 200 && backendResponse.code < 300

        // 转换为前端期望的格式
        response.data = {
          success,
          message: backendResponse.message,
          data: backendResponse.data,
          error: backendResponse.error,
          timestamp: backendResponse.timestamp,
        }

        // 如果不成功，抛出错误
        if (!success) {
          return Promise.reject(new Error(backendResponse.message || '请求失败'))
        }
      }
      // 格式2: 带有 success 字段的响应 (新格式，邮箱管理API)
      else if (typeof response.data.success !== 'undefined') {
        //console.log(`[API Response] success  ${response.config.url}`, JSON.stringify(response.data))
        // 已经是前端期望的格式，直接使用
        // 如果不成功，抛出错误
        if (!response.data.success) {
          return Promise.reject(new Error(response.data.message || '请求失败'))
        }
      }
    }

    return response
  },
  error => {
    console.error('[API Response Error]', error)
    const authStore = useAuthStore()

    if (error.response) {
      const { status } = error.response

      // 只处理认证相关的错误，其他错误让组件自己处理
      if (status === 401) {
        authStore.logout()
      }
    }

    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 导出请求方法
export const api = {
  get: <T = any>(url: string, config?: any): Promise<T> =>
    request.get(url, config).then(res => res.data),

  post: <T = any>(url: string, data?: any, config?: any): Promise<T> =>
    request.post(url, data, config).then(res => res.data),

  put: <T = any>(url: string, data?: any, config?: any): Promise<T> =>
    request.put(url, data, config).then(res => res.data),

  delete: <T = any>(url: string, config?: any): Promise<T> =>
    request.delete(url, config).then(res => res.data),

  patch: <T = any>(url: string, data?: any, config?: any): Promise<T> =>
    request.patch(url, data, config).then(res => res.data),
}

export default request
