package scheduler

import (
	"context"
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/manager"
	"go-mail/internal/types"
	"log/slog"
	"time"
)

// MailboxHealthMonitorTask 邮箱健康监控任务
type MailboxHealthMonitorTask struct {
	name        string
	interval    time.Duration
	enabled     bool
	logger      *slog.Logger
	mailManager *manager.MailManager
	db          *database.Database
	lastRun     time.Time
	isRunning   bool
	config      *HealthMonitorConfig
}

// HealthMonitorConfig 健康监控配置
type HealthMonitorConfig struct {
	CheckInterval    time.Duration `json:"check_interval_minutes"`
	BatchSize        int           `json:"batch_size"`
	MaxConcurrent    int           `json:"max_concurrent"`
	HealthThreshold  float64       `json:"health_threshold"`
	AlertThreshold   float64       `json:"alert_threshold"`
	DisableThreshold float64       `json:"disable_threshold"`
	AutoDisable      bool          `json:"auto_disable"`
	NotifyOnAlert    bool          `json:"notify_on_alert"`
}

// NewMailboxHealthMonitorTask 创建邮箱健康监控任务
func NewMailboxHealthMonitorTask(mailManager *manager.MailManager, db *database.Database, logger *slog.Logger) *MailboxHealthMonitorTask {
	config := &HealthMonitorConfig{
		CheckInterval:    time.Hour,
		BatchSize:        50,
		MaxConcurrent:    5,
		HealthThreshold:  0.8, // 80%以上为健康
		AlertThreshold:   0.6, // 60%以下发出警告
		DisableThreshold: 0.3, // 30%以下自动禁用
		AutoDisable:      true,
		NotifyOnAlert:    true,
	}

	return &MailboxHealthMonitorTask{
		name:        "mailbox_health_monitor",
		interval:    config.CheckInterval,
		enabled:     true,
		logger:      logger,
		mailManager: mailManager,
		db:          db,
		config:      config,
	}
}

// GetName 获取任务名称
func (t *MailboxHealthMonitorTask) GetName() string {
	return t.name
}

// GetInterval 获取执行间隔
func (t *MailboxHealthMonitorTask) GetInterval() time.Duration {
	return t.interval
}

// IsEnabled 是否启用
func (t *MailboxHealthMonitorTask) IsEnabled() bool {
	return t.enabled
}

// SetEnabled 设置启用状态
func (t *MailboxHealthMonitorTask) SetEnabled(enabled bool) {
	t.enabled = enabled
}

// Execute 执行任务
func (t *MailboxHealthMonitorTask) Execute(ctx context.Context) error {
	if t.isRunning {
		t.logger.Warn("邮箱健康监控任务已在运行中，跳过本次执行")
		return nil
	}

	t.isRunning = true
	t.lastRun = time.Now()
	defer func() {
		t.isRunning = false
	}()

	t.logger.Info("开始执行邮箱健康监控任务")

	// 获取活跃账户
	accounts, err := t.getActiveAccounts(ctx)
	if err != nil {
		t.logger.Error("获取活跃账户失败", "error", err)
		return err
	}

	if len(accounts) == 0 {
		t.logger.Info("没有活跃账户需要监控")
		return nil
	}

	t.logger.Info("开始监控账户健康状态", "count", len(accounts))

	// 执行健康检查
	healthReport, err := t.performHealthCheck(ctx, accounts)
	if err != nil {
		t.logger.Error("健康检查失败", "error", err)
		return err
	}

	// 分析健康报告
	t.analyzeHealthReport(healthReport)

	// 更新账户状态
	t.updateAccountHealth(healthReport)

	// 生成警报（如果需要）
	if t.config.NotifyOnAlert {
		t.generateAlerts(healthReport)
	}

	t.logger.Info("邮箱健康监控任务完成",
		"total", len(accounts),
		"healthy", healthReport.HealthyCount,
		"unhealthy", healthReport.UnhealthyCount,
		"failed", healthReport.FailedCount)

	return nil
}

// HealthReport 健康报告
type HealthReport struct {
	TotalCount     int
	HealthyCount   int
	UnhealthyCount int
	FailedCount    int
	AccountHealth  map[string]*AccountHealth
	OverallHealth  float64
}

// AccountHealth 账户健康状态
type AccountHealth struct {
	Email         string
	IsHealthy     bool
	HealthScore   float64
	LastCheckTime time.Time
	ErrorMessage  string
	ResponseTime  time.Duration
	SessionValid  bool
	LoginSuccess  bool
}

// getActiveAccounts 获取活跃账户
func (t *MailboxHealthMonitorTask) getActiveAccounts(_ context.Context) ([]database.ExtendedAccount, error) {
	query := `
		SELECT id, email, password, login_status, last_login_time, is_disabled
		FROM accounts 
		WHERE is_disabled = 0 
		  AND login_status = 'success'
		  AND last_login_time > ?
		ORDER BY last_login_time DESC
		LIMIT ?
	`

	// 只检查最近7天内有登录记录的账户
	recentTime := time.Now().Add(-7 * 24 * time.Hour)

	rows, err := t.db.GetDB().Query(query, recentTime, t.config.BatchSize*2)
	if err != nil {
		return nil, fmt.Errorf("查询活跃账户失败: %w", err)
	}
	defer rows.Close()

	var accounts []database.ExtendedAccount
	for rows.Next() {
		var account database.ExtendedAccount
		var isDisabled bool
		err := rows.Scan(
			&account.ID, &account.Email, &account.Password, &account.LoginStatus,
			&account.LastLoginTime, &isDisabled,
		)
		if err != nil {
			t.logger.Error("扫描账户数据失败", "error", err)
			continue
		}
		accounts = append(accounts, account)
	}

	return accounts, nil
}

// performHealthCheck 执行健康检查
func (t *MailboxHealthMonitorTask) performHealthCheck(ctx context.Context, accounts []database.ExtendedAccount) (*HealthReport, error) {
	report := &HealthReport{
		TotalCount:    len(accounts),
		AccountHealth: make(map[string]*AccountHealth),
	}

	// 分批检查
	batchSize := t.config.BatchSize
	for i := 0; i < len(accounts); i += batchSize {
		end := i + batchSize
		if end > len(accounts) {
			end = len(accounts)
		}

		batch := accounts[i:end]
		t.checkBatch(ctx, batch, report)

		// 批次间暂停
		if end < len(accounts) {
			time.Sleep(time.Second * 2)
		}
	}

	// 计算整体健康度
	if report.TotalCount > 0 {
		report.OverallHealth = float64(report.HealthyCount) / float64(report.TotalCount)
	}

	return report, nil
}

// checkBatch 检查批次
func (t *MailboxHealthMonitorTask) checkBatch(ctx context.Context, accounts []database.ExtendedAccount, report *HealthReport) {
	// 转换为types.Account格式
	typeAccounts := make([]types.Account, len(accounts))
	for i, account := range accounts {
		typeAccounts[i] = types.Account{
			Username: account.Email,
			Password: account.Password, // 注意：这里需要解密
			Status:   types.AccountStatusActive,
		}
	}

	// 执行批量健康检查
	startTime := time.Now()
	healthResult, err := t.mailManager.BatchHealthCheck(ctx)
	checkDuration := time.Since(startTime)

	if err != nil {
		t.logger.Error("批量健康检查失败", "error", err)
		// 标记所有账户为检查失败
		for _, account := range accounts {
			report.AccountHealth[account.Email] = &AccountHealth{
				Email:         account.Email,
				IsHealthy:     false,
				HealthScore:   0.0,
				LastCheckTime: time.Now(),
				ErrorMessage:  err.Error(),
				ResponseTime:  checkDuration,
			}
			report.FailedCount++
		}
		return
	}

	// 处理健康检查结果
	for _, account := range accounts {
		health := &AccountHealth{
			Email:         account.Email,
			LastCheckTime: time.Now(),
			ResponseTime:  checkDuration / time.Duration(len(accounts)),
		}

		// 检查账户状态
		if accountStatus, exists := healthResult.AccountStatus[account.Email]; exists {
			health.LoginSuccess = (accountStatus == types.AccountStatusActive)
			health.HealthScore = t.calculateHealthScore(accountStatus, health.ResponseTime)
			health.IsHealthy = health.HealthScore >= t.config.HealthThreshold
		} else {
			health.IsHealthy = false
			health.HealthScore = 0.0
			health.ErrorMessage = "账户状态未知"
		}

		// 检查会话状态
		if sessionStatus, exists := healthResult.SessionStatus[account.Email]; exists {
			health.SessionValid = (sessionStatus == types.SessionStatusActive)
		}

		report.AccountHealth[account.Email] = health

		if health.IsHealthy {
			report.HealthyCount++
		} else {
			report.UnhealthyCount++
		}
	}
}

// calculateHealthScore 计算健康分数
func (t *MailboxHealthMonitorTask) calculateHealthScore(status types.AccountStatus, responseTime time.Duration) float64 {
	baseScore := 0.0

	// 基于账户状态的分数
	switch status {
	case types.AccountStatusActive:
		baseScore = 1.0
	case types.AccountStatusInactive:
		baseScore = 0.5
	case types.AccountStatusLocked:
		baseScore = 0.0
	case types.AccountStatusExpired:
		baseScore = 0.0
	default:
		baseScore = 0.3
	}

	// 基于响应时间的调整
	if responseTime > time.Second*30 {
		baseScore *= 0.8 // 响应慢扣分
	} else if responseTime < time.Second*5 {
		baseScore *= 1.1 // 响应快加分
		if baseScore > 1.0 {
			baseScore = 1.0
		}
	}

	return baseScore
}

// analyzeHealthReport 分析健康报告
func (t *MailboxHealthMonitorTask) analyzeHealthReport(report *HealthReport) {
	t.logger.Info("健康检查报告分析",
		"overall_health", fmt.Sprintf("%.2f%%", report.OverallHealth*100),
		"healthy_count", report.HealthyCount,
		"unhealthy_count", report.UnhealthyCount,
		"failed_count", report.FailedCount)

	// 检查是否需要警报
	if report.OverallHealth < t.config.AlertThreshold {
		t.logger.Warn("邮箱整体健康度低于警报阈值",
			"current", fmt.Sprintf("%.2f%%", report.OverallHealth*100),
			"threshold", fmt.Sprintf("%.2f%%", t.config.AlertThreshold*100))
	}
}

// updateAccountHealth 更新账户健康状态
func (t *MailboxHealthMonitorTask) updateAccountHealth(report *HealthReport) {
	for email, health := range report.AccountHealth {
		// 更新账户验证状态
		status := "verified"
		if !health.IsHealthy {
			status = "failed"
		}

		query := `
			UPDATE accounts SET 
				verification_status = ?,
				last_verification_time = ?,
				verification_error = ?
			WHERE email = ?
		`

		_, err := t.db.GetDB().Exec(query, status, health.LastCheckTime, health.ErrorMessage, email)
		if err != nil {
			t.logger.Error("更新账户健康状态失败", "email", email, "error", err)
		}

		// 如果启用自动禁用且健康分数过低
		if t.config.AutoDisable && health.HealthScore < t.config.DisableThreshold {
			t.disableUnhealthyAccount(email, health)
		}
	}
}

// disableUnhealthyAccount 禁用不健康的账户
func (t *MailboxHealthMonitorTask) disableUnhealthyAccount(email string, health *AccountHealth) {
	query := "UPDATE accounts SET is_disabled = 1 WHERE email = ?"
	_, err := t.db.GetDB().Exec(query, email)
	if err != nil {
		t.logger.Error("禁用不健康账户失败", "email", email, "error", err)
	} else {
		t.logger.Warn("自动禁用不健康账户",
			"email", email,
			"health_score", fmt.Sprintf("%.2f", health.HealthScore),
			"threshold", fmt.Sprintf("%.2f", t.config.DisableThreshold))
	}
}

// generateAlerts 生成警报
func (t *MailboxHealthMonitorTask) generateAlerts(report *HealthReport) {
	if report.OverallHealth < t.config.AlertThreshold {
		// TODO: 实现警报通知机制（邮件、webhook等）
		t.logger.Warn("生成健康警报",
			"overall_health", fmt.Sprintf("%.2f%%", report.OverallHealth*100),
			"unhealthy_accounts", report.UnhealthyCount)
	}
}
