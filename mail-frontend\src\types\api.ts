// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  timestamp?: string
}

// 分页响应类型
export interface PaginatedResponse<T> {
  Items: T[] // 后端返回的字段名是大写
  Total: number // 后端返回的字段名是大写
  Page: number
  PageSize?: number // 可选，兼容后端返回的字段
  TotalPages?: number // 可选，前端可以计算
  // 兼容小写字段名
  items?: T[]
  total?: number
  page?: number
  pageSize?: number
  size?: number
  totalPages?: number
}

// 登录相关类型
export interface LoginRequest {
  username: string
  password: string
  deviceFingerprint?: string
}

export interface LoginResponse {
  token: string
  refresh_token: string
  expires_in: number
  user: UserInfo
}

export interface UserInfo {
  id: string
  username: string
  role: string
  lastLoginTime?: string
}

// 激活码相关类型
export interface ActivationCode {
  id: string | number // 兼容后端返回的数字ID
  code: string
  status: 'active' | 'used' | 'expired' | 'unused' // 添加 unused 状态
  deviceFingerprint?: string
  device_fingerprint?: string // 兼容后端蛇形命名
  createdAt: string
  created_at?: string // 兼容后端蛇形命名
  usedAt?: string
  used_at?: string // 兼容后端蛇形命名
  expiresAt: string
  expires_at?: string // 兼容后端蛇形命名
  macAddress?: string
  mac_address?: string // 兼容后端蛇形命名
  description?: string
  batchId?: string
  batch_id?: string // 兼容后端蛇形命名
}

export interface CreateActivationCodeRequest {
  count: number
  expiry_days: number
  prefix?: string
  description?: string
}

// 邮箱相关类型
export interface MailboxInfo {
  id: string
  address: string
  aliasName: string
  status: 'active' | 'expired' | 'released'
  createdAt: string
  expiresAt: string
  mailCount: number
}

export interface MailItem {
  id: string
  mailboxId: string
  subject: string
  from: string
  to: string
  date: string
  isRead: boolean
  isNew: boolean
  hasAttachments: boolean
}

export interface MailContent {
  id: string
  subject: string
  from: string
  to: string
  cc?: string
  bcc?: string
  date: string
  textBody: string
  htmlBody: string
  attachments: MailAttachment[]
}

export interface MailAttachment {
  id: string
  name: string
  size: number
  mimeType: string
  downloadUrl: string
}

// 系统监控相关类型
export interface SystemStats {
  totalActivationCodes: number
  usedActivationCodes: number
  activeMailboxes: number
  totalMails: number
  systemUptime: string
  memoryUsage: number
  cpuUsage: number
  // 后端返回的实际数据结构
  accounts?: {
    total: number
    active: number
    inactive: number
  }
  mailboxes?: {
    total_allocated: number
    currently_active: number
    today_allocated: number
    today_released: number
  }
  activation_codes?: {
    total_generated: number
    used: number
    unused: number
    expired: number
  }
  system?: {
    uptime: string
    memory_usage: string
    app_memory_usage: string
    memory_total: string
    memory_used: string
    memory_percent: number
    cpu_usage: number
    cpu_cores: number
    goroutines: number
    disk_usage: string
    disk_total: string
    disk_used: string
    disk_percent: number
    database_size: string
    workdir_size: string
  }
  database?: {
    size: string
    table_counts: Record<string, number>
    connection_ok: boolean
    avg_query_time_ms: number
  }
  performance?: {
    avg_response_time_ms: number
    total_requests: number
    error_rate_percent: number
    active_sessions: number
  }
}

export interface TaskInfo {
  name: string
  last_run: string
  next_run: string
  run_count: number
  error_count: number
  last_error?: string
  is_running: boolean
  enabled: boolean
}

// 配置相关类型
export interface SystemConfig {
  mailboxExpirationMinutes: number
  maxMailboxesPerCode: number
  cleanupIntervalMinutes: number
  enableAutoCleanup: boolean
  maxMailsPerMailbox: number
  enableEmailNotifications: boolean
}

export interface ConfigUpdateRequest {
  key: string
  value: any
  description?: string
}
