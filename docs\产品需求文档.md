# Go-Mail 临时邮箱服务平台 - 产品需求文档 (PRD)

## 📋 项目概述

### 🎯 产品定位
基于 Mail.com 邮件别名功能的临时邮箱服务平台，为用户提供隐私保护和开发测试场景下的临时邮箱解决方案。

### 👥 目标用户群体
- **主要用户**：个人隐私保护用户（注册网站、避免垃圾邮件）
- **次要用户**：开发者和测试人员（软件测试、API调试）

### 💰 商业模式
- **免费服务**：随机生成的临时邮箱（有使用限制）
- **付费服务**：自定义临时邮箱（后续版本）
- **技术核心**：利用 Mail.com 的邮件别名功能

## 🏗️ 系统架构

### 三层架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (go-mail-client)                    │
│              Wails 桌面应用 - 暂缓开发                          │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   管理后台 (mail-frontend)                    │
│           Vue3 + TypeScript + Naive UI                     │
│              优先开发 - 产品驱动策略                           │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                  后端服务 (go-mail-backend)                   │
│              Go + Gin + SQLite + 现有架构                    │
│                扩展现有功能 - API服务层                        │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心功能需求

### 📱 客户端功能（暂缓开发）
1. **邮箱申请流程**
   - 激活码验证和设备绑定
   - 免费模式：随机临时邮箱申请
   - 邮箱分配机制：确保独占使用

2. **邮件监控机制**
   - 3秒轮询检查新邮件
   - 实时显示邮件列表和详情
   - 桌面通知功能

3. **邮箱生命周期管理**
   - 占用机制：获取邮件后独占邮箱
   - 自动释放：3分钟无新邮件自动释放
   - 手动释放：用户主动释放

### 🖥️ 管理后台功能（优先开发）
1. **认证系统**
   - JWT认证的管理员登录
   - 用户权限管理
   - 会话管理

2. **激活码管理**
   - 批量生成激活码
   - 激活码状态管理
   - 使用情况统计

3. **系统监控**
   - Mail.com账户状态监控
   - 临时邮箱使用统计
   - 系统性能监控
   - 错误日志查看

4. **业务管理**
   - 用户使用情况分析
   - 邮箱分配记录
   - 系统配置管理

### 🔧 后端服务功能（扩展现有架构）
1. **API服务层**
   - 激活码生成和验证API
   - 临时邮箱分配API
   - 邮件查询API
   - 邮箱释放API

2. **安全机制**
   - AES对称加密 + HMAC签名
   - 设备绑定验证
   - 接口访问控制
   - 数据传输完整性验证

3. **定时任务系统**
   - 自动释放超时邮箱别名
   - Mail.com账户健康检查
   - 系统清理任务

## 🔐 安全设计

### 通信加密
- **方案**：AES对称加密 + HMAC签名
- **优势**：性能优秀，适合高频访问
- **实现**：中间件形式集成到API层

### 设备绑定
- **标识方案**：系统UUID + 用户名 + 计算机名
- **兼容性**：支持Windows、macOS、Linux
- **安全性**：硬件信息组合，难以伪造

### 激活码系统
- **格式**：16位随机字符串（如：ABCD-1234-EFGH-5678）
- **类型**：按时效分类（体验版、月卡、季卡等）
- **绑定**：一码一设备，防止滥用

## 📊 免费服务限制策略

### 综合限制方案
1. **数量限制**：每日/每小时申请次数限制
2. **功能限制**：仅随机邮箱，不能自定义
3. **时间限制**：免费邮箱最长使用时间限制
4. **综合控制**：多种限制组合使用

## 🎮 用户使用场景

### 短期使用场景（主要）
- 获取邮箱 → 注册/测试 → 接收验证邮件 → 立即释放
- 使用时长：3-5分钟
- 用户群体：隐私保护用户、开发测试人员

### 混合模式场景
- 根据不同需求灵活选择使用时长
- 支持短期和中期使用需求

### 批量使用场景（高级用户）
- 同时管理多个临时邮箱
- 适合专业测试和开发场景

## 🚀 开发路线图

### 第一阶段：管理后台框架（2周）
- Vue3 + TypeScript + Naive UI 项目搭建
- JWT认证系统实现
- 基础页面框架搭建
- 主要功能模块界面

### 第二阶段：后端API服务（2周）
- Gin框架集成到现有项目
- 激活码管理API开发
- 临时邮箱分配API开发
- AES加密中间件实现

### 第三阶段：功能完善（2周）
- 定时任务系统开发
- 系统监控功能完善
- 前后端联调测试
- 部署和优化

### 第四阶段：客户端开发（后续）
- Wails桌面应用开发
- 客户端-服务端通信
- 用户体验优化

## 📈 成功指标

### 技术指标
- API响应时间 < 200ms
- 系统可用性 > 99%
- 邮箱分配成功率 > 95%
- 并发用户支持 > 100

### 业务指标
- 用户注册转化率
- 激活码使用率
- 用户留存率
- 系统稳定性

## 🔧 技术栈总结

### 后端技术栈
- **语言**：Go 1.21
- **框架**：Gin（新增）
- **数据库**：SQLite
- **现有架构**：Manager/Client/Pool/Database 分层设计

### 前端技术栈
- **框架**：Vue3
- **语言**：TypeScript
- **UI库**：Naive UI
- **构建工具**：Vite
- **状态管理**：Pinia

### 部署方案
- **后端**：编译为可执行文件，跨平台部署
- **前端**：静态文件部署
- **数据库**：SQLite文件存储
- **配置**：支持配置文件和环境变量

## 📝 下一步行动

1. **技术方案确认**：审查本PRD文档
2. **架构设计**：详细的技术实现方案
3. **开发启动**：按照产品驱动策略开始开发
4. **迭代优化**：根据用户反馈持续改进

---

**文档版本**：v1.0  
**创建时间**：2025-01-20  
**负责人**：产品经理 & 技术团队  
**审核状态**：待确认
