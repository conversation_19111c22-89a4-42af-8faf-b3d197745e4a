package client

import (
	"compress/gzip"
	"context"
	"fmt"
	"go-mail/internal/errors"
	"go-mail/internal/types"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"golang.org/x/net/html"
)

// GetAliasEmails 获取别名邮箱列表
func (c *MailClient) GetAliasEmails(ctx context.Context, session *types.Session) (*types.AliasEmailList, error) {
	if session == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "会话信息为空", nil)
	}

	if session.Client == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "HTTP客户端为空", nil)
	}

	// 构建请求URL
	reqURL := fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s",
		session.JSessionID)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建获取别名邮箱请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 发送请求
	resp, err := session.Client.Do(req)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "获取别名邮箱请求失败", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("获取别名邮箱返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 读取响应体
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "解压gzip响应失败", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "读取别名邮箱响应失败", err)
	}

	// 解析HTML获取别名邮箱列表
	aliasEmailList, err := c.parseAliasEmailList(string(body))
	if err != nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "解析别名邮箱列表失败", err)
	}

	return aliasEmailList, nil
}

// CreateAliasEmail 创建别名邮箱
func (c *MailClient) CreateAliasEmail(ctx context.Context, session *types.Session, request *types.CreateAliasRequest) (*types.CreateAliasResponse, error) {
	if session == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "会话信息为空", nil)
	}

	if session.Client == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "HTTP客户端为空", nil)
	}

	if request == nil {
		return nil, errors.NewValidationError(errors.ErrCodeValidationRequired, "创建请求不能为空", nil)
	}

	if request.LocalPart == "" {
		return nil, errors.NewValidationError(errors.ErrCodeValidationRequired, "邮箱前缀不能为空", nil)
	}

	if request.DomainSelection == "" {
		return nil, errors.NewValidationError(errors.ErrCodeValidationRequired, "域名选项不能为空", nil)
	}

	// 首先获取页面以提取必要的会话信息
	pageInfo, err := c.getAliasPageInfo(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("获取页面信息失败: %w", err)
	}

	// 检查是否有iac_token，如果没有则尝试获取
	if pageInfo.IacToken == "" {
		fmt.Printf("警告：未找到iac_token，这可能导致创建失败\n")
	} else {
		fmt.Printf("调试：找到iac_token: %s\n", pageInfo.IacToken)
	}

	// 构建请求URL
	reqURL := fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s?2-1.0-splitPanel-splitpanelContainer-splitpanelRight-splitPanel_body-childContainer-internalAliasChapter-internalAliasChapter_body-internalAliasPanel-form-fieldSet-fieldSet_body-grid-button-button",
		session.JSessionID)

	// 构建表单数据 - 按照成功请求的顺序：localPart -> domainSelection -> button
	formData := url.Values{}
	formData.Add("fieldSet:fieldSet_body:grid:addressSelection:localPart", request.LocalPart)
	formData.Add("fieldSet:fieldSet_body:grid:addressSelection:domainSelection", request.DomainSelection)
	formData.Add("fieldSet:fieldSet_body:grid:button:button", "1")

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", reqURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建别名邮箱请求失败", err)
	}

	// 设置请求头 - 按照成功请求的格式设置
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	req.Header.Set("sec-ch-ua-platform", "\"Windows\"")
	req.Header.Set("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"")
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("Wicket-Ajax", "true")
	req.Header.Set("Wicket-Ajax-BaseURL", "settings/allEmailAddresses")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("User-Agent", c.config.UserAgent)
	req.Header.Set("Accept", "application/xml, text/xml, */*; q=0.01")
	req.Header.Set("Wicket-FocusedElementId", pageInfo.FocusedElementId) // 动态获取的关键字段
	req.Header.Set("Origin", "https://3c-lxa.mail.com")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-Mode", "cors")  // 修正为cors而不是navigate
	req.Header.Set("Sec-Fetch-Dest", "empty") // 修正为empty而不是document
	req.Header.Set("Referer", fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s", session.JSessionID))
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd") // 完整的编码支持
	req.Header.Set("Accept-Language", "en,zh-CN;q=0.9,zh;q=0.8") // 匹配成功请求的语言设置

	// 调试：输出请求信息
	c.debugCreateAliasRequest(req, formData)

	// 发送请求
	resp, err := session.Client.Do(req)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建别名邮箱请求失败", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("创建别名邮箱返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 读取响应体
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "解压gzip响应失败", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "读取创建别名邮箱响应失败", err)
	}

	// 解析响应
	response := &types.CreateAliasResponse{}

	// 检查是否创建成功
	htmlContent := string(body)

	// 检查成功消息
	successRegex := regexp.MustCompile(`Your e-mail address \(([^)]+)\) was successfully created`)
	if matches := successRegex.FindStringSubmatch(htmlContent); len(matches) > 1 {
		response.Success = true
		response.Message = fmt.Sprintf("邮箱别名 %s 创建成功", matches[1])
		response.Email = matches[1]
		return response, nil
	}

	// 检查失败消息
	failRegex := regexp.MustCompile(`This e-mail-address \(([^)]+)\) is not available!`)
	if matches := failRegex.FindStringSubmatch(htmlContent); len(matches) > 1 {
		response.Success = false
		response.Message = fmt.Sprintf("邮箱别名 %s 已存在，无法创建", matches[1])
		response.Email = matches[1]
		return response, nil
	}

	// 其他错误情况
	response.Success = false
	response.Message = "创建别名邮箱失败，未知原因"
	return response, nil
}

// DeleteAliasEmail 删除别名邮箱 - 实现完整的两步删除流程
func (c *MailClient) DeleteAliasEmail(ctx context.Context, session *types.Session, request *types.DeleteAliasRequest) (*types.DeleteAliasResponse, error) {
	if session == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "会话信息为空", nil)
	}

	if session.Client == nil {
		return nil, errors.NewSessionError(errors.ErrCodeSessionInvalid, "HTTP客户端为空", nil)
	}

	if request == nil || request.RowID == "" {
		return nil, errors.NewValidationError(errors.ErrCodeValidationRequired, "别名邮箱ID不能为空", nil)
	}

	// 第一步：发送初始删除请求，获取确认对话框
	fmt.Printf("调试：开始删除别名邮箱，第一步 - 发送初始删除请求\n")
	confirmInfo, err := c.sendInitialDeleteRequest(ctx, session, request.RowID)
	if err != nil {
		return nil, fmt.Errorf("第一步删除请求失败: %w", err)
	}

	if confirmInfo.ConfirmURL == "" || confirmInfo.OkButtonId == "" {
		return &types.DeleteAliasResponse{
			Success: false,
			Message: "无法获取确认信息，删除失败",
		}, nil
	}

	// 第二步：发送确认请求，完成删除
	fmt.Printf("调试：第二步 - 发送确认请求，URL: %s, ButtonId: %s\n", confirmInfo.ConfirmURL, confirmInfo.OkButtonId)
	finalResponse, err := c.sendDeleteConfirmation(ctx, session, confirmInfo)
	if err != nil {
		return nil, fmt.Errorf("第二步确认请求失败: %w", err)
	}

	return finalResponse, nil
}

// makeTimestamp 生成当前时间戳（毫秒）
func makeTimestamp() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

// parseAliasEmailList 解析HTML响应中的别名邮箱列表和域名选项
func (c *MailClient) parseAliasEmailList(htmlContent string) (*types.AliasEmailList, error) {
	result := &types.AliasEmailList{
		Aliases:       make([]types.AliasEmail, 0),
		DomainOptions: make([]types.DomainOption, 0),
	}

	// 解析HTML文档
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	// 解析别名邮箱列表
	aliases, err := c.parseAliasEmails(doc)
	if err != nil {
		return nil, fmt.Errorf("解析别名邮箱失败: %w", err)
	}
	result.Aliases = aliases

	// 解析域名选项
	domainOptions, err := c.parseDomainOptions(doc)
	if err != nil {
		return nil, fmt.Errorf("解析域名选项失败: %w", err)
	}
	result.DomainOptions = domainOptions

	return result, nil
}

// parseAliasEmails 解析HTML中的别名邮箱列表
func (c *MailClient) parseAliasEmails(doc *html.Node) ([]types.AliasEmail, error) {
	aliases := make([]types.AliasEmail, 0)

	// 查找table_body div
	tableBody := c.findNodeByClass(doc, "table_body")
	if tableBody == nil {
		return aliases, nil // 返回空列表，可能没有别名邮箱
	}

	// 遍历table_body-row元素
	for row := tableBody.FirstChild; row != nil; row = row.NextSibling {
		if row.Type == html.ElementNode && row.Data == "div" {
			// 检查是否是table_body-row
			isRow := false
			rowID := ""
			isDefault := false

			for _, attr := range row.Attr {
				if attr.Key == "class" && strings.Contains(attr.Val, "table_body-row") {
					isRow = true
				}
				if attr.Key == "data-row-id" {
					rowID = attr.Val
				}
				if attr.Key == "class" && strings.Contains(attr.Val, "is-first") {
					isDefault = true // 第一行通常是默认邮箱
				}
			}

			if isRow && rowID != "" {
				// 提取邮箱地址
				emailField := c.findNodeByClass(row, "table_field")
				if emailField != nil {
					emailText := c.getTextContent(emailField)
					emailText = strings.TrimSpace(emailText)

					// 移除"(Default sender address)"文本
					emailText = strings.ReplaceAll(emailText, "(Default sender address)", "")
					emailText = strings.TrimSpace(emailText)

					if emailText != "" {
						// 分割邮箱地址为本地部分和域名部分
						parts := strings.Split(emailText, "@")
						localPart := emailText
						domain := ""

						if len(parts) == 2 {
							localPart = parts[0]
							domain = parts[1]
						}

						alias := types.AliasEmail{
							ID:        rowID,
							Email:     emailText,
							LocalPart: localPart,
							Domain:    domain,
							IsDefault: isDefault,
						}
						aliases = append(aliases, alias)
					}
				}
			}
		}
	}

	return aliases, nil
}

// parseDomainOptions 解析HTML中的域名选项
func (c *MailClient) parseDomainOptions(doc *html.Node) ([]types.DomainOption, error) {
	domainOptions := make([]types.DomainOption, 0)

	// 查找域名选择下拉框
	selectNode := c.findSelectNode(doc)
	if selectNode == nil {
		return domainOptions, nil // 返回空列表，可能找不到域名选择框
	}

	// 当前分类
	currentCategory := ""

	// 遍历select的子节点
	for child := selectNode.FirstChild; child != nil; child = child.NextSibling {
		if child.Type == html.ElementNode {
			if child.Data == "optgroup" {
				// 获取分类标签
				for _, attr := range child.Attr {
					if attr.Key == "label" {
						currentCategory = attr.Val
						break
					}
				}

				// 遍历optgroup的子节点
				for option := child.FirstChild; option != nil; option = option.NextSibling {
					if option.Type == html.ElementNode && option.Data == "option" {
						optionValue := ""
						for _, attr := range option.Attr {
							if attr.Key == "value" {
								optionValue = attr.Val
								break
							}
						}

						if optionValue != "" {
							domainText := c.getTextContent(option)
							if domainText != "" {
								domainOption := types.DomainOption{
									Value:    optionValue,
									Domain:   domainText,
									Category: currentCategory,
								}
								domainOptions = append(domainOptions, domainOption)
							}
						}
					}
				}
			} else if child.Data == "option" {
				// 直接的option元素（没有分类）
				optionValue := ""
				for _, attr := range child.Attr {
					if attr.Key == "value" {
						optionValue = attr.Val
						break
					}
				}

				if optionValue != "" {
					domainText := c.getTextContent(child)
					if domainText != "" {
						domainOption := types.DomainOption{
							Value:    optionValue,
							Domain:   domainText,
							Category: "General", // 默认分类
						}
						domainOptions = append(domainOptions, domainOption)
					}
				}
			}
		}
	}

	return domainOptions, nil
}

// findNodeByClass 根据class属性查找HTML节点
func (c *MailClient) findNodeByClass(n *html.Node, className string) *html.Node {
	if n.Type == html.ElementNode {
		for _, attr := range n.Attr {
			if attr.Key == "class" && strings.Contains(attr.Val, className) {
				return n
			}
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		if result := c.findNodeByClass(child, className); result != nil {
			return result
		}
	}

	return nil
}

// findSelectNode 查找域名选择的select节点
func (c *MailClient) findSelectNode(n *html.Node) *html.Node {
	if n.Type == html.ElementNode && n.Data == "select" {
		// 检查name属性是否包含domainSelection
		for _, attr := range n.Attr {
			if attr.Key == "name" && strings.Contains(attr.Val, "domainSelection") {
				return n
			}
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		if result := c.findSelectNode(child); result != nil {
			return result
		}
	}

	return nil
}

// debugCreateAliasRequest 调试输出创建别名邮箱的请求信息
func (c *MailClient) debugCreateAliasRequest(req *http.Request, formData url.Values) {
	fmt.Printf("\n=== 调试：创建别名邮箱请求信息 ===\n")
	fmt.Printf("请求URL: %s\n", req.URL.String())
	fmt.Printf("请求方法: %s\n", req.Method)

	// 输出请求头
	fmt.Printf("\n请求头:\n")
	for name, values := range req.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", name, value)
		}
	}

	// 输出表单数据
	fmt.Printf("\n表单数据:\n")
	fmt.Printf("  原始编码: %s\n", formData.Encode())
	fmt.Printf("  解析后的参数:\n")
	for key, values := range formData {
		for _, value := range values {
			fmt.Printf("    %s = %s\n", key, value)
		}
	}

	// 输出Cookie信息
	fmt.Printf("\nCookie信息:\n")
	if cookieHeader := req.Header.Get("Cookie"); cookieHeader != "" {
		fmt.Printf("  Cookie头: %s\n", cookieHeader)
		// 检查是否包含iac_token
		if strings.Contains(cookieHeader, "iac_token") {
			fmt.Printf("  ✓ 包含iac_token\n")
		} else {
			fmt.Printf("  ✗ 缺少iac_token\n")
		}
	} else {
		fmt.Printf("  ✗ 没有Cookie头\n")
	}

	fmt.Printf("=== 创建别名邮箱请求调试结束 ===\n\n")
}

// getAliasPageInfo 获取别名邮箱页面的关键信息
func (c *MailClient) getAliasPageInfo(ctx context.Context, session *types.Session) (*types.AliasPageInfo, error) {
	// 构建请求URL
	reqURL := fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s",
		session.JSessionID)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建页面请求失败: %w", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 发送请求
	resp, err := session.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("获取页面请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("获取页面返回异常状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("解压gzip响应失败: %w", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("读取页面响应失败: %w", err)
	}

	// 解析页面获取关键信息
	pageInfo, err := c.parseAliasPageInfo(string(body))
	if err != nil {
		return nil, fmt.Errorf("解析页面信息失败: %w", err)
	}

	// 从Cookie中获取iac_token
	if u, err := url.Parse(reqURL); err == nil {
		cookies := session.Client.Jar.Cookies(u)
		for _, cookie := range cookies {
			if cookie.Name == "iac_token" {
				pageInfo.IacToken = cookie.Value
				break
			}
		}
	}

	return pageInfo, nil
}

// parseAliasPageInfo 解析页面HTML获取关键信息
func (c *MailClient) parseAliasPageInfo(htmlContent string) (*types.AliasPageInfo, error) {
	pageInfo := &types.AliasPageInfo{}

	// 解析HTML文档
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	// 查找Wicket-FocusedElementId
	focusedElementId := c.findFocusedElementId(doc)
	if focusedElementId == "" {
		// 如果找不到，使用默认值
		focusedElementId = "id30" // 根据成功请求的值
	}
	pageInfo.FocusedElementId = focusedElementId

	return pageInfo, nil
}

// findFocusedElementId 查找页面中的焦点元素ID
func (c *MailClient) findFocusedElementId(n *html.Node) string {
	// 查找button元素，通常是提交按钮
	if n.Type == html.ElementNode && n.Data == "input" {
		isButton := false
		elementId := ""

		for _, attr := range n.Attr {
			if attr.Key == "type" && attr.Val == "submit" {
				isButton = true
			}
			if attr.Key == "id" {
				elementId = attr.Val
			}
		}

		if isButton && elementId != "" {
			return elementId
		}
	}

	// 递归查找子节点
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		if result := c.findFocusedElementId(child); result != "" {
			return result
		}
	}

	return ""
}

// sendInitialDeleteRequest 发送初始删除请求，获取确认对话框信息
func (c *MailClient) sendInitialDeleteRequest(ctx context.Context, session *types.Session, rowID string) (*types.DeleteConfirmInfo, error) {
	// 构建第一步删除请求URL
	reqURL := fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s?2-1.0-splitPanel-splitpanelContainer-splitpanelRight-splitPanel_body-childContainer-addressesListChapter-addressesListChapter_body-addresses-hoverTemplate-hoverIconPanel-hoverIcons-2-hoverIcon&rowId=%s&_=%d",
		session.JSessionID, rowID, makeTimestamp())

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建初始删除请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("sec-ch-ua-platform", "\"Windows\"")
	req.Header.Set("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"")
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("Wicket-Ajax", "true")
	req.Header.Set("Wicket-Ajax-BaseURL", "settings/allEmailAddresses")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("User-Agent", c.config.UserAgent)
	req.Header.Set("Accept", "application/xml, text/xml, */*; q=0.01")
	req.Header.Set("Wicket-FocusedElementId", "id2f") // 根据原始请求设置
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Referer", fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s", session.JSessionID))
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Accept-Language", "en,zh-CN;q=0.9,zh;q=0.8")

	// 发送请求
	resp, err := session.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送初始删除请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("初始删除请求返回异常状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("解压gzip响应失败: %w", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("读取初始删除响应失败: %w", err)
	}

	// 解析XML响应获取确认信息
	confirmInfo, err := c.parseDeleteConfirmInfo(string(body), session.JSessionID)
	if err != nil {
		return nil, fmt.Errorf("解析删除确认信息失败: %w", err)
	}

	return confirmInfo, nil
}

// sendDeleteConfirmation 发送删除确认请求，完成删除操作
func (c *MailClient) sendDeleteConfirmation(ctx context.Context, session *types.Session, confirmInfo *types.DeleteConfirmInfo) (*types.DeleteAliasResponse, error) {
	// 构建确认请求URL
	reqURL := fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s%s&_=%d",
		session.JSessionID, confirmInfo.ConfirmURL, makeTimestamp())

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建确认删除请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("sec-ch-ua-platform", "\"Windows\"")
	req.Header.Set("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"")
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("Wicket-Ajax", "true")
	req.Header.Set("Wicket-Ajax-BaseURL", "settings/allEmailAddresses")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("User-Agent", c.config.UserAgent)
	req.Header.Set("Accept", "application/xml, text/xml, */*; q=0.01")
	req.Header.Set("Wicket-FocusedElementId", confirmInfo.OkButtonId) // 使用动态获取的按钮ID
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Referer", fmt.Sprintf("https://3c-lxa.mail.com/mail/client/settings/allEmailAddresses;jsessionid=%s", session.JSessionID))
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Accept-Language", "en,zh-CN;q=0.9,zh;q=0.8")

	// 发送请求
	resp, err := session.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送确认删除请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("确认删除请求返回异常状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("解压gzip响应失败: %w", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("读取确认删除响应失败: %w", err)
	}

	// 解析最终响应
	response := &types.DeleteAliasResponse{}
	xmlContent := string(body)

	// 检查是否删除成功
	if strings.Contains(xmlContent, "Entry deleted successfully") {
		response.Success = true
		response.Message = "别名邮箱删除成功"
	} else if strings.Contains(xmlContent, "error") || strings.Contains(xmlContent, "failed") {
		response.Success = false
		response.Message = "别名邮箱删除失败"
	} else {
		response.Success = false
		response.Message = "删除结果未知，请检查邮箱列表"
	}

	fmt.Printf("调试：删除确认响应内容: %s\n", xmlContent[:min(500, len(xmlContent))])

	return response, nil
}

// parseDeleteConfirmInfo 解析删除确认对话框的XML响应
func (c *MailClient) parseDeleteConfirmInfo(xmlContent string, _ string) (*types.DeleteConfirmInfo, error) {
	confirmInfo := &types.DeleteConfirmInfo{}

	// 解析XML获取OK按钮的ID
	// 查找类似这样的内容：<button name="topLevelContainer:dialog:root-container:container:menu:buttonContainer:primary" id="id5d"
	okButtonRegex := regexp.MustCompile(`<button[^>]*name="[^"]*:primary"[^>]*id="([^"]+)"`)
	if matches := okButtonRegex.FindStringSubmatch(xmlContent); len(matches) > 1 {
		confirmInfo.OkButtonId = matches[1]
	}

	// 如果没找到，尝试另一种模式
	if confirmInfo.OkButtonId == "" {
		okButtonRegex2 := regexp.MustCompile(`id="([^"]+)"[^>]*data-webdriver="primary"`)
		if matches := okButtonRegex2.FindStringSubmatch(xmlContent); len(matches) > 1 {
			confirmInfo.OkButtonId = matches[1]
		}
	}

	// 从evaluate部分提取确认URL
	// 查找类似这样的内容：{"u":"./allEmailAddresses;jsessionid=...?2-1.0-topLevelContainer-dialog-root~container-container-menu-buttonContainer-primary"
	urlRegex := regexp.MustCompile(`"u":"\.(/[^"]*topLevelContainer-dialog-root~container-container-menu-buttonContainer-primary[^"]*)"`)
	if matches := urlRegex.FindStringSubmatch(xmlContent); len(matches) > 1 {
		confirmInfo.ConfirmURL = matches[1]
	}

	// 如果没找到完整URL，尝试构建URL
	if confirmInfo.ConfirmURL == "" && confirmInfo.OkButtonId != "" {
		confirmInfo.ConfirmURL = "?2-1.0-topLevelContainer-dialog-root~container-container-menu-buttonContainer-primary"
	}

	// 提取对话框文本
	textRegex := regexp.MustCompile(`<p[^>]*id="[^"]*">([^<]*)</p>`)
	if matches := textRegex.FindStringSubmatch(xmlContent); len(matches) > 1 {
		confirmInfo.DialogText = strings.TrimSpace(matches[1])
	}

	// 验证是否获取到必要信息
	if confirmInfo.OkButtonId == "" {
		return nil, fmt.Errorf("无法从XML响应中提取OK按钮ID")
	}

	if confirmInfo.ConfirmURL == "" {
		return nil, fmt.Errorf("无法从XML响应中提取确认URL")
	}

	fmt.Printf("调试：解析到的确认信息 - ButtonId: %s, URL: %s, Text: %s\n",
		confirmInfo.OkButtonId, confirmInfo.ConfirmURL, confirmInfo.DialogText)

	return confirmInfo, nil
}
