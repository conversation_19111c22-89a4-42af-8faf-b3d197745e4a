package pool

import (
	"fmt"
	"go-mail/internal/errors"
	"go-mail/internal/types"
	"sync"
	"time"
)

// ProxyManager 代理管理器实现
type ProxyManager struct {
	proxies     map[string]*types.ProxyConfig
	mutex       sync.RWMutex
	currentIdx  int
	healthCheck map[string]*ProxyHealth
}

// ProxyHealth 代理健康状态
type ProxyHealth struct {
	LastCheck    time.Time
	ResponseTime time.Duration
	ErrorCount   int
	Available    bool
}

// NewProxyManager 创建新的代理管理器
func NewProxyManager() *ProxyManager {
	return &ProxyManager{
		proxies:     make(map[string]*types.ProxyConfig),
		healthCheck: make(map[string]*ProxyHealth),
	}
}

// Add 添加代理
func (m *ProxyManager) Add(proxy types.ProxyConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 验证代理配置
	if err := m.validateProxy(proxy); err != nil {
		return err
	}

	// 生成代理ID（如果未提供）
	if proxy.ID == "" {
		proxy.ID = fmt.Sprintf("%s:%d", proxy.Host, proxy.Port)
	}

	// 检查代理是否已存在
	if _, exists := m.proxies[proxy.ID]; exists {
		return errors.NewValidationError(errors.ErrCodeValidationConflict, 
			fmt.Sprintf("代理 %s 已存在", proxy.ID), nil)
	}

	// 添加代理
	m.proxies[proxy.ID] = &proxy
	m.healthCheck[proxy.ID] = &ProxyHealth{
		LastCheck: time.Now(),
		Available: proxy.Enabled,
	}

	return nil
}

// Remove 移除代理
func (m *ProxyManager) Remove(proxyID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.proxies[proxyID]; !exists {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("代理 %s 不存在", proxyID), nil)
	}

	delete(m.proxies, proxyID)
	delete(m.healthCheck, proxyID)
	return nil
}

// Get 获取代理
func (m *ProxyManager) Get(proxyID string) (*types.ProxyConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	proxy, exists := m.proxies[proxyID]
	if !exists {
		return nil, errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("代理 %s 不存在", proxyID), nil)
	}

	// 返回副本以避免外部修改
	proxyCopy := *proxy
	return &proxyCopy, nil
}

// GetAvailable 获取所有可用代理
func (m *ProxyManager) GetAvailable() []types.ProxyConfig {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var available []types.ProxyConfig
	for _, proxy := range m.proxies {
		if proxy.Enabled {
			if health, exists := m.healthCheck[proxy.ID]; exists && health.Available {
				available = append(available, *proxy)
			}
		}
	}

	return available
}

// GetNext 获取下一个可用代理（轮询）
func (m *ProxyManager) GetNext() (*types.ProxyConfig, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	available := m.getAvailableProxies()
	if len(available) == 0 {
		return nil, errors.NewProxyError(errors.ErrCodeProxyNotFound, "没有可用的代理", nil)
	}

	// 轮询选择
	proxy := available[m.currentIdx%len(available)]
	m.currentIdx++

	// 返回副本
	proxyCopy := *proxy
	return &proxyCopy, nil
}

// getAvailableProxies 内部方法：获取可用代理列表（不加锁）
func (m *ProxyManager) getAvailableProxies() []*types.ProxyConfig {
	var available []*types.ProxyConfig
	for _, proxy := range m.proxies {
		if proxy.Enabled {
			if health, exists := m.healthCheck[proxy.ID]; exists && health.Available {
				available = append(available, proxy)
			}
		}
	}
	return available
}

// HealthCheck 执行代理健康检查
func (m *ProxyManager) HealthCheck(proxyID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	proxy, exists := m.proxies[proxyID]
	if !exists {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("代理 %s 不存在", proxyID), nil)
	}

	health, exists := m.healthCheck[proxyID]
	if !exists {
		health = &ProxyHealth{}
		m.healthCheck[proxyID] = health
	}

	// 执行健康检查（这里简化实现）
	start := time.Now()
	available := m.checkProxyConnectivity(proxy)
	responseTime := time.Since(start)

	// 更新健康状态
	health.LastCheck = time.Now()
	health.ResponseTime = responseTime
	health.Available = available

	if !available {
		health.ErrorCount++
	} else {
		health.ErrorCount = 0 // 重置错误计数
	}

	return nil
}

// checkProxyConnectivity 检查代理连通性（简化实现）
func (m *ProxyManager) checkProxyConnectivity(proxy *types.ProxyConfig) bool {
	// 这里应该实现真正的连通性检查
	// 简化实现：假设代理总是可用的
	return proxy.Enabled
}

// UpdateStatus 更新代理状态
func (m *ProxyManager) UpdateStatus(proxyID string, enabled bool) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	proxy, exists := m.proxies[proxyID]
	if !exists {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, 
			fmt.Sprintf("代理 %s 不存在", proxyID), nil)
	}

	proxy.Enabled = enabled

	// 更新健康状态
	if health, exists := m.healthCheck[proxyID]; exists {
		health.Available = enabled
	}

	return nil
}

// Count 获取代理总数
func (m *ProxyManager) Count() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.proxies)
}

// Clear 清空代理池
func (m *ProxyManager) Clear() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.proxies = make(map[string]*types.ProxyConfig)
	m.healthCheck = make(map[string]*ProxyHealth)
	m.currentIdx = 0
	return nil
}

// GetProxyStatus 获取代理状态信息
func (m *ProxyManager) GetProxyStatus() []types.ProxyStatus {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var statuses []types.ProxyStatus
	for _, proxy := range m.proxies {
		status := types.ProxyStatus{
			ID:      proxy.ID,
			Host:    proxy.Host,
			Port:    proxy.Port,
			Enabled: proxy.Enabled,
		}

		if health, exists := m.healthCheck[proxy.ID]; exists {
			status.LastUsed = health.LastCheck
			status.ResponseTime = health.ResponseTime.Milliseconds()
			status.ErrorCount = health.ErrorCount
		}

		statuses = append(statuses, status)
	}

	return statuses
}

// validateProxy 验证代理配置
func (m *ProxyManager) validateProxy(proxy types.ProxyConfig) error {
	if proxy.Host == "" {
		return errors.NewValidationError(errors.ErrCodeValidationRequired, "代理主机不能为空", nil)
	}

	if proxy.Port <= 0 || proxy.Port > 65535 {
		return errors.NewValidationError(errors.ErrCodeValidationRange, "代理端口必须在1-65535范围内", nil)
	}

	if proxy.Type != types.ProxyTypeHTTP && proxy.Type != types.ProxyTypeSOCKS5 {
		return errors.NewValidationError(errors.ErrCodeValidationFormat, "不支持的代理类型", nil)
	}

	return nil
}

// GetStatistics 获取代理管理器统计信息
func (m *ProxyManager) GetStatistics() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]interface{})
	typeCount := make(map[string]int)
	
	enabledCount := 0
	availableCount := 0
	totalErrors := 0

	for _, proxy := range m.proxies {
		typeCount[proxy.Type.String()]++
		
		if proxy.Enabled {
			enabledCount++
		}

		if health, exists := m.healthCheck[proxy.ID]; exists {
			if health.Available {
				availableCount++
			}
			totalErrors += health.ErrorCount
		}
	}

	stats["total_proxies"] = len(m.proxies)
	stats["enabled_proxies"] = enabledCount
	stats["available_proxies"] = availableCount
	stats["type_distribution"] = typeCount
	stats["total_errors"] = totalErrors

	return stats
}

// BatchHealthCheck 批量健康检查
func (m *ProxyManager) BatchHealthCheck() error {
	m.mutex.RLock()
	proxyIDs := make([]string, 0, len(m.proxies))
	for id := range m.proxies {
		proxyIDs = append(proxyIDs, id)
	}
	m.mutex.RUnlock()

	// 并发执行健康检查
	for _, proxyID := range proxyIDs {
		go func(id string) {
			m.HealthCheck(id)
		}(proxyID)
	}

	return nil
}
